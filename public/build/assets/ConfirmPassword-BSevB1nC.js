import{u as l,c as n,o as d,w as t,a as e,b as r,d as o,h as u,e as p,n as f,f as c}from"./app-WH8gGbsu.js";import{_}from"./GuestLayout-Bm1lJpQi.js";import{_ as w,a as b,b as x}from"./TextInput-CMW8PQHW.js";import{P as g}from"./PrimaryButton-hn30oliD.js";import"./ApplicationLogo-DepmmPQx.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";const y={class:"mt-4 flex justify-end"},N={__name:"ConfirmPassword",setup(P){const s=l({password:""}),i=()=>{s.post(route("password.confirm"),{onFinish:()=>s.reset()})};return(V,a)=>(d(),n(_,null,{default:t(()=>[e(o(u),{title:"Confirm Password"}),a[2]||(a[2]=r("div",{class:"mb-4 text-sm text-gray-600"}," This is a secure area of the application. Please confirm your password before continuing. ",-1)),r("form",{onSubmit:p(i,["prevent"])},[r("div",null,[e(w,{for:"password",value:"Password"}),e(b,{id:"password",type:"password",class:"mt-1 block w-full",modelValue:o(s).password,"onUpdate:modelValue":a[0]||(a[0]=m=>o(s).password=m),required:"",autocomplete:"current-password",autofocus:""},null,8,["modelValue"]),e(x,{class:"mt-2",message:o(s).errors.password},null,8,["message"])]),r("div",y,[e(g,{class:f(["ms-4",{"opacity-25":o(s).processing}]),disabled:o(s).processing},{default:t(()=>a[1]||(a[1]=[c(" Confirm ")])),_:1,__:[1]},8,["class","disabled"])])],32)]),_:1,__:[2]}))}};export{N as default};
