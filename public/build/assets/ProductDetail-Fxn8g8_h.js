import{m,j as D,z as ot,c as at,o as r,w as T,b as t,g as l,i as x,a as I,d as N,l as U,f as h,t as a,F as b,y as f,n as y,k as rt,s as lt,x as nt}from"./app-kiRtcQ7Q.js";import{_ as dt}from"./AppLayout-BN-OM8pM.js";const it={class:"bg-muted py-4"},ut={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},ct={class:"flex items-center space-x-2 text-sm"},mt={class:"text-foreground font-medium"},gt={class:"py-12 bg-background"},pt={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},bt={class:"grid lg:grid-cols-2 gap-12"},ft={class:"space-y-4"},vt={class:"aspect-square bg-muted rounded-lg overflow-hidden"},xt=["src","alt"],ht={key:0,class:"grid grid-cols-4 gap-2"},yt=["onClick"],kt=["src","alt"],wt={class:"space-y-6"},_t={class:"space-y-4"},Dt={class:"flex items-center gap-2"},Mt={class:"text-sm text-muted-foreground"},St={class:"text-3xl font-bold text-foreground"},jt={class:"flex items-center gap-4"},Ct={class:"flex items-center"},Tt={class:"text-sm text-muted-foreground"},Lt={class:"space-y-2"},Bt={class:"flex items-baseline gap-2"},Pt={class:"text-3xl font-bold text-primary"},Rt={class:"text-sm text-muted-foreground"},zt={class:"card"},At={class:"card-content"},It={class:"text-3xl font-bold text-primary mb-2"},Nt={class:"text-muted-foreground mb-4"},Ut={class:"space-y-4"},$t={class:"border rounded-lg p-4"},Ft={class:"flex items-center justify-between mb-4"},Gt={class:"text-lg font-semibold"},Kt={class:"grid grid-cols-7 gap-1 mb-2"},Ht={class:"grid grid-cols-7 gap-1"},Vt=["onClick","disabled"],Yt={key:0,class:"bg-muted p-4 rounded-lg"},Jt={class:"space-y-1 text-sm"},qt={class:"flex justify-between"},Et={class:"flex justify-between"},Wt={class:"border-t pt-1 flex justify-between font-medium"},Ot=["disabled"],Xt={key:2,disabled:"",class:"btn-outline w-full text-lg py-6 opacity-50 cursor-not-allowed"},Zt={class:"space-y-3"},Qt=["disabled"],te={key:1,disabled:"",class:"btn-outline w-full text-lg py-3 opacity-50 cursor-not-allowed"},ee={class:"py-12 bg-background"},se={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},oe={class:"border-b border-border mb-6"},ae={class:"flex space-x-8"},re=["onClick"],le={class:"tab-content"},ne={key:0,class:"card"},de={class:"card-content space-y-4"},ie={class:"text-muted-foreground leading-relaxed"},ue={class:"grid md:grid-cols-2 gap-6 pt-4"},ce={class:"space-y-2"},me={class:"flex justify-between"},ge={class:"font-medium"},pe={class:"flex justify-between"},be={class:"font-medium"},fe={class:"flex justify-between"},ve={class:"font-medium"},xe={class:"flex justify-between"},he={class:"font-medium capitalize"},ye={key:0,class:"flex justify-between"},ke={class:"font-medium"},we={key:1,class:"flex justify-between"},_e={class:"font-medium"},De={class:"space-y-2"},Me={class:"flex justify-between"},Se={class:"font-medium"},je={class:"flex justify-between"},Ce={class:"font-medium"},Te={class:"flex justify-between"},Le={class:"font-medium"},Be={class:"flex justify-between"},Pe={class:"font-medium"},Re={key:1,class:"card"},ze={key:2,class:"card"},Ae={class:"card-content"},Ie={class:"space-y-6"},Ne={class:"flex items-center justify-between mb-2"},Ue={class:"font-medium text-foreground"},$e={class:"text-sm text-muted-foreground"},Fe={class:"flex items-center mb-2"},Ge={class:"text-muted-foreground"},Ke={key:0,class:"py-12 bg-background"},He={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},Ve={class:"grid md:grid-cols-2 lg:grid-cols-4 gap-6"},Ye={class:"relative overflow-hidden rounded-t-lg"},Je=["src","alt"],qe={class:"card-content"},Ee={class:"font-semibold text-foreground mb-2 line-clamp-2"},We={class:"flex items-center mb-3"},Oe={class:"flex items-center"},Xe={class:"text-sm text-muted-foreground ml-2"},Ze={class:"flex items-center justify-between"},Qe={class:"text-lg font-bold text-primary"},os={__name:"ProductDetail",props:{product:Object,relatedProducts:Array},setup(o){const p=o,M=m(""),g=m(null),v=m(null),c=m(new Date);m(!0);const k=m(1),w=m("description"),L=m([]),$=m(!1),Y=m([{id:"description",label:"Deskripsi"},{id:"size-guide",label:"Panduan Ukuran"},{id:"reviews",label:"Ulasan"}]),J=m([{name:"Siti Aminah",rating:5,date:"15 Februari 2024",comment:"Gaun sangat cantik dan berkualitas. Sesuai dengan foto dan deskripsi. Pelayanan juga sangat baik!"},{name:"Fatimah Zahra",rating:5,date:"10 Februari 2024",comment:"Alhamdulillah gaun untuk akad nikah saya sangat sempurna. Terima kasih GaunSyariJogja!"},{name:"Khadijah Rahman",rating:4,date:"5 Februari 2024",comment:"Gaun bagus, hanya saja pengiriman agak terlambat. Tapi overall puas dengan kualitasnya."}]),q=D(()=>c.value.toLocaleDateString("id-ID",{month:"long",year:"numeric"})),E=D(()=>{const n=c.value.getFullYear(),e=c.value.getMonth(),s=new Date(n,e,1),j=new Date(n,e+1,0).getDate(),Q=s.getDay(),tt=new Date(n,e,0).getDate(),_=[];for(let d=Q-1;d>=0;d--){const i=tt-d,z=new Date(n,e-1,i);_.push({day:i,date:z,isCurrentMonth:!1,isPast:!1,isAvailable:!1,isSelected:!1,isInRange:!1,key:`prev-${i}`})}const G=new Date;G.setHours(0,0,0,0);for(let d=1;d<=j;d++){const i=new Date(n,e,d),z=i.toISOString().split("T")[0],K=i<G,st=!L.value.includes(z)&&!K;let A=!1,H=!1;if(g.value&&v.value){const C=new Date(g.value),V=new Date(v.value);A=i.getTime()===C.getTime()||i.getTime()===V.getTime(),H=i>C&&i<V}else if(g.value){const C=new Date(g.value);A=i.getTime()===C.getTime()}_.push({day:d,date:i,isCurrentMonth:!0,isPast:K,isAvailable:st,isSelected:A,isInRange:H,key:`current-${d}`})}const et=42-_.length;for(let d=1;d<=et;d++){const i=new Date(n,e+1,d);_.push({day:d,date:i,isCurrentMonth:!1,isPast:!1,isAvailable:!1,isSelected:!1,isInRange:!1,key:`next-${d}`})}return _}),S=D(()=>{if(!g.value)return k.value;if(v.value){const n=new Date(g.value),e=new Date(v.value),s=Math.abs(e-n),u=Math.ceil(s/(1e3*60*60*24));return u>0?u:k.value}return k.value}),B=D(()=>S.value*p.product.price),P=D(()=>g.value&&v.value&&S.value>0),R=async()=>{$.value=!0;try{const n=c.value.getFullYear(),e=c.value.getMonth()+1,u=await(await fetch(`/api/product/${p.product.sku}/availability?year=${n}&month=${e}`)).json();L.value=u.unavailable_dates||[]}catch(n){console.error("Error loading availability:",n),L.value=[]}finally{$.value=!1}},W=()=>{c.value=new Date(c.value.getFullYear(),c.value.getMonth()-1,1),R()},O=()=>{c.value=new Date(c.value.getFullYear(),c.value.getMonth()+1,1),R()},X=n=>{if(!n.isCurrentMonth||n.isPast||!n.isAvailable)return;g.value=n.date;const e=new Date(n.date);e.setDate(e.getDate()+k.value-1),v.value=e},Z=()=>{navigator.share?navigator.share({title:p.product.name,text:`Lihat gaun syar'i ${p.product.name} di Gaun Syari Jogja`,url:window.location.href}):(navigator.clipboard.writeText(window.location.href),alert("Link produk telah disalin ke clipboard!"))},F=()=>{if(!P.value)return;const n={product_id:p.product.id,product_sku:p.product.sku,rental_start_date:g.value.toISOString().split("T")[0],rental_end_date:v.value.toISOString().split("T")[0],rental_days:S.value,unit_price:p.product.price,total_price:B.value,deposit_amount:p.product.deposit_amount};nt.post("/booking",n)};return ot(()=>{var n;M.value=((n=p.product.images[0])==null?void 0:n.image_path)||"/placeholder.jpg",R()}),(n,e)=>(r(),at(dt,null,{default:T(()=>[t("section",it,[t("div",ut,[t("nav",ct,[I(N(U),{href:"/",class:"text-muted-foreground hover:text-foreground"},{default:T(()=>e[1]||(e[1]=[h("Beranda")])),_:1,__:[1]}),e[3]||(e[3]=t("span",{class:"text-muted-foreground"},"/",-1)),I(N(U),{href:"/catalog",class:"text-muted-foreground hover:text-foreground"},{default:T(()=>e[2]||(e[2]=[h("Katalog")])),_:1,__:[2]}),e[4]||(e[4]=t("span",{class:"text-muted-foreground"},"/",-1)),t("span",mt,a(o.product.name),1)])])]),t("section",gt,[t("div",pt,[t("div",bt,[t("div",ft,[t("div",vt,[t("img",{src:M.value,alt:o.product.name,class:"w-full h-full object-cover"},null,8,xt)]),o.product.images.length>1?(r(),l("div",ht,[(r(!0),l(b,null,f(o.product.images,s=>(r(),l("button",{key:s.id,onClick:u=>M.value=s.image_path,class:y(["aspect-square bg-muted rounded-lg overflow-hidden border-2 transition-colors",M.value===s.image_path?"border-primary":"border-transparent hover:border-muted-foreground"])},[t("img",{src:s.image_path,alt:s.alt_text,class:"w-full h-full object-cover"},null,8,kt)],10,yt))),128))])):x("",!0)]),t("div",wt,[t("div",_t,[t("div",Dt,[t("span",Mt,"SKU: "+a(o.product.sku),1),t("span",{class:y(["px-2 py-1 rounded-full text-xs font-medium",o.product.is_available?"bg-green-100 text-green-800":"bg-red-100 text-red-800"])},a(o.product.is_available?"Tersedia":"Tidak Tersedia"),3)]),t("h1",St,a(o.product.name),1),t("div",jt,[t("div",Ct,[(r(),l(b,null,f(5,s=>t("span",{key:s,class:"text-yellow-400"},[(r(),l("svg",{class:y(["h-5 w-5",s<=Math.floor(o.product.rating)?"fill-current":"text-gray-300"]),fill:"currentColor",viewBox:"0 0 20 20"},e[5]||(e[5]=[t("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"},null,-1)]),2))])),64))]),t("span",Tt,"("+a(o.product.rating)+" dari "+a(o.product.rating_count)+" ulasan)",1)]),t("div",Lt,[t("div",Bt,[t("span",Pt,"Rp "+a(Number(o.product.price).toLocaleString()),1),e[6]||(e[6]=t("span",{class:"text-muted-foreground"},"/hari",-1))]),t("p",Rt," Deposit: Rp "+a(Number(o.product.deposit_amount).toLocaleString())+" ("+a(o.product.deposit_percentage)+"%) ",1)])]),t("div",zt,[t("div",At,[t("div",It,[h(" Rp "+a(Number(o.product.price).toLocaleString())+" ",1),e[7]||(e[7]=t("span",{class:"text-lg text-muted-foreground font-normal"},"/hari",-1))]),t("p",Nt,"DP: Rp "+a(Number(o.product.deposit_amount).toLocaleString()),1),t("div",Ut,[t("div",null,[e[9]||(e[9]=t("label",{class:"block text-sm font-medium text-foreground mb-2"},"Durasi Sewa",-1)),rt(t("select",{"onUpdate:modelValue":e[0]||(e[0]=s=>k.value=s),class:"input-field"},e[8]||(e[8]=[t("option",{value:"1"},"1 hari",-1),t("option",{value:"2"},"2 hari",-1),t("option",{value:"3"},"3 hari",-1),t("option",{value:"4"},"4 hari",-1),t("option",{value:"5"},"5 hari",-1)]),512),[[lt,k.value]])]),t("div",null,[e[13]||(e[13]=t("label",{class:"block text-sm font-medium text-foreground mb-2"},"Pilih Tanggal Sewa",-1)),t("div",$t,[t("div",Ft,[t("button",{onClick:W,class:"p-2 hover:bg-accent rounded-md"},e[10]||(e[10]=[t("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 19l-7-7 7-7"})],-1)])),t("h3",Gt,a(q.value),1),t("button",{onClick:O,class:"p-2 hover:bg-accent rounded-md"},e[11]||(e[11]=[t("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5l7 7-7 7"})],-1)]))]),t("div",Kt,[(r(),l(b,null,f(["Min","Sen","Sel","Rab","Kam","Jum","Sab"],s=>t("div",{key:s,class:"text-center text-sm font-medium text-muted-foreground py-2"},a(s),1)),64))]),t("div",Ht,[(r(!0),l(b,null,f(E.value,s=>(r(),l("button",{key:s.key,onClick:u=>X(s),disabled:!s.isCurrentMonth||s.isPast||!s.isAvailable,class:y(["aspect-square text-sm rounded-md transition-colors relative",s.isCurrentMonth?s.isPast?"text-muted-foreground/50 cursor-not-allowed":s.isAvailable?s.isSelected?"bg-primary text-primary-foreground":s.isInRange?"bg-primary/20 text-primary":"hover:bg-accent":"bg-red-100 text-red-800 cursor-not-allowed":"text-muted-foreground/50 cursor-not-allowed"])},a(s.day),11,Vt))),128))]),e[12]||(e[12]=t("div",{class:"flex items-center gap-4 mt-4 text-sm"},[t("div",{class:"flex items-center gap-2"},[t("div",{class:"w-3 h-3 bg-green-500 rounded"}),t("span",null,"Tersedia")]),t("div",{class:"flex items-center gap-2"},[t("div",{class:"w-3 h-3 bg-red-500 rounded"}),t("span",null,"Tidak tersedia")])],-1))])]),g.value&&v.value?(r(),l("div",Yt,[e[16]||(e[16]=t("h4",{class:"font-medium text-foreground mb-2"},"Ringkasan Biaya",-1)),t("div",Jt,[t("div",qt,[t("span",null,"Harga sewa ("+a(S.value)+" hari)",1),t("span",null,"Rp "+a(B.value.toLocaleString()),1)]),t("div",Et,[e[14]||(e[14]=t("span",null,"DP (Uang Muka)",-1)),t("span",null,"Rp "+a(Number(o.product.deposit_amount).toLocaleString()),1)]),t("div",Wt,[e[15]||(e[15]=t("span",null,"Total Pembayaran Awal",-1)),t("span",null,"Rp "+a((B.value+Number(o.product.deposit_amount)).toLocaleString()),1)])])])):x("",!0),o.product.is_available?(r(),l("button",{key:1,onClick:F,disabled:!P.value,class:"btn-primary w-full text-lg py-6 disabled:opacity-50 disabled:cursor-not-allowed"}," Pesan Sekarang ",8,Ot)):(r(),l("button",Xt," Tidak Tersedia "))])])]),e[20]||(e[20]=t("div",{class:"grid grid-cols-3 gap-4"},[t("div",{class:"text-center p-4 card"},[t("div",{class:"bg-primary/10 p-2 rounded-lg w-fit mx-auto mb-2"},[t("svg",{class:"w-8 h-8 text-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})])]),t("p",{class:"text-sm font-medium"},"Garansi Kualitas")]),t("div",{class:"text-center p-4 card"},[t("div",{class:"bg-primary/10 p-2 rounded-lg w-fit mx-auto mb-2"},[t("svg",{class:"w-8 h-8 text-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"})])]),t("p",{class:"text-sm font-medium"},"Antar Jemput")]),t("div",{class:"text-center p-4 card"},[t("div",{class:"bg-primary/10 p-2 rounded-lg w-fit mx-auto mb-2"},[t("svg",{class:"w-8 h-8 text-primary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])]),t("p",{class:"text-sm font-medium"},"Konsultasi Gratis")])],-1)),t("div",Zt,[o.product.is_available?(r(),l("button",{key:0,onClick:F,disabled:!P.value,class:"btn-primary w-full text-lg py-3 disabled:opacity-50 disabled:cursor-not-allowed"},e[17]||(e[17]=[t("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"})],-1),h(" Booking Sekarang ")]),8,Qt)):(r(),l("button",te," Tidak Tersedia ")),t("div",{class:"flex gap-3"},[e[19]||(e[19]=t("button",{class:"btn-outline flex-1 flex items-center justify-center"},[t("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})]),h(" Favorit ")],-1)),t("button",{onClick:Z,class:"btn-outline flex-1 flex items-center justify-center"},e[18]||(e[18]=[t("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"})],-1),h(" Bagikan ")]))])])])])])]),t("section",ee,[t("div",se,[t("div",oe,[t("nav",ae,[(r(!0),l(b,null,f(Y.value,s=>(r(),l("button",{key:s.id,onClick:u=>w.value=s.id,class:y(["py-2 px-1 border-b-2 font-medium text-sm transition-colors",w.value===s.id?"border-primary text-primary":"border-transparent text-muted-foreground hover:text-foreground hover:border-muted-foreground"])},a(s.label),11,re))),128))])]),t("div",le,[w.value==="description"?(r(),l("div",ne,[e[33]||(e[33]=t("div",{class:"card-header"},[t("h2",{class:"text-2xl font-bold"},"Deskripsi Produk")],-1)),t("div",de,[t("p",ie,a(o.product.description),1),t("div",ue,[t("div",null,[e[27]||(e[27]=t("h3",{class:"font-semibold mb-3"},"Detail Produk",-1)),t("dl",ce,[t("div",me,[e[21]||(e[21]=t("dt",{class:"text-muted-foreground"},"Kategori:",-1)),t("dd",ge,a(o.product.category),1)]),t("div",pe,[e[22]||(e[22]=t("dt",{class:"text-muted-foreground"},"Warna Utama:",-1)),t("dd",be,a(o.product.color),1)]),t("div",fe,[e[23]||(e[23]=t("dt",{class:"text-muted-foreground"},"Motif:",-1)),t("dd",ve,a(o.product.motif),1)]),t("div",xe,[e[24]||(e[24]=t("dt",{class:"text-muted-foreground"},"Kondisi:",-1)),t("dd",he,a(o.product.condition),1)]),o.product.sizes&&o.product.sizes.length>0?(r(),l("div",ye,[e[25]||(e[25]=t("dt",{class:"text-muted-foreground"},"Ukuran Tersedia:",-1)),t("dd",ke,a(o.product.sizes.join(", ")),1)])):x("",!0),o.product.colors&&o.product.colors.length>0?(r(),l("div",we,[e[26]||(e[26]=t("dt",{class:"text-muted-foreground"},"Pilihan Warna:",-1)),t("dd",_e,a(o.product.colors.join(", ")),1)])):x("",!0)])]),t("div",null,[e[32]||(e[32]=t("h3",{class:"font-semibold mb-3"},"Informasi Sewa",-1)),t("dl",De,[t("div",Me,[e[28]||(e[28]=t("dt",{class:"text-muted-foreground"},"Harga per Hari:",-1)),t("dd",Se,"Rp "+a(Number(o.product.price).toLocaleString()),1)]),t("div",je,[e[29]||(e[29]=t("dt",{class:"text-muted-foreground"},"Deposit:",-1)),t("dd",Ce,a(o.product.deposit_percentage)+"%",1)]),t("div",Te,[e[30]||(e[30]=t("dt",{class:"text-muted-foreground"},"Rating:",-1)),t("dd",Le,a(o.product.rating)+"/5 ("+a(o.product.rating_count)+" ulasan)",1)]),t("div",Be,[e[31]||(e[31]=t("dt",{class:"text-muted-foreground"},"Total Disewa:",-1)),t("dd",Pe,a(o.product.total_rentals||0)+" kali",1)])])])])])])):x("",!0),w.value==="size-guide"?(r(),l("div",Re,e[34]||(e[34]=[t("div",{class:"card-header"},[t("h2",{class:"text-2xl font-bold"},"Panduan Ukuran")],-1),t("div",{class:"card-content"},[t("div",{class:"overflow-x-auto"},[t("table",{class:"w-full border-collapse border border-border"},[t("thead",null,[t("tr",{class:"bg-muted"},[t("th",{class:"border border-border px-4 py-2 text-left"},"Ukuran"),t("th",{class:"border border-border px-4 py-2 text-left"},"Lingkar Dada (cm)"),t("th",{class:"border border-border px-4 py-2 text-left"},"Lingkar Pinggang (cm)"),t("th",{class:"border border-border px-4 py-2 text-left"},"Panjang Gaun (cm)")])]),t("tbody",null,[t("tr",null,[t("td",{class:"border border-border px-4 py-2 font-medium"},"S"),t("td",{class:"border border-border px-4 py-2"},"86-90"),t("td",{class:"border border-border px-4 py-2"},"66-70"),t("td",{class:"border border-border px-4 py-2"},"140")]),t("tr",null,[t("td",{class:"border border-border px-4 py-2 font-medium"},"M"),t("td",{class:"border border-border px-4 py-2"},"90-94"),t("td",{class:"border border-border px-4 py-2"},"70-74"),t("td",{class:"border border-border px-4 py-2"},"142")]),t("tr",null,[t("td",{class:"border border-border px-4 py-2 font-medium"},"L"),t("td",{class:"border border-border px-4 py-2"},"94-98"),t("td",{class:"border border-border px-4 py-2"},"74-78"),t("td",{class:"border border-border px-4 py-2"},"144")]),t("tr",null,[t("td",{class:"border border-border px-4 py-2 font-medium"},"XL"),t("td",{class:"border border-border px-4 py-2"},"98-102"),t("td",{class:"border border-border px-4 py-2"},"78-82"),t("td",{class:"border border-border px-4 py-2"},"146")])])])]),t("p",{class:"text-sm text-muted-foreground mt-4"}," * Ukuran dapat bervariasi ±2cm. Untuk konsultasi ukuran yang lebih akurat, silakan hubungi customer service kami. ")],-1)]))):x("",!0),w.value==="reviews"?(r(),l("div",ze,[e[36]||(e[36]=t("div",{class:"card-header"},[t("h2",{class:"text-2xl font-bold"},"Ulasan Pelanggan")],-1)),t("div",Ae,[t("div",Ie,[(r(!0),l(b,null,f(J.value,(s,u)=>(r(),l("div",{key:u,class:"border-b border-border pb-4 last:border-b-0"},[t("div",Ne,[t("h5",Ue,a(s.name),1),t("span",$e,a(s.date),1)]),t("div",Fe,[(r(),l(b,null,f(5,j=>t("span",{key:j,class:"text-yellow-400"},[(r(),l("svg",{class:y(["h-4 w-4",j<=s.rating?"fill-current":"text-gray-300"]),fill:"currentColor",viewBox:"0 0 20 20"},e[35]||(e[35]=[t("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"},null,-1)]),2))])),64))]),t("p",Ge,a(s.comment),1)]))),128))])])])):x("",!0)])])]),o.relatedProducts.length>0?(r(),l("section",Ke,[t("div",He,[e[40]||(e[40]=t("div",{class:"text-center mb-8"},[t("h2",{class:"text-3xl font-bold text-foreground mb-4"},"Produk Serupa"),t("p",{class:"text-muted-foreground"},"Gaun lain yang mungkin Anda sukai")],-1)),t("div",Ve,[(r(!0),l(b,null,f(o.relatedProducts,s=>(r(),l("div",{key:s.id,class:"card group hover:card-shadow-lg transition-all duration-300 animate-fade-in"},[t("div",Ye,[t("img",{src:s.image,alt:s.name,class:"w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"},null,8,Je)]),t("div",qe,[t("h3",Ee,a(s.name),1),t("div",We,[t("div",Oe,[(r(),l(b,null,f(5,u=>t("span",{key:u,class:"text-yellow-400"},[(r(),l("svg",{class:y(["h-4 w-4",u<=Math.floor(s.rating)?"fill-current":"text-gray-300"]),fill:"currentColor",viewBox:"0 0 20 20"},e[37]||(e[37]=[t("path",{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"},null,-1)]),2))])),64))]),t("span",Xe,"("+a(s.rating)+")",1)]),t("div",Ze,[t("div",null,[t("span",Qe,"Rp "+a(Number(s.price).toLocaleString()),1),e[38]||(e[38]=t("span",{class:"text-sm text-muted-foreground"},"/hari",-1))])]),I(N(U),{href:`/product/${s.sku}`,class:"btn-outline w-full mt-3 inline-flex items-center justify-center"},{default:T(()=>e[39]||(e[39]=[h(" Lihat Detail ")])),_:2,__:[39]},1032,["href"])])]))),128))])])])):x("",!0)]),_:1}))}};export{os as default};
