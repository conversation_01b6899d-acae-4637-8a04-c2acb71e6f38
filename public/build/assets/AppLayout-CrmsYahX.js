import{m as i,g as u,o as b,b as a,k as m,a as s,w as e,f as o,d as r,l as n,y as x,r as f}from"./app-s15Tu9FY.js";const g={class:"min-h-screen bg-brand-bg"},k={class:"bg-white shadow-sm border-b border-brand-light"},p={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},_={class:"flex justify-between items-center h-16"},v={class:"flex items-center"},h={class:"hidden md:flex space-x-8"},c={class:"md:hidden"},y={class:"md:hidden py-4 border-t border-brand-light"},w={class:"flex flex-col space-y-2"},B={class:"bg-brand-dark text-white"},j={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12"},S={class:"grid grid-cols-1 md:grid-cols-4 gap-8"},A={class:"space-y-2 text-brand-light"},G={__name:"AppLayout",setup(K){const l=i(!1);return(d,t)=>(b(),u("div",g,[a("header",k,[a("div",p,[a("div",_,[a("div",v,[s(r(n),{href:"/",class:"text-2xl font-bold text-brand-dark"},{default:e(()=>t[1]||(t[1]=[o(" Gaun Syari Jogja ")])),_:1,__:[1]})]),a("nav",h,[s(r(n),{href:"/",class:"text-brand-dark hover:text-brand-medium transition-colors"},{default:e(()=>t[2]||(t[2]=[o(" Beranda ")])),_:1,__:[2]}),s(r(n),{href:"/catalog",class:"text-brand-dark hover:text-brand-medium transition-colors"},{default:e(()=>t[3]||(t[3]=[o(" Katalog ")])),_:1,__:[3]}),s(r(n),{href:"/booking",class:"text-brand-dark hover:text-brand-medium transition-colors"},{default:e(()=>t[4]||(t[4]=[o(" Booking ")])),_:1,__:[4]}),s(r(n),{href:"/order-tracking",class:"text-brand-dark hover:text-brand-medium transition-colors"},{default:e(()=>t[5]||(t[5]=[o(" Lacak Pesanan ")])),_:1,__:[5]})]),a("div",c,[a("button",{onClick:t[0]||(t[0]=L=>l.value=!l.value),class:"text-brand-dark"},t[6]||(t[6]=[a("svg",{class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[a("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"})],-1)]))])]),m(a("div",y,[a("div",w,[s(r(n),{href:"/",class:"text-brand-dark hover:text-brand-medium transition-colors py-2"},{default:e(()=>t[7]||(t[7]=[o(" Beranda ")])),_:1,__:[7]}),s(r(n),{href:"/catalog",class:"text-brand-dark hover:text-brand-medium transition-colors py-2"},{default:e(()=>t[8]||(t[8]=[o(" Katalog ")])),_:1,__:[8]}),s(r(n),{href:"/booking",class:"text-brand-dark hover:text-brand-medium transition-colors py-2"},{default:e(()=>t[9]||(t[9]=[o(" Booking ")])),_:1,__:[9]}),s(r(n),{href:"/order-tracking",class:"text-brand-dark hover:text-brand-medium transition-colors py-2"},{default:e(()=>t[10]||(t[10]=[o(" Lacak Pesanan ")])),_:1,__:[10]})])],512),[[x,l.value]])])]),a("main",null,[f(d.$slots,"default")]),a("footer",B,[a("div",j,[a("div",S,[t[15]||(t[15]=a("div",{class:"col-span-1 md:col-span-2"},[a("h3",{class:"text-xl font-bold mb-4"},"Gaun Syari Jogja"),a("p",{class:"text-brand-light mb-4"}," Sewa gaun syar'i berkualitas premium untuk acara spesial Anda. Koleksi terlengkap dengan desain elegan dan modern di Yogyakarta. ")],-1)),a("div",null,[t[14]||(t[14]=a("h4",{class:"font-semibold mb-4"},"Layanan",-1)),a("ul",A,[a("li",null,[s(r(n),{href:"/catalog",class:"hover:text-white transition-colors"},{default:e(()=>t[11]||(t[11]=[o("Katalog Gaun")])),_:1,__:[11]})]),a("li",null,[s(r(n),{href:"/booking",class:"hover:text-white transition-colors"},{default:e(()=>t[12]||(t[12]=[o("Booking Online")])),_:1,__:[12]})]),a("li",null,[s(r(n),{href:"/order-tracking",class:"hover:text-white transition-colors"},{default:e(()=>t[13]||(t[13]=[o("Lacak Pesanan")])),_:1,__:[13]})])])]),t[16]||(t[16]=a("div",null,[a("h4",{class:"font-semibold mb-4"},"Kontak"),a("ul",{class:"space-y-2 text-brand-light"},[a("li",null,"WhatsApp: +62 812-3456-7890"),a("li",null,"Email: <EMAIL>"),a("li",null,"Alamat: Jl. Malioboro No. 123, Yogyakarta")])],-1))]),t[17]||(t[17]=a("div",{class:"border-t border-brand-medium mt-8 pt-8 text-center text-brand-light"},[a("p",null,"© 2025 Gaun Syari Jogja. All rights reserved.")],-1))])])]))}};export{G as _};
