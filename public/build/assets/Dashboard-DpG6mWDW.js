import{_ as o}from"./AuthenticatedLayout-x0KPi7aV.js";import{g as r,o as l,a as s,d,h as i,w as t,b as a,F as m}from"./app-s15Tu9FY.js";import"./ApplicationLogo-v2Vu8Kka.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";const f={__name:"Dashboard",setup(n){return(p,e)=>(l(),r(m,null,[s(d(i),{title:"Dashboard"}),s(o,null,{header:t(()=>e[0]||(e[0]=[a("h2",{class:"text-xl font-semibold leading-tight text-gray-800"}," Dashboard ",-1)])),default:t(()=>[e[1]||(e[1]=a("div",{class:"py-12"},[a("div",{class:"mx-auto max-w-7xl sm:px-6 lg:px-8"},[a("div",{class:"overflow-hidden bg-white shadow-sm sm:rounded-lg"},[a("div",{class:"p-6 text-gray-900"}," You're logged in! ")])])],-1))]),_:1,__:[1]})],64))}};export{f as default};
