import{A as a}from"./ApplicationLogo-C7vKEG5W.js";import{g as o,o as r,b as e,a as s,w as l,d as n,l as c,r as d}from"./app-7Re41hnF.js";const i={class:"flex min-h-screen flex-col items-center bg-gray-100 pt-6 sm:justify-center sm:pt-0"},m={class:"mt-6 w-full overflow-hidden bg-white px-6 py-4 shadow-md sm:max-w-md sm:rounded-lg"},h={__name:"GuestLayout",setup(f){return(t,u)=>(r(),o("div",i,[e("div",null,[s(n(c),{href:"/"},{default:l(()=>[s(a,{class:"h-20 w-20 fill-current text-gray-500"})]),_:1})]),e("div",m,[d(t.$slots,"default")])]))}};export{h as _};
