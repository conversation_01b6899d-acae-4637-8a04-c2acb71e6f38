import{m,g as n,o as l,b as e,k as b,a as s,w as a,f as o,d as r,l as d,A as f,r as x}from"./app-7Re41hnF.js";const g={class:"min-h-screen bg-brand-bg"},p={class:"bg-white shadow-sm border-b border-brand-light"},h={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},_={class:"flex justify-between items-center h-16"},k={class:"flex items-center"},v={class:"hidden md:flex items-center space-x-8"},c={key:0,class:"flex items-center space-x-4"},y={key:1,class:"flex items-center space-x-4"},w={class:"md:hidden"},L={class:"md:hidden py-4 border-t border-brand-light"},j={class:"flex flex-col space-y-2"},B={class:"border-t border-brand-light pt-4 mt-4"},A={key:0,class:"flex flex-col space-y-2"},S={key:1,class:"flex flex-col space-y-2"},D={class:"bg-brand-dark text-white"},K={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12"},M={class:"grid grid-cols-1 md:grid-cols-4 gap-8"},G={class:"space-y-2 text-brand-light"},C={__name:"AppLayout",setup(J){const i=m(!1);return(u,t)=>(l(),n("div",g,[e("header",p,[e("div",h,[e("div",_,[e("div",k,[s(r(d),{href:"/",class:"text-2xl font-bold text-brand-dark"},{default:a(()=>t[1]||(t[1]=[o(" Gaun Syari Jogja ")])),_:1,__:[1]})]),e("nav",v,[s(r(d),{href:"/",class:"text-brand-dark hover:text-brand-medium transition-colors font-medium"},{default:a(()=>t[2]||(t[2]=[o(" Beranda ")])),_:1,__:[2]}),s(r(d),{href:"/catalog",class:"text-brand-dark hover:text-brand-medium transition-colors font-medium"},{default:a(()=>t[3]||(t[3]=[o(" Katalog ")])),_:1,__:[3]}),s(r(d),{href:"/order-tracking",class:"text-brand-dark hover:text-brand-medium transition-colors font-medium"},{default:a(()=>t[4]||(t[4]=[o(" Lacak Pesanan ")])),_:1,__:[4]}),u.$page.props.auth.user?(l(),n("div",c,[s(r(d),{href:"/dashboard",class:"text-brand-dark hover:text-brand-medium transition-colors font-medium"},{default:a(()=>t[5]||(t[5]=[o(" Dashboard ")])),_:1,__:[5]}),s(r(d),{href:"/logout",method:"post",as:"button",class:"bg-brand-dark text-white px-4 py-2 rounded-lg hover:bg-brand-medium transition-colors"},{default:a(()=>t[6]||(t[6]=[o(" Logout ")])),_:1,__:[6]})])):(l(),n("div",y,[s(r(d),{href:"/login",class:"text-brand-dark hover:text-brand-medium transition-colors font-medium"},{default:a(()=>t[7]||(t[7]=[o(" Login ")])),_:1,__:[7]}),s(r(d),{href:"/register",class:"bg-brand-dark text-white px-4 py-2 rounded-lg hover:bg-brand-medium transition-colors"},{default:a(()=>t[8]||(t[8]=[o(" Daftar ")])),_:1,__:[8]})]))]),e("div",w,[e("button",{onClick:t[0]||(t[0]=N=>i.value=!i.value),class:"text-brand-dark"},t[9]||(t[9]=[e("svg",{class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"})],-1)]))])]),b(e("div",L,[e("div",j,[s(r(d),{href:"/",class:"text-brand-dark hover:text-brand-medium transition-colors py-2 font-medium"},{default:a(()=>t[10]||(t[10]=[o(" Beranda ")])),_:1,__:[10]}),s(r(d),{href:"/catalog",class:"text-brand-dark hover:text-brand-medium transition-colors py-2 font-medium"},{default:a(()=>t[11]||(t[11]=[o(" Katalog ")])),_:1,__:[11]}),s(r(d),{href:"/order-tracking",class:"text-brand-dark hover:text-brand-medium transition-colors py-2 font-medium"},{default:a(()=>t[12]||(t[12]=[o(" Lacak Pesanan ")])),_:1,__:[12]}),e("div",B,[u.$page.props.auth.user?(l(),n("div",A,[s(r(d),{href:"/dashboard",class:"text-brand-dark hover:text-brand-medium transition-colors py-2 font-medium"},{default:a(()=>t[13]||(t[13]=[o(" Dashboard ")])),_:1,__:[13]}),s(r(d),{href:"/logout",method:"post",as:"button",class:"bg-brand-dark text-white px-4 py-2 rounded-lg hover:bg-brand-medium transition-colors text-center"},{default:a(()=>t[14]||(t[14]=[o(" Logout ")])),_:1,__:[14]})])):(l(),n("div",S,[s(r(d),{href:"/login",class:"text-brand-dark hover:text-brand-medium transition-colors py-2 font-medium"},{default:a(()=>t[15]||(t[15]=[o(" Login ")])),_:1,__:[15]}),s(r(d),{href:"/register",class:"bg-brand-dark text-white px-4 py-2 rounded-lg hover:bg-brand-medium transition-colors text-center"},{default:a(()=>t[16]||(t[16]=[o(" Daftar ")])),_:1,__:[16]})]))])])],512),[[f,i.value]])])]),e("main",null,[x(u.$slots,"default")]),e("footer",D,[e("div",K,[e("div",M,[t[21]||(t[21]=e("div",{class:"col-span-1 md:col-span-2"},[e("h3",{class:"text-xl font-bold mb-4"},"Gaun Syari Jogja"),e("p",{class:"text-brand-light mb-4"}," Sewa gaun syar'i berkualitas premium untuk acara spesial Anda. Koleksi terlengkap dengan desain elegan dan modern di Yogyakarta. ")],-1)),e("div",null,[t[20]||(t[20]=e("h4",{class:"font-semibold mb-4"},"Layanan",-1)),e("ul",G,[e("li",null,[s(r(d),{href:"/catalog",class:"hover:text-white transition-colors"},{default:a(()=>t[17]||(t[17]=[o("Katalog Gaun")])),_:1,__:[17]})]),e("li",null,[s(r(d),{href:"/booking",class:"hover:text-white transition-colors"},{default:a(()=>t[18]||(t[18]=[o("Booking Online")])),_:1,__:[18]})]),e("li",null,[s(r(d),{href:"/order-tracking",class:"hover:text-white transition-colors"},{default:a(()=>t[19]||(t[19]=[o("Lacak Pesanan")])),_:1,__:[19]})])])]),t[22]||(t[22]=e("div",null,[e("h4",{class:"font-semibold mb-4"},"Kontak"),e("ul",{class:"space-y-2 text-brand-light"},[e("li",null,"WhatsApp: +62 812-3456-7890"),e("li",null,"Email: <EMAIL>"),e("li",null,"Alamat: Jl. Malioboro No. 123, Yogyakarta")])],-1))]),t[23]||(t[23]=e("div",{class:"border-t border-brand-medium mt-8 pt-8 text-center text-brand-light"},[e("p",null,"© 2025 Gaun Syari Jogja. All rights reserved.")],-1))])])]))}};export{C as _};
