import{m as V,j as p,p as B,c as D,o as v,w as c,b as e,a as f,d as g,l as x,f as b,t as n,e as P,g as _,i as L,k as a,q as d,s as y,x as T}from"./app-7Re41hnF.js";import{_ as N}from"./AppLayout-DNOk7MlS.js";const j={class:"bg-muted py-4"},q={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},A={class:"flex items-center space-x-2 text-sm"},C={class:"py-12 bg-background"},X={class:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8"},R={class:"card"},K={class:"card-content"},z={class:"flex gap-4"},E=["src","alt"],$={class:"flex-1"},H={class:"font-semibold text-lg"},I={class:"text-muted-foreground"},O={class:"text-lg font-bold text-primary mt-2"},W={class:"card"},F={class:"card-content space-y-4"},G={class:"grid md:grid-cols-2 gap-4"},J=["min"],Q=["min"],Y={class:"grid md:grid-cols-2 gap-4"},Z={key:0,class:"bg-muted p-4 rounded-lg"},ee={class:"flex justify-between items-center"},te={class:"font-semibold"},le={class:"flex justify-between items-center"},se={class:"font-semibold"},oe={class:"flex justify-between items-center"},ae={class:"font-semibold"},ne={class:"flex justify-between items-center text-lg font-bold border-t pt-2 mt-2"},de={class:"text-primary"},re={class:"card"},ie={class:"card-content space-y-4"},ue={class:"grid md:grid-cols-2 gap-4"},me={class:"grid md:grid-cols-2 gap-4"},pe={key:0},ve={class:"flex gap-4"},ce=["disabled"],fe={key:0},ge={key:1},_e={__name:"Booking",props:{product:Object},setup(o){const r=o,l=V({product_id:r.product.id,rental_start_date:"",rental_end_date:"",rental_days:0,selected_size:"",selected_color:"",unit_price:r.product.price,total_price:0,deposit_amount:r.product.deposit_amount,customer_name:"",customer_email:"",customer_phone:"",customer_address:"",delivery_method:"",delivery_address:"",notes:""}),i=V(!1),w=p(()=>new Date().toISOString().split("T")[0]),u=p(()=>{if(!l.value.rental_start_date||!l.value.rental_end_date)return 0;const m=new Date(l.value.rental_start_date),t=new Date(l.value.rental_end_date),s=Math.abs(t-m),M=Math.ceil(s/(1e3*60*60*24));return M>0?M:0}),k=p(()=>u.value*r.product.price),U=p(()=>k.value+r.product.deposit_amount),S=p(()=>l.value.rental_start_date&&l.value.rental_end_date&&u.value>0&&l.value.customer_name&&l.value.customer_email&&l.value.customer_phone&&l.value.customer_address&&l.value.delivery_method);B([()=>l.value.rental_start_date,()=>l.value.rental_end_date],()=>{l.value.rental_days=u.value,l.value.total_price=k.value});const h=()=>{S.value&&(i.value=!0,l.value.delivery_method==="delivery"&&!l.value.delivery_address&&(l.value.delivery_address=l.value.customer_address),T.post("/booking",l.value,{onFinish:()=>{i.value=!1},onError:m=>{console.error("Booking errors:",m),i.value=!1}}))};return(m,t)=>(v(),D(N,null,{default:c(()=>[e("section",j,[e("div",q,[e("nav",A,[f(g(x),{href:"/",class:"text-muted-foreground hover:text-foreground"},{default:c(()=>t[11]||(t[11]=[b("Beranda")])),_:1,__:[11]}),t[13]||(t[13]=e("span",{class:"text-muted-foreground"},"/",-1)),f(g(x),{href:"/catalog",class:"text-muted-foreground hover:text-foreground"},{default:c(()=>t[12]||(t[12]=[b("Katalog")])),_:1,__:[12]}),t[14]||(t[14]=e("span",{class:"text-muted-foreground"},"/",-1)),f(g(x),{href:`/product/${o.product.sku}`,class:"text-muted-foreground hover:text-foreground"},{default:c(()=>[b(n(o.product.name),1)]),_:1},8,["href"]),t[15]||(t[15]=e("span",{class:"text-muted-foreground"},"/",-1)),t[16]||(t[16]=e("span",{class:"text-foreground font-medium"},"Booking",-1))])])]),e("section",C,[e("div",X,[t[38]||(t[38]=e("div",{class:"text-center mb-8"},[e("h1",{class:"text-3xl font-bold text-foreground mb-4"},"Booking Gaun"),e("p",{class:"text-muted-foreground"},"Lengkapi form di bawah untuk melakukan booking")],-1)),e("form",{onSubmit:P(h,["prevent"]),class:"space-y-8"},[e("div",R,[t[17]||(t[17]=e("div",{class:"card-header"},[e("h2",{class:"text-xl font-semibold"},"Ringkasan Produk")],-1)),e("div",K,[e("div",z,[e("img",{src:o.product.image,alt:o.product.name,class:"w-24 h-24 object-cover rounded-lg"},null,8,E),e("div",$,[e("h3",H,n(o.product.name),1),e("p",I,n(o.product.category),1),e("p",O," Rp "+n(Number(o.product.price).toLocaleString())+"/hari ",1)])])])]),e("div",W,[t[27]||(t[27]=e("div",{class:"card-header"},[e("h2",{class:"text-xl font-semibold"},"Detail Sewa")],-1)),e("div",F,[e("div",G,[e("div",null,[t[18]||(t[18]=e("label",{class:"block text-sm font-medium text-foreground mb-2"},"Tanggal Mulai *",-1)),a(e("input",{"onUpdate:modelValue":t[0]||(t[0]=s=>l.value.rental_start_date=s),type:"date",min:w.value,required:"",class:"input-field"},null,8,J),[[d,l.value.rental_start_date]])]),e("div",null,[t[19]||(t[19]=e("label",{class:"block text-sm font-medium text-foreground mb-2"},"Tanggal Selesai *",-1)),a(e("input",{"onUpdate:modelValue":t[1]||(t[1]=s=>l.value.rental_end_date=s),type:"date",min:l.value.rental_start_date||w.value,required:"",class:"input-field"},null,8,Q),[[d,l.value.rental_end_date]])])]),e("div",Y,[e("div",null,[t[21]||(t[21]=e("label",{class:"block text-sm font-medium text-foreground mb-2"},"Ukuran",-1)),a(e("select",{"onUpdate:modelValue":t[2]||(t[2]=s=>l.value.selected_size=s),class:"input-field"},t[20]||(t[20]=[e("option",{value:""},"Pilih Ukuran",-1),e("option",{value:"S"},"S",-1),e("option",{value:"M"},"M",-1),e("option",{value:"L"},"L",-1),e("option",{value:"XL"},"XL",-1),e("option",{value:"XXL"},"XXL",-1)]),512),[[y,l.value.selected_size]])]),e("div",null,[t[23]||(t[23]=e("label",{class:"block text-sm font-medium text-foreground mb-2"},"Warna",-1)),a(e("select",{"onUpdate:modelValue":t[3]||(t[3]=s=>l.value.selected_color=s),class:"input-field"},t[22]||(t[22]=[e("option",{value:""},"Pilih Warna",-1),e("option",{value:"Putih"},"Putih",-1),e("option",{value:"Cream"},"Cream",-1),e("option",{value:"Navy"},"Navy",-1),e("option",{value:"Maroon"},"Maroon",-1),e("option",{value:"Hitam"},"Hitam",-1)]),512),[[y,l.value.selected_color]])])]),u.value>0?(v(),_("div",Z,[e("div",ee,[t[24]||(t[24]=e("span",null,"Durasi Sewa:",-1)),e("span",te,n(u.value)+" hari",1)]),e("div",le,[t[25]||(t[25]=e("span",null,"Subtotal:",-1)),e("span",se,"Rp "+n(k.value.toLocaleString()),1)]),e("div",oe,[e("span",null,"Deposit ("+n(o.product.deposit_percentage)+"%):",1),e("span",ae,"Rp "+n(Number(o.product.deposit_amount).toLocaleString()),1)]),e("div",ne,[t[26]||(t[26]=e("span",null,"Total Pembayaran:",-1)),e("span",de,"Rp "+n(U.value.toLocaleString()),1)])])):L("",!0)])]),e("div",re,[t[36]||(t[36]=e("div",{class:"card-header"},[e("h2",{class:"text-xl font-semibold"},"Informasi Penyewa")],-1)),e("div",ie,[e("div",ue,[e("div",null,[t[28]||(t[28]=e("label",{class:"block text-sm font-medium text-foreground mb-2"},"Nama Lengkap *",-1)),a(e("input",{"onUpdate:modelValue":t[4]||(t[4]=s=>l.value.customer_name=s),type:"text",required:"",class:"input-field",placeholder:"Masukkan nama lengkap"},null,512),[[d,l.value.customer_name]])]),e("div",null,[t[29]||(t[29]=e("label",{class:"block text-sm font-medium text-foreground mb-2"},"Email *",-1)),a(e("input",{"onUpdate:modelValue":t[5]||(t[5]=s=>l.value.customer_email=s),type:"email",required:"",class:"input-field",placeholder:"<EMAIL>"},null,512),[[d,l.value.customer_email]])])]),e("div",me,[e("div",null,[t[30]||(t[30]=e("label",{class:"block text-sm font-medium text-foreground mb-2"},"No. Telepon *",-1)),a(e("input",{"onUpdate:modelValue":t[6]||(t[6]=s=>l.value.customer_phone=s),type:"tel",required:"",class:"input-field",placeholder:"08123456789"},null,512),[[d,l.value.customer_phone]])]),e("div",null,[t[32]||(t[32]=e("label",{class:"block text-sm font-medium text-foreground mb-2"},"Metode Pengambilan *",-1)),a(e("select",{"onUpdate:modelValue":t[7]||(t[7]=s=>l.value.delivery_method=s),required:"",class:"input-field"},t[31]||(t[31]=[e("option",{value:""},"Pilih Metode",-1),e("option",{value:"pickup"},"Ambil di Toko",-1),e("option",{value:"delivery"},"Antar ke Alamat",-1)]),512),[[y,l.value.delivery_method]])])]),e("div",null,[t[33]||(t[33]=e("label",{class:"block text-sm font-medium text-foreground mb-2"},"Alamat Lengkap *",-1)),a(e("textarea",{"onUpdate:modelValue":t[8]||(t[8]=s=>l.value.customer_address=s),required:"",rows:"3",class:"input-field",placeholder:"Masukkan alamat lengkap"},null,512),[[d,l.value.customer_address]])]),l.value.delivery_method==="delivery"?(v(),_("div",pe,[t[34]||(t[34]=e("label",{class:"block text-sm font-medium text-foreground mb-2"},"Alamat Pengiriman",-1)),a(e("textarea",{"onUpdate:modelValue":t[9]||(t[9]=s=>l.value.delivery_address=s),rows:"3",class:"input-field",placeholder:"Kosongkan jika sama dengan alamat di atas"},null,512),[[d,l.value.delivery_address]])])):L("",!0),e("div",null,[t[35]||(t[35]=e("label",{class:"block text-sm font-medium text-foreground mb-2"},"Catatan Tambahan",-1)),a(e("textarea",{"onUpdate:modelValue":t[10]||(t[10]=s=>l.value.notes=s),rows:"3",class:"input-field",placeholder:"Catatan khusus untuk pesanan Anda"},null,512),[[d,l.value.notes]])])])]),e("div",ve,[f(g(x),{href:`/product/${o.product.sku}`,class:"btn-outline flex-1 text-center py-3"},{default:c(()=>t[37]||(t[37]=[b(" Kembali ")])),_:1,__:[37]},8,["href"]),e("button",{type:"submit",disabled:!S.value||i.value,class:"btn-primary flex-1 py-3 disabled:opacity-50 disabled:cursor-not-allowed"},[i.value?(v(),_("span",fe,"Memproses...")):(v(),_("span",ge,"Konfirmasi Booking"))],8,ce)])],32)])])]),_:1}))}};export{_e as default};
