import{m as g,j as U,c as p,o as l,w as u,b as t,k as m,q as B,g as n,F as w,y as k,t as d,s as C,i as S,n as j,a as D,d as x,l as b,f,x as P}from"./app-7Re41hnF.js";import{_ as A}from"./AppLayout-DNOk7MlS.js";import{_ as F}from"./_plugin-vue_export-helper-DlAUqK2U.js";const L={class:"py-8 bg-white border-b"},R={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},I={class:"flex flex-col lg:flex-row gap-4 items-center justify-between"},K={class:"flex-1 max-w-md"},M={class:"flex gap-4"},O=["value"],$={class:"py-12 bg-brand-bg"},q={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},z={key:0,class:"text-center py-12"},E={key:1,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"},G={class:"aspect-w-3 aspect-h-4 bg-gray-200"},H=["src","alt"],Q={class:"p-4"},Z={class:"text-lg font-semibold text-brand-dark mb-2 line-clamp-2"},J={class:"text-sm text-brand-medium mb-2"},W={class:"flex items-center mb-3"},X={class:"text-sm text-brand-medium"},Y={class:"flex items-center justify-between mb-3"},ee={class:"text-sm text-brand-medium"},te={class:"text-sm text-brand-medium"},se={class:"flex justify-between items-center"},ae={class:"text-xl font-bold text-brand-dark"},re={key:2,class:"mt-12 flex justify-center"},le={class:"flex items-center space-x-2"},oe={key:1,class:"px-3 py-2"},ne={__name:"Catalog",props:{products:Object,categories:Array,filters:Object},setup(o){const i=o,h=g(i.filters.search||""),v=g(i.filters.category||""),_=g(i.filters.status||""),y=g(i.filters.sort||"name");let T=null;const c=()=>{clearTimeout(T),T=setTimeout(()=>{P.get("/catalog",{search:h.value,category:v.value,status:_.value,sort:y.value},{preserveState:!0,preserveScroll:!0})},300)},V=U(()=>{const a=[],s=i.products.current_page,e=i.products.last_page;if(e<=7)for(let r=1;r<=e;r++)a.push(r);else if(s<=4){for(let r=1;r<=5;r++)a.push(r);a.push("..."),a.push(e)}else if(s>=e-3){a.push(1),a.push("...");for(let r=e-4;r<=e;r++)a.push(r)}else{a.push(1),a.push("...");for(let r=s-1;r<=s+1;r++)a.push(r);a.push("..."),a.push(e)}return a}),N=a=>{const s=new URL(window.location);return s.searchParams.set("page",a),s.pathname+s.search};return(a,s)=>(l(),p(A,null,{default:u(()=>[s[12]||(s[12]=t("section",{class:"bg-brand-light py-12"},[t("div",{class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},[t("div",{class:"text-center"},[t("h1",{class:"text-4xl font-bold text-brand-dark mb-4"},"Katalog Gaun Syar'i"),t("p",{class:"text-xl text-brand-medium"},"Temukan gaun syar'i impian Anda untuk acara spesial")])])],-1)),t("section",L,[t("div",R,[t("div",I,[t("div",K,[m(t("input",{"onUpdate:modelValue":s[0]||(s[0]=e=>h.value=e),onInput:c,type:"text",placeholder:"Cari gaun...",class:"w-full px-4 py-2 border border-brand-light rounded-lg focus:ring-2 focus:ring-brand-medium focus:border-transparent"},null,544),[[B,h.value]])]),t("div",M,[m(t("select",{"onUpdate:modelValue":s[1]||(s[1]=e=>v.value=e),onChange:c,class:"px-4 py-2 border border-brand-light rounded-lg focus:ring-2 focus:ring-brand-medium"},[s[4]||(s[4]=t("option",{value:""},"Semua Kategori",-1)),(l(!0),n(w,null,k(o.categories,e=>(l(),n("option",{key:e.id,value:e.slug},d(e.name)+" ("+d(e.active_products_count)+") ",9,O))),128))],544),[[C,v.value]]),m(t("select",{"onUpdate:modelValue":s[2]||(s[2]=e=>_.value=e),onChange:c,class:"px-4 py-2 border border-brand-light rounded-lg focus:ring-2 focus:ring-brand-medium"},s[5]||(s[5]=[t("option",{value:""},"Semua Status",-1),t("option",{value:"available"},"Tersedia",-1),t("option",{value:"rented"},"Disewa",-1)]),544),[[C,_.value]]),m(t("select",{"onUpdate:modelValue":s[3]||(s[3]=e=>y.value=e),onChange:c,class:"px-4 py-2 border border-brand-light rounded-lg focus:ring-2 focus:ring-brand-medium"},s[6]||(s[6]=[t("option",{value:"name"},"Nama A-Z",-1),t("option",{value:"price"},"Harga",-1),t("option",{value:"rating"},"Rating",-1)]),544),[[C,y.value]])])])])]),t("section",$,[t("div",q,[o.products.data.length===0?(l(),n("div",z,s[7]||(s[7]=[t("p",{class:"text-xl text-brand-medium"},"Tidak ada gaun yang ditemukan.",-1)]))):(l(),n("div",E,[(l(!0),n(w,null,k(o.products.data,e=>(l(),n("div",{key:e.id,class:"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow"},[t("div",G,[t("img",{src:e.image,alt:e.name,class:"w-full h-64 object-cover"},null,8,H)]),t("div",Q,[t("h3",Z,d(e.name),1),t("p",J,d(e.category),1),t("div",W,[s[8]||(s[8]=t("span",{class:"text-yellow-400 mr-1"},"★",-1)),t("span",X,d(e.rating),1),t("span",{class:j(["ml-auto text-sm px-2 py-1 rounded-full",e.status==="Tersedia"?"bg-green-100 text-green-800":"bg-red-100 text-red-800"])},d(e.status),3)]),t("div",Y,[t("span",ee,d(e.color),1),t("span",te,d(e.motif),1)]),t("div",se,[t("span",ae,"Rp "+d(Number(e.price).toLocaleString()),1),D(x(b),{href:`/product/${e.sku}`,class:"bg-brand-dark text-white px-3 py-2 rounded text-sm hover:bg-brand-medium transition-colors"},{default:u(()=>s[9]||(s[9]=[f(" Detail ")])),_:2,__:[9]},1032,["href"])])])]))),128))])),o.products.last_page>1?(l(),n("div",re,[t("nav",le,[o.products.prev_page_url?(l(),p(x(b),{key:0,href:o.products.prev_page_url,class:"px-3 py-2 bg-white border border-brand-light rounded-lg hover:bg-brand-light transition-colors"},{default:u(()=>s[10]||(s[10]=[f(" Previous ")])),_:1,__:[10]},8,["href"])):S("",!0),(l(!0),n(w,null,k(V.value,e=>(l(),n("span",{key:e,class:"px-3 py-2"},[e!=="..."?(l(),p(x(b),{key:0,href:N(e),class:j(["px-3 py-2 rounded-lg transition-colors",e===o.products.current_page?"bg-brand-dark text-white":"bg-white border border-brand-light hover:bg-brand-light"])},{default:u(()=>[f(d(e),1)]),_:2},1032,["href","class"])):(l(),n("span",oe,"..."))]))),128)),o.products.next_page_url?(l(),p(x(b),{key:1,href:o.products.next_page_url,class:"px-3 py-2 bg-white border border-brand-light rounded-lg hover:bg-brand-light transition-colors"},{default:u(()=>s[11]||(s[11]=[f(" Next ")])),_:1,__:[11]},8,["href"])):S("",!0)])])):S("",!0)])])]),_:1,__:[12]}))}},ce=F(ne,[["__scopeId","data-v-b2973973"]]);export{ce as default};
