import{_ as i}from"./AuthenticatedLayout-3-7kYo0D.js";import m from"./DeleteUserForm-Dd7AznrP.js";import l from"./UpdatePasswordForm-T3Z7csU1.js";import r from"./UpdateProfileInformationForm-CMFVIj-g.js";import{g as d,o as n,a as t,d as c,h as p,w as o,b as s,F as _}from"./app-kiRtcQ7Q.js";import"./ApplicationLogo-6dMqEb3G.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./TextInput-DDYDNAqf.js";import"./PrimaryButton-QLD6zaEn.js";const u={class:"py-12"},f={class:"mx-auto max-w-7xl space-y-6 sm:px-6 lg:px-8"},x={class:"bg-white p-4 shadow sm:rounded-lg sm:p-8"},h={class:"bg-white p-4 shadow sm:rounded-lg sm:p-8"},g={class:"bg-white p-4 shadow sm:rounded-lg sm:p-8"},N={__name:"Edit",props:{mustVerifyEmail:{type:Boolean},status:{type:String}},setup(a){return(w,e)=>(n(),d(_,null,[t(c(p),{title:"Profile"}),t(i,null,{header:o(()=>e[0]||(e[0]=[s("h2",{class:"text-xl font-semibold leading-tight text-gray-800"}," Profile ",-1)])),default:o(()=>[s("div",u,[s("div",f,[s("div",x,[t(r,{"must-verify-email":a.mustVerifyEmail,status:a.status,class:"max-w-xl"},null,8,["must-verify-email","status"])]),s("div",h,[t(l,{class:"max-w-xl"})]),s("div",g,[t(m,{class:"max-w-xl"})])])])]),_:1})],64))}};export{N as default};
