import{_ as r}from"./_plugin-vue_export-helper-DlAUqK2U.js";import{g as t,o,r as n}from"./app-Cn_9FHpg.js";const s={},i={class:"inline-flex items-center rounded-md border border-transparent bg-gray-800 px-4 py-2 text-xs font-semibold uppercase tracking-widest text-white transition duration-150 ease-in-out hover:bg-gray-700 focus:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 active:bg-gray-900"};function a(e,c){return o(),t("button",i,[n(e.$slots,"default")])}const d=r(s,[["render",a]]);export{d as P};
