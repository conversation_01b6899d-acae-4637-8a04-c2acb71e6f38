import{r as x,c as U,a as b,o,w as p,b as t,d as f,v as B,e as n,F as w,f as k,t as d,g as C,h as S,n as j,i as D,u as h,l as v,j as _,k as P}from"./app-DdRvbQkn.js";import{_ as A}from"./AppLayout-D4oDEfer.js";const F=(l,i)=>{const u=l.__vccOpts||l;for(const[c,g]of i)u[c]=g;return u},L={class:"py-8 bg-white border-b"},O={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},R={class:"flex flex-col lg:flex-row gap-4 items-center justify-between"},I={class:"flex-1 max-w-md"},K={class:"flex gap-4"},M=["value"],$={class:"py-12 bg-brand-bg"},z={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},E={key:0,class:"text-center py-12"},G={key:1,class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"},H={class:"aspect-w-3 aspect-h-4 bg-gray-200"},Q=["src","alt"],Z={class:"p-4"},q={class:"text-lg font-semibold text-brand-dark mb-2 line-clamp-2"},J={class:"text-sm text-brand-medium mb-2"},W={class:"flex items-center mb-3"},X={class:"text-sm text-brand-medium"},Y={class:"flex items-center justify-between mb-3"},ee={class:"text-sm text-brand-medium"},te={class:"text-sm text-brand-medium"},se={class:"flex justify-between items-center"},ae={class:"text-xl font-bold text-brand-dark"},re={key:2,class:"mt-12 flex justify-center"},oe={class:"flex items-center space-x-2"},le={key:1,class:"px-3 py-2"},ne={__name:"Catalog",props:{products:Object,categories:Array,filters:Object},setup(l){const i=l,u=x(i.filters.search||""),c=x(i.filters.category||""),g=x(i.filters.status||""),y=x(i.filters.sort||"name");let T=null;const m=()=>{clearTimeout(T),T=setTimeout(()=>{P.get("/catalog",{search:u.value,category:c.value,status:g.value,sort:y.value},{preserveState:!0,preserveScroll:!0})},300)},V=U(()=>{const a=[],s=i.products.current_page,e=i.products.last_page;if(e<=7)for(let r=1;r<=e;r++)a.push(r);else if(s<=4){for(let r=1;r<=5;r++)a.push(r);a.push("..."),a.push(e)}else if(s>=e-3){a.push(1),a.push("...");for(let r=e-4;r<=e;r++)a.push(r)}else{a.push(1),a.push("...");for(let r=s-1;r<=s+1;r++)a.push(r);a.push("..."),a.push(e)}return a}),N=a=>{const s=new URL(window.location);return s.searchParams.set("page",a),s.pathname+s.search};return(a,s)=>(o(),b(A,null,{default:p(()=>[s[12]||(s[12]=t("section",{class:"bg-brand-light py-12"},[t("div",{class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},[t("div",{class:"text-center"},[t("h1",{class:"text-4xl font-bold text-brand-dark mb-4"},"Katalog Gaun Syar'i"),t("p",{class:"text-xl text-brand-medium"},"Temukan gaun syar'i impian Anda untuk acara spesial")])])],-1)),t("section",L,[t("div",O,[t("div",R,[t("div",I,[f(t("input",{"onUpdate:modelValue":s[0]||(s[0]=e=>u.value=e),onInput:m,type:"text",placeholder:"Cari gaun...",class:"w-full px-4 py-2 border border-brand-light rounded-lg focus:ring-2 focus:ring-brand-medium focus:border-transparent"},null,544),[[B,u.value]])]),t("div",K,[f(t("select",{"onUpdate:modelValue":s[1]||(s[1]=e=>c.value=e),onChange:m,class:"px-4 py-2 border border-brand-light rounded-lg focus:ring-2 focus:ring-brand-medium"},[s[4]||(s[4]=t("option",{value:""},"Semua Kategori",-1)),(o(!0),n(w,null,k(l.categories,e=>(o(),n("option",{key:e.id,value:e.slug},d(e.name)+" ("+d(e.active_products_count)+") ",9,M))),128))],544),[[C,c.value]]),f(t("select",{"onUpdate:modelValue":s[2]||(s[2]=e=>g.value=e),onChange:m,class:"px-4 py-2 border border-brand-light rounded-lg focus:ring-2 focus:ring-brand-medium"},s[5]||(s[5]=[t("option",{value:""},"Semua Status",-1),t("option",{value:"available"},"Tersedia",-1),t("option",{value:"rented"},"Disewa",-1)]),544),[[C,g.value]]),f(t("select",{"onUpdate:modelValue":s[3]||(s[3]=e=>y.value=e),onChange:m,class:"px-4 py-2 border border-brand-light rounded-lg focus:ring-2 focus:ring-brand-medium"},s[6]||(s[6]=[t("option",{value:"name"},"Nama A-Z",-1),t("option",{value:"price"},"Harga",-1),t("option",{value:"rating"},"Rating",-1)]),544),[[C,y.value]])])])])]),t("section",$,[t("div",z,[l.products.data.length===0?(o(),n("div",E,s[7]||(s[7]=[t("p",{class:"text-xl text-brand-medium"},"Tidak ada gaun yang ditemukan.",-1)]))):(o(),n("div",G,[(o(!0),n(w,null,k(l.products.data,e=>(o(),n("div",{key:e.id,class:"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow"},[t("div",H,[t("img",{src:e.image,alt:e.name,class:"w-full h-64 object-cover"},null,8,Q)]),t("div",Z,[t("h3",q,d(e.name),1),t("p",J,d(e.category),1),t("div",W,[s[8]||(s[8]=t("span",{class:"text-yellow-400 mr-1"},"★",-1)),t("span",X,d(e.rating),1),t("span",{class:j(["ml-auto text-sm px-2 py-1 rounded-full",e.status==="Tersedia"?"bg-green-100 text-green-800":"bg-red-100 text-red-800"])},d(e.status),3)]),t("div",Y,[t("span",ee,d(e.color),1),t("span",te,d(e.motif),1)]),t("div",se,[t("span",ae,"Rp "+d(Number(e.price).toLocaleString()),1),D(h(v),{href:`/dress/${e.id}`,class:"bg-brand-dark text-white px-3 py-2 rounded text-sm hover:bg-brand-medium transition-colors"},{default:p(()=>s[9]||(s[9]=[_(" Detail ")])),_:2,__:[9]},1032,["href"])])])]))),128))])),l.products.last_page>1?(o(),n("div",re,[t("nav",oe,[l.products.prev_page_url?(o(),b(h(v),{key:0,href:l.products.prev_page_url,class:"px-3 py-2 bg-white border border-brand-light rounded-lg hover:bg-brand-light transition-colors"},{default:p(()=>s[10]||(s[10]=[_(" Previous ")])),_:1,__:[10]},8,["href"])):S("",!0),(o(!0),n(w,null,k(V.value,e=>(o(),n("span",{key:e,class:"px-3 py-2"},[e!=="..."?(o(),b(h(v),{key:0,href:N(e),class:j(["px-3 py-2 rounded-lg transition-colors",e===l.products.current_page?"bg-brand-dark text-white":"bg-white border border-brand-light hover:bg-brand-light"])},{default:p(()=>[_(d(e),1)]),_:2},1032,["href","class"])):(o(),n("span",le,"..."))]))),128)),l.products.next_page_url?(o(),b(h(v),{key:1,href:l.products.next_page_url,class:"px-3 py-2 bg-white border border-brand-light rounded-lg hover:bg-brand-light transition-colors"},{default:p(()=>s[11]||(s[11]=[_(" Next ")])),_:1,__:[11]},8,["href"])):S("",!0)])])):S("",!0)])])]),_:1,__:[12]}))}},ue=F(ne,[["__scopeId","data-v-efabc6cf"]]);export{ue as default};
