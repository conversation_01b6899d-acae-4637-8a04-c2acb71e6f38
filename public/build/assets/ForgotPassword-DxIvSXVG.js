import{u,c as d,o as r,w as i,a as t,b as o,g as c,i as f,d as a,h as p,t as _,e as w,n as g,f as y}from"./app-DvAxUTK4.js";import{_ as b}from"./GuestLayout-DK8qaPcr.js";import{_ as x,a as k,b as V}from"./TextInput-CRBH6do8.js";import{P as v}from"./PrimaryButton-B70gssqv.js";import"./ApplicationLogo-nv4uSAVB.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";const B={key:0,class:"mb-4 text-sm font-medium text-green-600"},N={class:"mt-4 flex items-center justify-end"},j={__name:"ForgotPassword",props:{status:{type:String}},setup(l){const e=u({email:""}),m=()=>{e.post(route("password.email"))};return(P,s)=>(r(),d(b,null,{default:i(()=>[t(a(p),{title:"Forgot Password"}),s[2]||(s[2]=o("div",{class:"mb-4 text-sm text-gray-600"}," Forgot your password? No problem. Just let us know your email address and we will email you a password reset link that will allow you to choose a new one. ",-1)),l.status?(r(),c("div",B,_(l.status),1)):f("",!0),o("form",{onSubmit:w(m,["prevent"])},[o("div",null,[t(x,{for:"email",value:"Email"}),t(k,{id:"email",type:"email",class:"mt-1 block w-full",modelValue:a(e).email,"onUpdate:modelValue":s[0]||(s[0]=n=>a(e).email=n),required:"",autofocus:"",autocomplete:"username"},null,8,["modelValue"]),t(V,{class:"mt-2",message:a(e).errors.email},null,8,["message"])]),o("div",N,[t(v,{class:g({"opacity-25":a(e).processing}),disabled:a(e).processing},{default:i(()=>s[1]||(s[1]=[y(" Email Password Reset Link ")])),_:1,__:[1]},8,["class","disabled"])])],32)]),_:1,__:[2]}))}};export{j as default};
