import{j as y,k as v,v as x,g as w,o as n,u as h,c,w as i,a as o,i as p,b as r,d as t,h as V,t as B,e as C,l as $,f,n as P}from"./app-kiRtcQ7Q.js";import{_ as q}from"./GuestLayout-BIXxgFxX.js";import{_ as g,a as _,b as k}from"./TextInput-DDYDNAqf.js";import{P as N}from"./PrimaryButton-QLD6zaEn.js";import"./ApplicationLogo-6dMqEb3G.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";const U=["value"],F={__name:"Checkbox",props:{checked:{type:[A<PERSON>y,Boolean],required:!0},value:{default:null}},emits:["update:checked"],setup(l,{emit:s}){const m=s,d=l,e=y({get(){return d.checked},set(a){m("update:checked",a)}});return(a,u)=>v((n(),w("input",{type:"checkbox",value:l.value,"onUpdate:modelValue":u[0]||(u[0]=b=>e.value=b),class:"rounded border-gray-300 text-indigo-600 shadow-sm focus:ring-indigo-500"},null,8,U)),[[x,e.value]])}},L={key:0,class:"mb-4 text-sm font-medium text-green-600"},R={class:"mt-4"},S={class:"mt-4 block"},j={class:"flex items-center"},D={class:"mt-4 flex items-center justify-end"},H={__name:"Login",props:{canResetPassword:{type:Boolean},status:{type:String}},setup(l){const s=h({email:"",password:"",remember:!1}),m=()=>{s.post(route("login"),{onFinish:()=>s.reset("password")})};return(d,e)=>(n(),c(q,null,{default:i(()=>[o(t(V),{title:"Log in"}),l.status?(n(),w("div",L,B(l.status),1)):p("",!0),r("form",{onSubmit:C(m,["prevent"])},[r("div",null,[o(g,{for:"email",value:"Email"}),o(_,{id:"email",type:"email",class:"mt-1 block w-full",modelValue:t(s).email,"onUpdate:modelValue":e[0]||(e[0]=a=>t(s).email=a),required:"",autofocus:"",autocomplete:"username"},null,8,["modelValue"]),o(k,{class:"mt-2",message:t(s).errors.email},null,8,["message"])]),r("div",R,[o(g,{for:"password",value:"Password"}),o(_,{id:"password",type:"password",class:"mt-1 block w-full",modelValue:t(s).password,"onUpdate:modelValue":e[1]||(e[1]=a=>t(s).password=a),required:"",autocomplete:"current-password"},null,8,["modelValue"]),o(k,{class:"mt-2",message:t(s).errors.password},null,8,["message"])]),r("div",S,[r("label",j,[o(F,{name:"remember",checked:t(s).remember,"onUpdate:checked":e[2]||(e[2]=a=>t(s).remember=a)},null,8,["checked"]),e[3]||(e[3]=r("span",{class:"ms-2 text-sm text-gray-600"},"Remember me",-1))])]),r("div",D,[l.canResetPassword?(n(),c(t($),{key:0,href:d.route("password.request"),class:"rounded-md text-sm text-gray-600 underline hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"},{default:i(()=>e[4]||(e[4]=[f(" Forgot your password? ")])),_:1,__:[4]},8,["href"])):p("",!0),o(N,{class:P(["ms-4",{"opacity-25":t(s).processing}]),disabled:t(s).processing},{default:i(()=>e[5]||(e[5]=[f(" Log in ")])),_:1,__:[5]},8,["class","disabled"])])],32)]),_:1}))}};export{H as default};
