import{m as _,j as w,c as D,o as r,w as S,b as e,g as n,i as u,e as P,k as j,q as M,t as s,n as x,F as g,y as p,x as L}from"./app-7Re41hnF.js";import{_ as N}from"./AppLayout-DNOk7MlS.js";const B={class:"py-12 bg-background"},C={class:"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8"},T={class:"card-content space-y-4"},A=["disabled"],G={key:0},R={key:1},q={key:0,class:"py-12 bg-muted/30"},I={class:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8"},z={class:"card mb-8"},F={class:"card-header"},O={class:"flex justify-between items-start"},U={class:"text-2xl font-bold text-foreground"},V={class:"text-muted-foreground"},E={class:"text-right"},K={class:"text-sm text-muted-foreground mt-1"},W={class:"card mb-8"},$={class:"card-content"},H={class:"space-y-4"},J={key:0,class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},Q={key:1},X={class:"flex-1"},Y={class:"font-medium text-foreground"},Z={class:"text-sm text-muted-foreground"},ee={key:0,class:"text-xs text-muted-foreground mt-1"},te={class:"grid lg:grid-cols-2 gap-8"},se={class:"card"},oe={class:"card-content space-y-4"},de={class:"font-medium text-foreground"},re={class:"text-sm text-muted-foreground"},ne={class:"text-sm text-muted-foreground"},ae={class:"text-sm text-muted-foreground"},ie={key:0,class:"text-sm text-muted-foreground"},le={class:"text-sm text-muted-foreground"},ue={class:"text-sm text-muted-foreground"},ce={class:"card"},me={class:"card-content space-y-4"},xe={class:"font-medium text-foreground"},ge={class:"text-sm text-muted-foreground"},pe={class:"flex justify-between items-center mt-2"},fe={class:"text-sm text-muted-foreground"},be={key:0},_e={key:1,class:"ml-2"},he={class:"text-right"},ve={class:"font-medium"},ye={class:"text-sm text-muted-foreground"},ke={class:"border-t border-border pt-4 space-y-2"},we={class:"flex justify-between"},De={class:"font-medium"},Se={class:"flex justify-between"},Pe={class:"font-medium"},je={class:"flex justify-between text-lg font-bold"},Me={class:"text-primary"},Le={key:0,class:"card mt-8"},Ne={class:"card-content"},Be={class:"flex justify-between items-start"},Ce={class:"font-medium text-foreground"},Te={class:"text-sm text-muted-foreground"},Ae={key:0,class:"text-sm text-muted-foreground"},Ge={class:"text-right"},Re={class:"font-medium"},qe={key:1,class:"py-12 bg-background"},Ie={class:"max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 text-center"},ze={class:"card"},Fe={class:"card-content"},Oe={class:"text-muted-foreground mb-4"},Ee={__name:"OrderTracking",props:{order:Object,search_query:String},setup(d){const a=d,l=_(a.search_query||""),c=_(!1),h=w(()=>a.order?[{title:"Booking Dibuat",description:"Pesanan booking telah dibuat",completed:!0,date:a.order.created_at},{title:"Menunggu Pembayaran",description:"Silakan lakukan pembayaran sesuai instruksi",completed:a.order.payment_status!=="pending",date:null},{title:"Pembayaran Dikonfirmasi",description:"Pembayaran telah dikonfirmasi",completed:a.order.payment_status==="confirmed",date:a.order.confirmed_at},{title:"Pesanan Diproses",description:"Gaun sedang disiapkan",completed:["processing","shipped","delivered","completed"].includes(a.order.status),date:a.order.confirmed_at},{title:"Siap Diambil/Dikirim",description:a.order.delivery_method==="pickup"?"Gaun siap diambil di toko":"Gaun sedang dikirim",completed:["shipped","delivered","completed"].includes(a.order.status),date:a.order.shipped_at},{title:"Selesai",description:"Gaun telah diterima dan pesanan selesai",completed:["delivered","completed"].includes(a.order.status),date:a.order.delivered_at}]:[]),v=()=>{l.value&&(c.value=!0,L.get("/order-tracking",{order_number:l.value},{onFinish:()=>{c.value=!1}}))},m=i=>i?new Date(i).toLocaleDateString("id-ID",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}):"",y=i=>({pending_payment:"bg-yellow-100 text-yellow-800",confirmed:"bg-blue-100 text-blue-800",processing:"bg-blue-100 text-blue-800",shipped:"bg-purple-100 text-purple-800",delivered:"bg-green-100 text-green-800",completed:"bg-green-100 text-green-800",cancelled:"bg-red-100 text-red-800"})[i]||"bg-gray-100 text-gray-800",k=i=>({pending_payment:"Menunggu Pembayaran",confirmed:"Dikonfirmasi",processing:"Diproses",shipped:"Dikirim",delivered:"Diterima",completed:"Selesai",cancelled:"Dibatalkan"})[i]||i,f=i=>({pending:"Menunggu",waiting_confirmation:"Menunggu Konfirmasi",confirmed:"Dikonfirmasi",failed:"Gagal",refunded:"Dikembalikan"})[i]||i;return(i,t)=>(r(),D(N,null,{default:S(()=>[t[17]||(t[17]=e("section",{class:"bg-muted py-12"},[e("div",{class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8"},[e("div",{class:"text-center"},[e("h1",{class:"text-4xl font-bold text-foreground mb-4"},"Lacak Pesanan"),e("p",{class:"text-xl text-muted-foreground"},"Masukkan nomor pesanan untuk melihat status booking Anda")])])],-1)),e("section",B,[e("div",C,[e("form",{onSubmit:P(v,["prevent"]),class:"card"},[e("div",T,[e("div",null,[t[2]||(t[2]=e("label",{class:"block text-sm font-medium text-foreground mb-2"},"Nomor Pesanan",-1)),j(e("input",{"onUpdate:modelValue":t[0]||(t[0]=o=>l.value=o),type:"text",placeholder:"Contoh: GSJ2501201234",class:"input-field",required:""},null,512),[[M,l.value]]),t[3]||(t[3]=e("p",{class:"text-sm text-muted-foreground mt-1"}," Nomor pesanan dapat ditemukan di email konfirmasi booking ",-1))]),e("button",{type:"submit",disabled:!l.value||c.value,class:"btn-primary w-full py-3 disabled:opacity-50 disabled:cursor-not-allowed"},[c.value?(r(),n("span",G,"Mencari...")):(r(),n("span",R,"Lacak Pesanan"))],8,A)])],32)])]),d.order?(r(),n("section",q,[e("div",I,[e("div",z,[e("div",F,[e("div",O,[e("div",null,[e("h2",U,s(d.order.order_number),1),e("p",V,"Dibuat pada "+s(m(d.order.created_at)),1)]),e("div",E,[e("span",{class:x(["px-3 py-1 rounded-full text-sm font-medium",y(d.order.status)])},s(k(d.order.status)),3),e("p",K," Pembayaran: "+s(f(d.order.payment_status)),1)])])])]),e("div",W,[t[5]||(t[5]=e("div",{class:"card-header"},[e("h3",{class:"text-xl font-semibold"},"Status Pesanan")],-1)),e("div",$,[e("div",H,[(r(!0),n(g,null,p(h.value,(o,b)=>(r(),n("div",{key:b,class:"flex items-center gap-4"},[e("div",{class:x(["w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium",o.completed?"bg-primary text-primary-foreground":"bg-muted text-muted-foreground"])},[o.completed?(r(),n("svg",J,t[4]||(t[4]=[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"},null,-1)]))):(r(),n("span",Q,s(b+1),1))],2),e("div",X,[e("h4",Y,s(o.title),1),e("p",Z,s(o.description),1),o.date?(r(),n("p",ee,s(m(o.date)),1)):u("",!0)])]))),128))])])]),e("div",te,[e("div",se,[t[8]||(t[8]=e("div",{class:"card-header"},[e("h3",{class:"text-xl font-semibold"},"Informasi Penyewa")],-1)),e("div",oe,[e("div",null,[e("h4",de,s(d.order.customer.full_name),1),e("p",re,s(d.order.customer.email),1),e("p",ne,s(d.order.customer.phone),1)]),e("div",null,[t[6]||(t[6]=e("h4",{class:"font-medium text-foreground mb-2"},"Pengambilan",-1)),e("p",ae,s(d.order.delivery_method==="pickup"?"Ambil di Toko":"Antar ke Alamat"),1),d.order.delivery_method==="delivery"?(r(),n("p",ie,s(d.order.delivery_address),1)):u("",!0)]),e("div",null,[t[7]||(t[7]=e("h4",{class:"font-medium text-foreground mb-2"},"Periode Sewa",-1)),e("p",le,s(m(d.order.rental_start_date))+" - "+s(m(d.order.rental_end_date)),1),e("p",ue,s(d.order.rental_days)+" hari",1)])])]),e("div",ce,[t[12]||(t[12]=e("div",{class:"card-header"},[e("h3",{class:"text-xl font-semibold"},"Item Pesanan")],-1)),e("div",me,[(r(!0),n(g,null,p(d.order.items,o=>(r(),n("div",{key:o.product_sku,class:"border-b border-border pb-4 last:border-b-0"},[e("h4",xe,s(o.product_name),1),e("p",ge,"SKU: "+s(o.product_sku),1),e("div",pe,[e("div",fe,[o.selected_size?(r(),n("span",be,"Ukuran: "+s(o.selected_size),1)):u("",!0),o.selected_color?(r(),n("span",_e,"Warna: "+s(o.selected_color),1)):u("",!0)]),e("div",he,[e("p",ve,"Rp "+s(Number(o.total_price).toLocaleString()),1),e("p",ye,s(o.rental_days)+" hari",1)])])]))),128)),e("div",ke,[e("div",we,[t[9]||(t[9]=e("span",{class:"text-muted-foreground"},"Subtotal:",-1)),e("span",De,"Rp "+s(Number(d.order.subtotal).toLocaleString()),1)]),e("div",Se,[t[10]||(t[10]=e("span",{class:"text-muted-foreground"},"Deposit:",-1)),e("span",Pe,"Rp "+s(Number(d.order.deposit_amount).toLocaleString()),1)]),e("div",je,[t[11]||(t[11]=e("span",null,"Total:",-1)),e("span",Me,"Rp "+s(Number(d.order.total_amount).toLocaleString()),1)])])])])]),d.order.payments&&d.order.payments.length>0?(r(),n("div",Le,[t[13]||(t[13]=e("div",{class:"card-header"},[e("h3",{class:"text-xl font-semibold"},"Informasi Pembayaran")],-1)),e("div",Ne,[(r(!0),n(g,null,p(d.order.payments,o=>(r(),n("div",{key:o.payment_reference,class:"border-b border-border pb-4 mb-4 last:border-b-0 last:mb-0"},[e("div",Be,[e("div",null,[e("h4",Ce,s(o.payment_reference),1),e("p",Te,s(o.payment_method)+" - "+s(o.payment_type),1),o.paid_at?(r(),n("p",Ae,"Dibayar: "+s(m(o.paid_at)),1)):u("",!0)]),e("div",Ge,[e("p",Re,"Rp "+s(Number(o.amount).toLocaleString()),1),e("span",{class:x(["px-2 py-1 rounded text-xs font-medium",o.status==="confirmed"?"bg-green-100 text-green-800":o.status==="pending"?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800"])},s(f(o.status)),3)])])]))),128))])])):u("",!0),t[14]||(t[14]=e("div",{class:"card mt-8"},[e("div",{class:"card-content"},[e("div",{class:"text-center"},[e("h3",{class:"font-semibold text-foreground mb-2"},"Butuh Bantuan?"),e("p",{class:"text-muted-foreground mb-4"}," Hubungi customer service kami untuk informasi lebih lanjut "),e("div",{class:"flex justify-center gap-4"},[e("a",{href:"https://wa.me/6281234567890",class:"btn-primary"}," WhatsApp "),e("a",{href:"mailto:<EMAIL>",class:"btn-outline"}," Email ")])])])],-1))])])):l.value&&!c.value?(r(),n("section",qe,[e("div",Ie,[e("div",ze,[e("div",Fe,[t[15]||(t[15]=e("svg",{class:"w-16 h-16 text-muted-foreground mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.29-1.009-5.824-2.562M15 6.306a7.962 7.962 0 00-6 0m6 0a7.962 7.962 0 016 2.562 7.962 7.962 0 01-6 2.562m0-5.124v5.124"})],-1)),t[16]||(t[16]=e("h3",{class:"text-xl font-semibold text-foreground mb-2"},"Pesanan Tidak Ditemukan",-1)),e("p",Oe,' Nomor pesanan "'+s(l.value)+'" tidak ditemukan. Pastikan nomor pesanan sudah benar. ',1),e("button",{onClick:t[1]||(t[1]=o=>l.value=""),class:"btn-primary"}," Coba Lagi ")])])])])):u("",!0)]),_:1,__:[17]}))}};export{Ee as default};
