import{_ as i}from"./AuthenticatedLayout-JUUmCKUa.js";import m from"./DeleteUserForm-D6vXdOKP.js";import l from"./UpdatePasswordForm-DxnkfINj.js";import r from"./UpdateProfileInformationForm-BoXZt84A.js";import{g as d,o as n,a as t,d as c,h as p,w as o,b as s,F as _}from"./app-DvAxUTK4.js";import"./ApplicationLogo-nv4uSAVB.js";import"./_plugin-vue_export-helper-DlAUqK2U.js";import"./TextInput-CRBH6do8.js";import"./PrimaryButton-B70gssqv.js";const u={class:"py-12"},f={class:"mx-auto max-w-7xl space-y-6 sm:px-6 lg:px-8"},x={class:"bg-white p-4 shadow sm:rounded-lg sm:p-8"},h={class:"bg-white p-4 shadow sm:rounded-lg sm:p-8"},g={class:"bg-white p-4 shadow sm:rounded-lg sm:p-8"},N={__name:"Edit",props:{mustVerifyEmail:{type:Boolean},status:{type:String}},setup(a){return(w,e)=>(n(),d(_,null,[t(c(p),{title:"Profile"}),t(i,null,{header:o(()=>e[0]||(e[0]=[s("h2",{class:"text-xl font-semibold leading-tight text-gray-800"}," Profile ",-1)])),default:o(()=>[s("div",u,[s("div",f,[s("div",x,[t(r,{"must-verify-email":a.mustVerifyEmail,status:a.status,class:"max-w-xl"},null,8,["must-verify-email","status"])]),s("div",h,[t(l,{class:"max-w-xl"})]),s("div",g,[t(m,{class:"max-w-xl"})])])])]),_:1})],64))}};export{N as default};
