[2025-07-20 23:15:18] local.ERROR: SQLSTATE[HY000] [1049] Unknown database 'gjs_sewa' (Connection: mysql, SQL: select * from `sessions` where `id` = UuGlB3c9OHUjZhyb07KBSksLCkBGoTfomz0sjTTN limit 1) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1049): SQLSTATE[HY000] [1049] Unknown database 'gjs_sewa' (Connection: mysql, SQL: select * from `sessions` where `id` = UuGlB3c9OHUjZhyb07KBSksLCkBGoTfomz0sjTTN limit 1) at /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Database/Connection.php:822)
[stacktrace]
#0 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Arra<PERSON>, Object(Closure))
#1 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Database/Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#2 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3131): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#3 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3116): Illuminate\\Database\\Query\\Builder->runSelect()
#4 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3115): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#7 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3038): Illuminate\\Database\\Query\\Builder->first(Array)
#8 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php(96): Illuminate\\Database\\Query\\Builder->find('UuGlB3c9OHUjZhy...')
#9 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Session/Store.php(116): Illuminate\\Session\\DatabaseSessionHandler->read('UuGlB3c9OHUjZhy...')
#10 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Session/Store.php(104): Illuminate\\Session\\Store->readFromHandler()
#11 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Session/Store.php(88): Illuminate\\Session\\Store->loadSession()
#12 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(146): Illuminate\\Session\\Store->start()
#13 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Support/helpers.php(399): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#14 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(143): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#15 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(115): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#16 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#17 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#24 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#25 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#26 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#27 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#28 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#29 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#49 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#50 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#51 /Volumes/YG/Project/gjs/gjs-sewa/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#52 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Volumes/YG/Pro...')
#53 {main}

[previous exception] [object] (PDOException(code: 1049): SQLSTATE[HY000] [1049] Unknown database 'gjs_sewa' at /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:66)
[stacktrace]
#0 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(66): PDO->__construct('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#1 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(44): Illuminate\\Database\\Connectors\\Connector->createPdoConnection('mysql:host=127....', 'root', Object(SensitiveParameterValue), Array)
#2 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection('mysql:host=127....', Array, Array)
#3 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect(Array)
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1228): call_user_func(Object(Closure))
#6 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Database/Connection.php(509): Illuminate\\Database\\Connection->getPdo()
#7 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Database/Connection.php(404): Illuminate\\Database\\Connection->getPdoForSelect(false)
#8 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Database/Connection.php(809): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('select * from `...', Array)
#9 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Database/Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('select * from `...', Array, Object(Closure))
#10 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Database/Connection.php(395): Illuminate\\Database\\Connection->run('select * from `...', Array, Object(Closure))
#11 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3131): Illuminate\\Database\\Connection->select('select * from `...', Array, false)
#12 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3116): Illuminate\\Database\\Query\\Builder->runSelect()
#13 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3706): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#14 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3115): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#15 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Database/Concerns/BuildsQueries.php(366): Illuminate\\Database\\Query\\Builder->get(Array)
#16 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3038): Illuminate\\Database\\Query\\Builder->first(Array)
#17 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Session/DatabaseSessionHandler.php(96): Illuminate\\Database\\Query\\Builder->find('UuGlB3c9OHUjZhy...')
#18 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Session/Store.php(116): Illuminate\\Session\\DatabaseSessionHandler->read('UuGlB3c9OHUjZhy...')
#19 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Session/Store.php(104): Illuminate\\Session\\Store->readFromHandler()
#20 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Session/Store.php(88): Illuminate\\Session\\Store->loadSession()
#21 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(146): Illuminate\\Session\\Store->start()
#22 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Support/helpers.php(399): Illuminate\\Session\\Middleware\\StartSession->Illuminate\\Session\\Middleware\\{closure}(Object(Illuminate\\Session\\Store))
#23 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(143): tap(Object(Illuminate\\Session\\Store), Object(Closure))
#24 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(115): Illuminate\\Session\\Middleware\\StartSession->startSession(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store))
#25 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#26 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#58 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#59 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#60 /Volumes/YG/Project/gjs/gjs-sewa/public/index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#61 /Volumes/YG/Project/gjs/gjs-sewa/vendor/laravel/framework/src/Illuminate/Foundation/resources/server.php(23): require_once('/Volumes/YG/Pro...')
#62 {main}
"} 
