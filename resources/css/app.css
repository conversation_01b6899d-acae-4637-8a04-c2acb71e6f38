@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 30 8% 89%;
    --foreground: 30 26% 22%;
    --card: 0 0% 100%;
    --card-foreground: 30 26% 22%;
    --popover: 0 0% 100%;
    --popover-foreground: 30 26% 22%;
    --primary: 30 26% 22%;
    --primary-foreground: 30 8% 89%;
    --secondary: 30 23% 55%;
    --secondary-foreground: 30 26% 22%;
    --muted: 30 18% 78%;
    --muted-foreground: 30 26% 22%;
    --accent: 30 18% 78%;
    --accent-foreground: 30 26% 22%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 86% 97%;
    --border: 30 18% 78%;
    --input: 0 0% 100%;
    --ring: 30 23% 55%;
    --radius: 0.5rem;

    /* Sidebar colors */
    --sidebar-background: #ffffff;
    --sidebar-foreground: #493628;
    --sidebar-primary: #493628;
    --sidebar-primary-foreground: #e4e0e1;
    --sidebar-accent: #d6c0b3;
    --sidebar-accent-foreground: #493628;
    --sidebar-border: #d6c0b3;
    --sidebar-ring: #ab886d;

    /* Brand Colors for compatibility */
    --brand-dark: #493628;
    --brand-medium: #ab886d;
    --brand-light: #d6c0b3;
    --brand-bg: #e4e0e1;
  }

  .dark {
    --background: 30 26% 22%;
    --foreground: 30 8% 89%;
    --card: 30 26% 22%;
    --card-foreground: 30 8% 89%;
    --popover: 30 26% 22%;
    --popover-foreground: 30 8% 89%;
    --primary: 30 8% 89%;
    --primary-foreground: 30 26% 22%;
    --secondary: 30 23% 55%;
    --secondary-foreground: 30 8% 89%;
    --muted: 30 23% 55%;
    --muted-foreground: 30 8% 89%;
    --accent: 30 23% 55%;
    --accent-foreground: 30 8% 89%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 86% 97%;
    --border: 30 23% 55%;
    --input: 30 26% 22%;
    --ring: 30 23% 55%;

    /* Sidebar colors for dark mode */
    --sidebar-background: #493628;
    --sidebar-foreground: #e4e0e1;
    --sidebar-primary: #e4e0e1;
    --sidebar-primary-foreground: #493628;
    --sidebar-accent: #ab886d;
    --sidebar-accent-foreground: #e4e0e1;
    --sidebar-border: #ab886d;
    --sidebar-ring: #ab886d;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #d6c0b3;
}

::-webkit-scrollbar-thumb {
  background: #ab886d;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #493628;
}

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* Brand gradient */
.brand-gradient {
  background: linear-gradient(135deg, #493628 0%, #ab886d 50%, #d6c0b3 100%);
}

/* Card shadows with brand colors */
.card-shadow {
  box-shadow: 0 4px 6px -1px rgba(73, 54, 40, 0.1), 0 2px 4px -1px rgba(73, 54, 40, 0.06);
}

.card-shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(73, 54, 40, 0.1), 0 4px 6px -2px rgba(73, 54, 40, 0.05);
}

/* Additional utility classes */
@layer components {
  .btn-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 px-4 py-2 rounded-md font-medium transition-colors;
  }

  .btn-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/80 px-4 py-2 rounded-md font-medium transition-colors;
  }

  .btn-outline {
    @apply border border-input bg-background hover:bg-accent hover:text-accent-foreground px-4 py-2 rounded-md font-medium transition-colors;
  }

  .input-field {
    @apply flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  .card {
    @apply rounded-lg border bg-card text-card-foreground shadow-sm;
  }

  .card-header {
    @apply flex flex-col space-y-1.5 p-6;
  }

  .card-content {
    @apply p-6 pt-0;
  }

  .card-footer {
    @apply flex items-center p-6 pt-0;
  }
}

/* Text utilities */
@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}
