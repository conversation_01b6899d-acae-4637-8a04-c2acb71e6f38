<template>
  <div class="min-h-screen bg-brand-bg">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-brand-light">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- Logo -->
          <div class="flex items-center">
            <Link href="/" class="text-2xl font-bold text-brand-dark">
              Gaun Syari Jo<PERSON>
            </Link>
          </div>

          <!-- Navigation -->
          <nav class="hidden md:flex space-x-8">
            <Link href="/" class="text-brand-dark hover:text-brand-medium transition-colors">
              Beranda
            </Link>
            <Link href="/catalog" class="text-brand-dark hover:text-brand-medium transition-colors">
              Katalog
            </Link>
            <Link href="/booking" class="text-brand-dark hover:text-brand-medium transition-colors">
              Booking
            </Link>
            <Link href="/order-tracking" class="text-brand-dark hover:text-brand-medium transition-colors">
              <PERSON><PERSON>
            </Link>
          </nav>

          <!-- Mobile menu button -->
          <div class="md:hidden">
            <button @click="mobileMenuOpen = !mobileMenuOpen" class="text-brand-dark">
              <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
          </div>
        </div>

        <!-- Mobile menu -->
        <div v-show="mobileMenuOpen" class="md:hidden py-4 border-t border-brand-light">
          <div class="flex flex-col space-y-2">
            <Link href="/" class="text-brand-dark hover:text-brand-medium transition-colors py-2">
              Beranda
            </Link>
            <Link href="/catalog" class="text-brand-dark hover:text-brand-medium transition-colors py-2">
              Katalog
            </Link>
            <Link href="/booking" class="text-brand-dark hover:text-brand-medium transition-colors py-2">
              Booking
            </Link>
            <Link href="/order-tracking" class="text-brand-dark hover:text-brand-medium transition-colors py-2">
              Lacak Pesanan
            </Link>
          </div>
        </div>
      </div>
    </header>

    <!-- Main Content -->
    <main>
      <slot />
    </main>

    <!-- Footer -->
    <footer class="bg-brand-dark text-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div class="col-span-1 md:col-span-2">
            <h3 class="text-xl font-bold mb-4">Gaun Syari Jogja</h3>
            <p class="text-brand-light mb-4">
              Sewa gaun syar'i berkualitas premium untuk acara spesial Anda. 
              Koleksi terlengkap dengan desain elegan dan modern di Yogyakarta.
            </p>
          </div>
          
          <div>
            <h4 class="font-semibold mb-4">Layanan</h4>
            <ul class="space-y-2 text-brand-light">
              <li><Link href="/catalog" class="hover:text-white transition-colors">Katalog Gaun</Link></li>
              <li><Link href="/booking" class="hover:text-white transition-colors">Booking Online</Link></li>
              <li><Link href="/order-tracking" class="hover:text-white transition-colors">Lacak Pesanan</Link></li>
            </ul>
          </div>
          
          <div>
            <h4 class="font-semibold mb-4">Kontak</h4>
            <ul class="space-y-2 text-brand-light">
              <li>WhatsApp: +62 812-3456-7890</li>
              <li>Email: <EMAIL></li>
              <li>Alamat: Jl. Malioboro No. 123, Yogyakarta</li>
            </ul>
          </div>
        </div>
        
        <div class="border-t border-brand-medium mt-8 pt-8 text-center text-brand-light">
          <p>&copy; 2025 Gaun Syari Jogja. All rights reserved.</p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Link } from '@inertiajs/vue3'

const mobileMenuOpen = ref(false)
</script>
