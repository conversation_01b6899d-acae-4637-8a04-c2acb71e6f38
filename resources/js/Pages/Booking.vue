<template>
  <AppLayout>
    <!-- Breadcrumb -->
    <section class="bg-muted py-4">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <nav class="flex items-center space-x-2 text-sm">
          <Link href="/" class="text-muted-foreground hover:text-foreground">Beranda</Link>
          <span class="text-muted-foreground">/</span>
          <Link href="/catalog" class="text-muted-foreground hover:text-foreground">Katalog</Link>
          <span class="text-muted-foreground">/</span>
          <Link :href="`/product/${product.sku}`" class="text-muted-foreground hover:text-foreground">{{ product.name }}</Link>
          <span class="text-muted-foreground">/</span>
          <span class="text-foreground font-medium">Booking</span>
        </nav>
      </div>
    </section>

    <!-- Booking Form -->
    <section class="py-12 bg-background">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-8">
          <h1 class="text-3xl font-bold text-foreground mb-4">Booking Gaun</h1>
          <p class="text-muted-foreground">Lengkapi form di bawah untuk melakukan booking</p>
        </div>

        <form @submit.prevent="submitBooking" class="space-y-8">
          <!-- Product Summary -->
          <div class="card">
            <div class="card-header">
              <h2 class="text-xl font-semibold">Ringkasan Produk</h2>
            </div>
            <div class="card-content">
              <div class="flex gap-4">
                <img
                  :src="product.image"
                  :alt="product.name"
                  class="w-24 h-24 object-cover rounded-lg"
                />
                <div class="flex-1">
                  <h3 class="font-semibold text-lg">{{ product.name }}</h3>
                  <p class="text-muted-foreground">{{ product.category }}</p>
                  <p class="text-lg font-bold text-primary mt-2">
                    Rp {{ Number(product.price).toLocaleString() }}/hari
                  </p>
                </div>
              </div>
            </div>
          </div>

          <!-- Rental Details -->
          <div class="card">
            <div class="card-header">
              <h2 class="text-xl font-semibold">Detail Sewa</h2>
            </div>
            <div class="card-content space-y-4">
              <div class="grid md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-foreground mb-2">Tanggal Mulai *</label>
                  <input
                    v-model="form.rental_start_date"
                    type="date"
                    :min="minDate"
                    required
                    class="input-field"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-foreground mb-2">Tanggal Selesai *</label>
                  <input
                    v-model="form.rental_end_date"
                    type="date"
                    :min="form.rental_start_date || minDate"
                    required
                    class="input-field"
                  />
                </div>
              </div>

              <div class="grid md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-foreground mb-2">Ukuran</label>
                  <select v-model="form.selected_size" class="input-field">
                    <option value="">Pilih Ukuran</option>
                    <option value="S">S</option>
                    <option value="M">M</option>
                    <option value="L">L</option>
                    <option value="XL">XL</option>
                    <option value="XXL">XXL</option>
                  </select>
                </div>
                <div>
                  <label class="block text-sm font-medium text-foreground mb-2">Warna</label>
                  <select v-model="form.selected_color" class="input-field">
                    <option value="">Pilih Warna</option>
                    <option value="Putih">Putih</option>
                    <option value="Cream">Cream</option>
                    <option value="Navy">Navy</option>
                    <option value="Maroon">Maroon</option>
                    <option value="Hitam">Hitam</option>
                  </select>
                </div>
              </div>

              <div v-if="rentalDays > 0" class="bg-muted p-4 rounded-lg">
                <div class="flex justify-between items-center">
                  <span>Durasi Sewa:</span>
                  <span class="font-semibold">{{ rentalDays }} hari</span>
                </div>
                <div class="flex justify-between items-center">
                  <span>Subtotal:</span>
                  <span class="font-semibold">Rp {{ totalPrice.toLocaleString() }}</span>
                </div>
                <div class="flex justify-between items-center">
                  <span>Deposit ({{ product.deposit_percentage }}%):</span>
                  <span class="font-semibold">Rp {{ Number(product.deposit_amount).toLocaleString() }}</span>
                </div>
                <div class="flex justify-between items-center text-lg font-bold border-t pt-2 mt-2">
                  <span>Total Pembayaran:</span>
                  <span class="text-primary">Rp {{ grandTotal.toLocaleString() }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Customer Information -->
          <div class="card">
            <div class="card-header">
              <h2 class="text-xl font-semibold">Informasi Penyewa</h2>
            </div>
            <div class="card-content space-y-4">
              <div class="grid md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-foreground mb-2">Nama Lengkap *</label>
                  <input
                    v-model="form.customer_name"
                    type="text"
                    required
                    class="input-field"
                    placeholder="Masukkan nama lengkap"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-foreground mb-2">Email *</label>
                  <input
                    v-model="form.customer_email"
                    type="email"
                    required
                    class="input-field"
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>

              <div class="grid md:grid-cols-2 gap-4">
                <div>
                  <label class="block text-sm font-medium text-foreground mb-2">No. Telepon *</label>
                  <input
                    v-model="form.customer_phone"
                    type="tel"
                    required
                    class="input-field"
                    placeholder="08123456789"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium text-foreground mb-2">Metode Pengambilan *</label>
                  <select v-model="form.delivery_method" required class="input-field">
                    <option value="">Pilih Metode</option>
                    <option value="pickup">Ambil di Toko</option>
                    <option value="delivery">Antar ke Alamat</option>
                  </select>
                </div>
              </div>

              <div>
                <label class="block text-sm font-medium text-foreground mb-2">Alamat Lengkap *</label>
                <textarea
                  v-model="form.customer_address"
                  required
                  rows="3"
                  class="input-field"
                  placeholder="Masukkan alamat lengkap"
                ></textarea>
              </div>

              <div v-if="form.delivery_method === 'delivery'">
                <label class="block text-sm font-medium text-foreground mb-2">Alamat Pengiriman</label>
                <textarea
                  v-model="form.delivery_address"
                  rows="3"
                  class="input-field"
                  placeholder="Kosongkan jika sama dengan alamat di atas"
                ></textarea>
              </div>

              <div>
                <label class="block text-sm font-medium text-foreground mb-2">Catatan Tambahan</label>
                <textarea
                  v-model="form.notes"
                  rows="3"
                  class="input-field"
                  placeholder="Catatan khusus untuk pesanan Anda"
                ></textarea>
              </div>
            </div>
          </div>

          <!-- Submit Button -->
          <div class="flex gap-4">
            <Link :href="`/product/${product.sku}`" class="btn-outline flex-1 text-center py-3">
              Kembali
            </Link>
            <button
              type="submit"
              :disabled="!canSubmit || processing"
              class="btn-primary flex-1 py-3 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span v-if="processing">Memproses...</span>
              <span v-else>Konfirmasi Booking</span>
            </button>
          </div>
        </form>
      </div>
    </section>
  </AppLayout>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import AppLayout from '@/Layouts/AppLayout.vue'

const props = defineProps({
  product: Object,
})

// Form data
const form = ref({
  product_id: props.product.id,
  rental_start_date: '',
  rental_end_date: '',
  rental_days: 0,
  selected_size: '',
  selected_color: '',
  unit_price: props.product.price,
  total_price: 0,
  deposit_amount: props.product.deposit_amount,
  customer_name: '',
  customer_email: '',
  customer_phone: '',
  customer_address: '',
  delivery_method: '',
  delivery_address: '',
  notes: '',
})

const processing = ref(false)

// Computed properties
const minDate = computed(() => {
  const today = new Date()
  return today.toISOString().split('T')[0]
})

const rentalDays = computed(() => {
  if (!form.value.rental_start_date || !form.value.rental_end_date) return 0
  
  const start = new Date(form.value.rental_start_date)
  const end = new Date(form.value.rental_end_date)
  const diffTime = Math.abs(end - start)
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  return diffDays > 0 ? diffDays : 0
})

const totalPrice = computed(() => {
  return rentalDays.value * props.product.price
})

const grandTotal = computed(() => {
  return totalPrice.value + props.product.deposit_amount
})

const canSubmit = computed(() => {
  return form.value.rental_start_date &&
         form.value.rental_end_date &&
         rentalDays.value > 0 &&
         form.value.customer_name &&
         form.value.customer_email &&
         form.value.customer_phone &&
         form.value.customer_address &&
         form.value.delivery_method
})

// Watch for date changes
watch([() => form.value.rental_start_date, () => form.value.rental_end_date], () => {
  form.value.rental_days = rentalDays.value
  form.value.total_price = totalPrice.value
})

// Methods
const submitBooking = () => {
  if (!canSubmit.value) return

  processing.value = true

  // Set delivery address if not provided
  if (form.value.delivery_method === 'delivery' && !form.value.delivery_address) {
    form.value.delivery_address = form.value.customer_address
  }

  router.post('/booking', form.value, {
    onFinish: () => {
      processing.value = false
    },
    onError: (errors) => {
      console.error('Booking errors:', errors)
      processing.value = false
    }
  })
}
</script>
