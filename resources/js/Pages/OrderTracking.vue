<template>
  <AppLayout>
    <!-- Header -->
    <section class="bg-muted py-12">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
          <h1 class="text-4xl font-bold text-foreground mb-4"><PERSON><PERSON>an</h1>
          <p class="text-xl text-muted-foreground">Masukkan nomor pesanan untuk melihat status booking Anda</p>
        </div>
      </div>
    </section>

    <!-- Search Form -->
    <section class="py-12 bg-background">
      <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
        <form @submit.prevent="searchOrder" class="card">
          <div class="card-content space-y-4">
            <div>
              <label class="block text-sm font-medium text-foreground mb-2"><PERSON><PERSON></label>
              <input
                v-model="searchQuery"
                type="text"
                placeholder="Contoh: GSJ2501201234"
                class="input-field"
                required
              />
              <p class="text-sm text-muted-foreground mt-1">
                Nomor pesanan dapat ditemukan di email konfirmasi booking
              </p>
            </div>
            
            <button
              type="submit"
              :disabled="!searchQuery || searching"
              class="btn-primary w-full py-3 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span v-if="searching">Mencari...</span>
              <span v-else>Lacak Pesanan</span>
            </button>
          </div>
        </form>
      </div>
    </section>

    <!-- Order Result -->
    <section v-if="order" class="py-12 bg-muted/30">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Order Header -->
        <div class="card mb-8">
          <div class="card-header">
            <div class="flex justify-between items-start">
              <div>
                <h2 class="text-2xl font-bold text-foreground">{{ order.order_number }}</h2>
                <p class="text-muted-foreground">Dibuat pada {{ formatDate(order.created_at) }}</p>
              </div>
              <div class="text-right">
                <span
                  :class="[
                    'px-3 py-1 rounded-full text-sm font-medium',
                    getStatusColor(order.status)
                  ]"
                >
                  {{ getStatusText(order.status) }}
                </span>
                <p class="text-sm text-muted-foreground mt-1">
                  Pembayaran: {{ getPaymentStatusText(order.payment_status) }}
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Order Timeline -->
        <div class="card mb-8">
          <div class="card-header">
            <h3 class="text-xl font-semibold">Status Pesanan</h3>
          </div>
          <div class="card-content">
            <div class="space-y-4">
              <div
                v-for="(step, index) in orderSteps"
                :key="index"
                class="flex items-center gap-4"
              >
                <div
                  :class="[
                    'w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium',
                    step.completed ? 'bg-primary text-primary-foreground' : 'bg-muted text-muted-foreground'
                  ]"
                >
                  <svg v-if="step.completed" class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  <span v-else>{{ index + 1 }}</span>
                </div>
                <div class="flex-1">
                  <h4 class="font-medium text-foreground">{{ step.title }}</h4>
                  <p class="text-sm text-muted-foreground">{{ step.description }}</p>
                  <p v-if="step.date" class="text-xs text-muted-foreground mt-1">{{ formatDate(step.date) }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Order Details -->
        <div class="grid lg:grid-cols-2 gap-8">
          <!-- Customer & Delivery Info -->
          <div class="card">
            <div class="card-header">
              <h3 class="text-xl font-semibold">Informasi Penyewa</h3>
            </div>
            <div class="card-content space-y-4">
              <div>
                <h4 class="font-medium text-foreground">{{ order.customer.full_name }}</h4>
                <p class="text-sm text-muted-foreground">{{ order.customer.email }}</p>
                <p class="text-sm text-muted-foreground">{{ order.customer.phone }}</p>
              </div>

              <div>
                <h4 class="font-medium text-foreground mb-2">Pengambilan</h4>
                <p class="text-sm text-muted-foreground">
                  {{ order.delivery_method === 'pickup' ? 'Ambil di Toko' : 'Antar ke Alamat' }}
                </p>
                <p v-if="order.delivery_method === 'delivery'" class="text-sm text-muted-foreground">
                  {{ order.delivery_address }}
                </p>
              </div>

              <div>
                <h4 class="font-medium text-foreground mb-2">Periode Sewa</h4>
                <p class="text-sm text-muted-foreground">
                  {{ formatDate(order.rental_start_date) }} - {{ formatDate(order.rental_end_date) }}
                </p>
                <p class="text-sm text-muted-foreground">{{ order.rental_days }} hari</p>
              </div>
            </div>
          </div>

          <!-- Order Items -->
          <div class="card">
            <div class="card-header">
              <h3 class="text-xl font-semibold">Item Pesanan</h3>
            </div>
            <div class="card-content space-y-4">
              <div v-for="item in order.items" :key="item.product_sku" class="border-b border-border pb-4 last:border-b-0">
                <h4 class="font-medium text-foreground">{{ item.product_name }}</h4>
                <p class="text-sm text-muted-foreground">SKU: {{ item.product_sku }}</p>
                <div class="flex justify-between items-center mt-2">
                  <div class="text-sm text-muted-foreground">
                    <span v-if="item.selected_size">Ukuran: {{ item.selected_size }}</span>
                    <span v-if="item.selected_color" class="ml-2">Warna: {{ item.selected_color }}</span>
                  </div>
                  <div class="text-right">
                    <p class="font-medium">Rp {{ Number(item.total_price).toLocaleString() }}</p>
                    <p class="text-sm text-muted-foreground">{{ item.rental_days }} hari</p>
                  </div>
                </div>
              </div>

              <div class="border-t border-border pt-4 space-y-2">
                <div class="flex justify-between">
                  <span class="text-muted-foreground">Subtotal:</span>
                  <span class="font-medium">Rp {{ Number(order.subtotal).toLocaleString() }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-muted-foreground">Deposit:</span>
                  <span class="font-medium">Rp {{ Number(order.deposit_amount).toLocaleString() }}</span>
                </div>
                <div class="flex justify-between text-lg font-bold">
                  <span>Total:</span>
                  <span class="text-primary">Rp {{ Number(order.total_amount).toLocaleString() }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Payment Info -->
        <div v-if="order.payments && order.payments.length > 0" class="card mt-8">
          <div class="card-header">
            <h3 class="text-xl font-semibold">Informasi Pembayaran</h3>
          </div>
          <div class="card-content">
            <div v-for="payment in order.payments" :key="payment.payment_reference" class="border-b border-border pb-4 mb-4 last:border-b-0 last:mb-0">
              <div class="flex justify-between items-start">
                <div>
                  <h4 class="font-medium text-foreground">{{ payment.payment_reference }}</h4>
                  <p class="text-sm text-muted-foreground">{{ payment.payment_method }} - {{ payment.payment_type }}</p>
                  <p v-if="payment.paid_at" class="text-sm text-muted-foreground">Dibayar: {{ formatDate(payment.paid_at) }}</p>
                </div>
                <div class="text-right">
                  <p class="font-medium">Rp {{ Number(payment.amount).toLocaleString() }}</p>
                  <span
                    :class="[
                      'px-2 py-1 rounded text-xs font-medium',
                      payment.status === 'confirmed' ? 'bg-green-100 text-green-800' : 
                      payment.status === 'pending' ? 'bg-yellow-100 text-yellow-800' : 
                      'bg-red-100 text-red-800'
                    ]"
                  >
                    {{ getPaymentStatusText(payment.status) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Contact Info -->
        <div class="card mt-8">
          <div class="card-content">
            <div class="text-center">
              <h3 class="font-semibold text-foreground mb-2">Butuh Bantuan?</h3>
              <p class="text-muted-foreground mb-4">
                Hubungi customer service kami untuk informasi lebih lanjut
              </p>
              <div class="flex justify-center gap-4">
                <a href="https://wa.me/6281234567890" class="btn-primary">
                  WhatsApp
                </a>
                <a href="mailto:<EMAIL>" class="btn-outline">
                  Email
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- No Order Found -->
    <section v-else-if="searchQuery && !searching" class="py-12 bg-background">
      <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div class="card">
          <div class="card-content">
            <svg class="w-16 h-16 text-muted-foreground mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 15c-2.34 0-4.29-1.009-5.824-2.562M15 6.306a7.962 7.962 0 00-6 0m6 0a7.962 7.962 0 016 2.562 7.962 7.962 0 01-6 2.562m0-5.124v5.124"></path>
            </svg>
            <h3 class="text-xl font-semibold text-foreground mb-2">Pesanan Tidak Ditemukan</h3>
            <p class="text-muted-foreground mb-4">
              Nomor pesanan "{{ searchQuery }}" tidak ditemukan. Pastikan nomor pesanan sudah benar.
            </p>
            <button @click="searchQuery = ''" class="btn-primary">
              Coba Lagi
            </button>
          </div>
        </div>
      </div>
    </section>
  </AppLayout>
</template>

<script setup>
import { ref, computed } from 'vue'
import { router } from '@inertiajs/vue3'
import AppLayout from '@/Layouts/AppLayout.vue'

const props = defineProps({
  order: Object,
  search_query: String,
})

const searchQuery = ref(props.search_query || '')
const searching = ref(false)

// Computed properties
const orderSteps = computed(() => {
  if (!props.order) return []

  const steps = [
    {
      title: 'Booking Dibuat',
      description: 'Pesanan booking telah dibuat',
      completed: true,
      date: props.order.created_at,
    },
    {
      title: 'Menunggu Pembayaran',
      description: 'Silakan lakukan pembayaran sesuai instruksi',
      completed: props.order.payment_status !== 'pending',
      date: null,
    },
    {
      title: 'Pembayaran Dikonfirmasi',
      description: 'Pembayaran telah dikonfirmasi',
      completed: props.order.payment_status === 'confirmed',
      date: props.order.confirmed_at,
    },
    {
      title: 'Pesanan Diproses',
      description: 'Gaun sedang disiapkan',
      completed: ['processing', 'shipped', 'delivered', 'completed'].includes(props.order.status),
      date: props.order.confirmed_at,
    },
    {
      title: 'Siap Diambil/Dikirim',
      description: props.order.delivery_method === 'pickup' ? 'Gaun siap diambil di toko' : 'Gaun sedang dikirim',
      completed: ['shipped', 'delivered', 'completed'].includes(props.order.status),
      date: props.order.shipped_at,
    },
    {
      title: 'Selesai',
      description: 'Gaun telah diterima dan pesanan selesai',
      completed: ['delivered', 'completed'].includes(props.order.status),
      date: props.order.delivered_at,
    },
  ]

  return steps
})

// Methods
const searchOrder = () => {
  if (!searchQuery.value) return

  searching.value = true
  router.get('/order-tracking', { order_number: searchQuery.value }, {
    onFinish: () => {
      searching.value = false
    }
  })
}

const formatDate = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const getStatusColor = (status) => {
  const colors = {
    'pending_payment': 'bg-yellow-100 text-yellow-800',
    'confirmed': 'bg-blue-100 text-blue-800',
    'processing': 'bg-blue-100 text-blue-800',
    'shipped': 'bg-purple-100 text-purple-800',
    'delivered': 'bg-green-100 text-green-800',
    'completed': 'bg-green-100 text-green-800',
    'cancelled': 'bg-red-100 text-red-800',
  }
  return colors[status] || 'bg-gray-100 text-gray-800'
}

const getStatusText = (status) => {
  const texts = {
    'pending_payment': 'Menunggu Pembayaran',
    'confirmed': 'Dikonfirmasi',
    'processing': 'Diproses',
    'shipped': 'Dikirim',
    'delivered': 'Diterima',
    'completed': 'Selesai',
    'cancelled': 'Dibatalkan',
  }
  return texts[status] || status
}

const getPaymentStatusText = (status) => {
  const texts = {
    'pending': 'Menunggu',
    'waiting_confirmation': 'Menunggu Konfirmasi',
    'confirmed': 'Dikonfirmasi',
    'failed': 'Gagal',
    'refunded': 'Dikembalikan',
  }
  return texts[status] || status
}
</script>
