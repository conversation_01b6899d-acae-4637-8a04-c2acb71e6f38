<template>
  <AppLayout>
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-r from-brand-light to-brand-bg py-20">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
          <h1 class="text-4xl md:text-6xl font-bold text-brand-dark mb-6">
            Gaun Syar'i Terbaik
            <span class="block text-brand-medium">di Yogyakarta</span>
          </h1>
          <p class="text-xl text-brand-dark mb-8 max-w-3xl mx-auto">
            Sewa gaun syar'i berkualitas premium untuk acara spesial Anda. 
            Koleksi terlengkap dengan desain elegan dan modern.
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <Link 
              href="/catalog" 
              class="bg-brand-dark text-white px-8 py-3 rounded-lg font-semibold hover:bg-brand-medium transition-colors"
            >
              Lihat Katalog
            </Link>
            <Link 
              href="/booking" 
              class="bg-white text-brand-dark px-8 py-3 rounded-lg font-semibold border-2 border-brand-dark hover:bg-brand-light transition-colors"
            >
              Booking Sekarang
            </Link>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="py-16 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-brand-dark mb-4">Mengapa Memilih Kami?</h2>
          <p class="text-brand-medium text-lg">Layanan terbaik untuk pengalaman sewa gaun yang tak terlupakan</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div class="text-center p-6">
            <div class="w-16 h-16 bg-brand-light rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-brand-dark" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-brand-dark mb-2">Kualitas Premium</h3>
            <p class="text-brand-medium">Gaun berkualitas tinggi dengan bahan terbaik dan desain eksklusif</p>
          </div>
          
          <div class="text-center p-6">
            <div class="w-16 h-16 bg-brand-light rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-brand-dark" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-brand-dark mb-2">Booking Mudah</h3>
            <p class="text-brand-medium">Sistem booking online yang mudah dan cepat, kapan saja</p>
          </div>
          
          <div class="text-center p-6">
            <div class="w-16 h-16 bg-brand-light rounded-full flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-brand-dark" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8h2a2 2 0 012 2v6a2 2 0 01-2 2h-2v4l-4-4H9a1.994 1.994 0 01-2-2V10a2 2 0 012-2h8z"></path>
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-brand-dark mb-2">Layanan 24/7</h3>
            <p class="text-brand-medium">Customer service siap membantu Anda kapan saja</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Popular Dresses Section -->
    <section class="py-16 bg-brand-bg">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-brand-dark mb-4">Koleksi Populer</h2>
          <p class="text-brand-medium text-lg">Gaun-gaun pilihan yang paling diminati</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div v-for="product in featuredProducts" :key="product.id" class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="aspect-w-3 aspect-h-4 bg-gray-200">
              <img :src="product.image" :alt="product.name" class="w-full h-64 object-cover">
            </div>
            <div class="p-6">
              <h3 class="text-xl font-semibold text-brand-dark mb-2">{{ product.name }}</h3>
              <p class="text-brand-medium mb-2">{{ product.category }}</p>
              <div class="flex items-center mb-4">
                <span class="text-yellow-400 mr-1">★</span>
                <span class="text-sm text-brand-medium">{{ product.rating }}</span>
                <span class="ml-2 text-sm px-2 py-1 rounded-full"
                      :class="product.status === 'Tersedia' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
                  {{ product.status }}
                </span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-2xl font-bold text-brand-dark">Rp {{ Number(product.price).toLocaleString() }}</span>
                <Link
                  :href="`/dress/${product.id}`"
                  class="bg-brand-dark text-white px-4 py-2 rounded hover:bg-brand-medium transition-colors"
                >
                  Lihat Detail
                </Link>
              </div>
            </div>
          </div>
        </div>
        
        <div class="text-center mt-12">
          <Link 
            href="/catalog" 
            class="bg-brand-dark text-white px-8 py-3 rounded-lg font-semibold hover:bg-brand-medium transition-colors"
          >
            Lihat Semua Koleksi
          </Link>
        </div>
      </div>
    </section>
  </AppLayout>
</template>

<script setup>
import { Link } from '@inertiajs/vue3'
import AppLayout from '@/Layouts/AppLayout.vue'

// Props from Laravel controller
const props = defineProps({
  featuredProducts: Array,
  categories: Array,
})
</script>
