<template>
  <AppLayout>
    <!-- Hero Section -->
    <section class="relative bg-gradient-to-br from-white to-brand-bg py-20 px-4">
      <div class="max-w-7xl mx-auto">
        <div class="grid lg:grid-cols-2 gap-12 items-center">
          <div class="space-y-8">
            <div class="space-y-4">
              <h1 class="text-4xl lg:text-6xl font-bold text-brand-dark leading-tight">
                Gaun Syar'i
                <span class="text-brand-medium block">Terbaik di Jogja</span>
              </h1>
              <p class="text-lg text-gray-600 leading-relaxed">
                Sewa gaun syar'i berkualitas premium untuk acara spesial Anda. Koleksi terlengkap dengan desain elegan
                dan modern.
              </p>
            </div>

            <div class="flex flex-col sm:flex-row gap-4">
              <Link
                href="/catalog"
                class="inline-flex items-center justify-center px-6 py-3 text-lg font-medium bg-brand-dark hover:bg-brand-medium text-white rounded-lg transition-colors"
              >
                Lihat Koleksi Terbaru
                <svg class="ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </Link>
              <Link
                href="#about"
                class="inline-flex items-center justify-center px-6 py-3 text-lg font-medium border-brand-dark text-brand-dark hover:bg-brand-dark hover:text-white bg-transparent border rounded-lg transition-colors"
              >
                Tentang Kami
              </Link>
            </div>

            <div class="flex items-center gap-8 pt-4">
              <div class="flex items-center gap-2">
                <svg class="h-5 w-5 text-brand-medium" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                </svg>
                <span class="text-sm text-gray-600">500+ Pelanggan Puas</span>
              </div>
              <div class="flex items-center gap-2">
                <svg class="h-5 w-5 text-brand-medium" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span class="text-sm text-gray-600">Premium Quality</span>
              </div>
              <div class="flex items-center gap-2">
                <svg class="h-5 w-5 text-brand-medium" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span class="text-sm text-gray-600">Booking Mudah</span>
              </div>
            </div>
          </div>

          <div class="relative">
            <div class="relative z-10">
              <img
                src="/placeholder.jpg"
                alt="Model wearing elegant Islamic dress"
                class="w-full h-96 lg:h-[600px] object-cover rounded-2xl shadow-2xl"
              />
            </div>
            <div class="absolute -top-4 -right-4 w-full h-full bg-brand-light rounded-2xl -z-10"></div>
          </div>
        </div>
      </div>
    </section>

    <!-- Featured Dresses -->
    <section class="py-20 px-4 bg-white">
      <div class="max-w-7xl mx-auto">
        <div class="text-center mb-16">
          <h2 class="text-3xl lg:text-4xl font-bold text-brand-dark mb-4">Koleksi Pilihan Terbaik</h2>
          <p class="text-lg text-gray-600 max-w-2xl mx-auto">
            Gaun syar'i premium dengan desain terkini untuk berbagai acara spesial Anda
          </p>
        </div>

        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div
            v-for="product in featuredProducts"
            :key="product.id"
            class="group hover:shadow-xl transition-all duration-300 overflow-hidden border border-brand-light rounded-lg bg-white"
          >
            <div class="relative">
              <img
                :src="product.image"
                :alt="product.name"
                class="w-full h-80 object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div class="absolute top-4 right-4">
                <span
                  :class="[
                    'px-3 py-1 rounded-full text-xs font-medium',
                    product.status === 'Tersedia' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  ]"
                >
                  {{ product.status }}
                </span>
              </div>
              <button class="absolute top-4 left-4 bg-white/80 hover:bg-white text-brand-dark p-2 rounded-lg transition-colors">
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                </svg>
              </button>
            </div>

            <div class="p-6">
              <div class="space-y-3">
                <h3 class="font-semibold text-lg text-brand-dark group-hover:text-brand-medium transition-colors">
                  {{ product.name }}
                </h3>

                <div class="flex items-center gap-2">
                  <div class="flex items-center">
                    <span v-for="i in 5" :key="i" class="text-yellow-400">
                      <svg
                        :class="[
                          'h-4 w-4',
                          i <= Math.floor(product.rating) ? 'fill-current' : 'text-gray-300'
                        ]"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                      </svg>
                    </span>
                  </div>
                  <span class="text-sm text-gray-600">({{ product.rating }})</span>
                </div>

                <div class="flex items-center justify-between">
                  <div>
                    <span class="text-2xl font-bold text-brand-medium">Rp {{ Number(product.price).toLocaleString() }}</span>
                    <span class="text-sm text-gray-500 ml-1">/hari</span>
                  </div>
                </div>

                <Link
                  :href="`/dress/${product.id}`"
                  class="w-full inline-flex items-center justify-center px-4 py-2 bg-brand-dark hover:bg-brand-medium text-white rounded-lg transition-colors"
                >
                  Lihat Detail
                </Link>
              </div>
            </div>
          </div>
        </div>

        <div class="text-center mt-12">
          <Link
            href="/catalog"
            class="inline-flex items-center justify-center px-6 py-3 text-lg font-medium border-brand-dark text-brand-dark hover:bg-brand-dark hover:text-white bg-transparent border rounded-lg transition-colors"
          >
            Lihat Semua Koleksi
            <svg class="ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
          </Link>
        </div>
      </div>
    </section>

    <!-- Testimonials -->
    <section class="py-20 px-4 bg-brand-bg">
      <div class="max-w-7xl mx-auto">
        <div class="text-center mb-16">
          <h2 class="text-3xl lg:text-4xl font-bold text-brand-dark mb-4">Testimoni Pelanggan</h2>
          <p class="text-lg text-gray-600">Kepuasan pelanggan adalah prioritas utama kami</p>
        </div>

        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div v-for="(testimonial, index) in testimonials" :key="index" class="bg-white border border-brand-light rounded-lg">
            <div class="p-6">
              <div class="space-y-4">
                <div class="flex items-center">
                  <span v-for="i in testimonial.rating" :key="i" class="text-yellow-400">
                    <svg class="h-4 w-4 fill-current" viewBox="0 0 20 20">
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                  </span>
                </div>

                <p class="text-gray-600 italic">"{{ testimonial.text }}"</p>

                <div class="border-t border-brand-light pt-4">
                  <p class="font-semibold text-brand-dark">{{ testimonial.name }}</p>
                  <p class="text-sm text-gray-500">{{ testimonial.event }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- About Section -->
    <section id="about" class="py-20 px-4 bg-white">
      <div class="max-w-7xl mx-auto">
        <div class="grid lg:grid-cols-2 gap-12 items-center">
          <div class="space-y-6">
            <h2 class="text-3xl lg:text-4xl font-bold text-brand-dark">Mengapa Memilih GaunSyariJogja.com?</h2>

            <div class="space-y-4">
              <div class="flex items-start gap-4">
                <div class="bg-brand-light p-2 rounded-lg">
                  <svg class="h-6 w-6 text-brand-dark" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
                <div>
                  <h3 class="font-semibold text-brand-dark mb-2">Kualitas Premium</h3>
                  <p class="text-gray-600">
                    Semua gaun dibuat dengan bahan berkualitas tinggi dan jahitan yang rapi
                  </p>
                </div>
              </div>

              <div class="flex items-start gap-4">
                <div class="bg-brand-light p-2 rounded-lg">
                  <svg class="h-6 w-6 text-brand-dark" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                  </svg>
                </div>
                <div>
                  <h3 class="font-semibold text-brand-dark mb-2">Pelayanan Terbaik</h3>
                  <p class="text-gray-600">Tim customer service yang ramah dan responsif siap membantu Anda</p>
                </div>
              </div>

              <div class="flex items-start gap-4">
                <div class="bg-brand-light p-2 rounded-lg">
                  <svg class="h-6 w-6 text-brand-dark" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
                <div>
                  <h3 class="font-semibold text-brand-dark mb-2">Proses Mudah</h3>
                  <p class="text-gray-600">Booking online yang mudah dengan sistem pembayaran yang fleksibel</p>
                </div>
              </div>
            </div>
          </div>

          <div class="relative">
            <img
              src="/placeholder.jpg"
              alt="GaunSyariJogja Collection"
              class="w-full h-96 lg:h-[500px] object-cover rounded-2xl shadow-xl"
            />
          </div>
        </div>
      </div>
    </section>
  </AppLayout>
</template>

<script setup>
import { Link } from '@inertiajs/vue3'
import AppLayout from '@/Layouts/AppLayout.vue'

// Props from Laravel controller
const props = defineProps({
  featuredProducts: Array,
  categories: Array,
})

// Static testimonials data
const testimonials = [
  {
    name: "Siti Aminah",
    text: "Gaun yang saya sewa sangat cantik dan berkualitas. Pelayanannya juga sangat memuaskan!",
    rating: 5,
    event: "Akad Nikah",
  },
  {
    name: "Fatimah Zahra",
    text: "Proses booking mudah dan gaun sesuai ekspektasi. Terima kasih GaunSyariJogja!",
    rating: 5,
    event: "Wisuda",
  },
  {
    name: "Khadijah Rahman",
    text: "Koleksi gaun syar'i terlengkap di Jogja. Pasti akan sewa lagi untuk acara berikutnya.",
    rating: 5,
    event: "Pesta",
  },
]
</script>
