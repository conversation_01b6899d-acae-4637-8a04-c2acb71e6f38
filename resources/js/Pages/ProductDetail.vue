<template>
  <AppLayout>
    <!-- Breadcrumb -->
    <section class="bg-muted py-4">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <nav class="flex items-center space-x-2 text-sm">
          <Link href="/" class="text-muted-foreground hover:text-foreground">Beranda</Link>
          <span class="text-muted-foreground">/</span>
          <Link href="/catalog" class="text-muted-foreground hover:text-foreground">Katalog</Link>
          <span class="text-muted-foreground">/</span>
          <span class="text-foreground font-medium">{{ product.name }}</span>
        </nav>
      </div>
    </section>

    <!-- Product Detail -->
    <section class="py-12 bg-background">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid lg:grid-cols-2 gap-12">
          <!-- Product Images -->
          <div class="space-y-4">
            <!-- Main Image -->
            <div class="aspect-square bg-muted rounded-lg overflow-hidden">
              <img
                :src="selectedImage"
                :alt="product.name"
                class="w-full h-full object-cover"
              />
            </div>

            <!-- Thumbnail Images -->
            <div v-if="product.images.length > 1" class="grid grid-cols-4 gap-2">
              <button
                v-for="image in product.images"
                :key="image.id"
                @click="selectedImage = image.image_path"
                :class="[
                  'aspect-square bg-muted rounded-lg overflow-hidden border-2 transition-colors',
                  selectedImage === image.image_path ? 'border-primary' : 'border-transparent hover:border-muted-foreground'
                ]"
              >
                <img
                  :src="image.image_path"
                  :alt="image.alt_text"
                  class="w-full h-full object-cover"
                />
              </button>
            </div>
          </div>

          <!-- Product Info -->
          <div class="space-y-6">
            <!-- Product Header -->
            <div class="space-y-4">
              <div class="flex items-center gap-2">
                <span class="text-sm text-muted-foreground">SKU: {{ product.sku }}</span>
                <span
                  :class="[
                    'px-2 py-1 rounded-full text-xs font-medium',
                    product.is_available ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  ]"
                >
                  {{ product.is_available ? 'Tersedia' : 'Tidak Tersedia' }}
                </span>
              </div>

              <h1 class="text-3xl font-bold text-foreground">{{ product.name }}</h1>

              <div class="flex items-center gap-4">
                <div class="flex items-center">
                  <span v-for="i in 5" :key="i" class="text-yellow-400">
                    <svg 
                      :class="[
                        'h-5 w-5',
                        i <= Math.floor(product.rating) ? 'fill-current' : 'text-gray-300'
                      ]"
                      fill="currentColor" 
                      viewBox="0 0 20 20"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                  </span>
                </div>
                <span class="text-sm text-muted-foreground">({{ product.rating }} dari {{ product.rating_count }} ulasan)</span>
              </div>

              <div class="space-y-2">
                <div class="flex items-baseline gap-2">
                  <span class="text-3xl font-bold text-primary">Rp {{ Number(product.price).toLocaleString() }}</span>
                  <span class="text-muted-foreground">/hari</span>
                </div>
                <p class="text-sm text-muted-foreground">
                  Deposit: Rp {{ Number(product.deposit_amount).toLocaleString() }} ({{ product.deposit_percentage }}%)
                </p>
              </div>
            </div>

            <!-- Pricing Card -->
            <div class="card">
              <div class="card-content">
                <div class="text-3xl font-bold text-primary mb-2">
                  Rp {{ Number(product.price).toLocaleString() }}
                  <span class="text-lg text-muted-foreground font-normal">/hari</span>
                </div>
                <p class="text-muted-foreground mb-4">DP: Rp {{ Number(product.deposit_amount).toLocaleString() }}</p>

                <div class="space-y-4">
                  <!-- Duration Selector -->
                  <div>
                    <label class="block text-sm font-medium text-foreground mb-2">Durasi Sewa</label>
                    <select v-model="rentalDuration" class="input-field">
                      <option value="1">1 hari</option>
                      <option value="2">2 hari</option>
                      <option value="3">3 hari</option>
                      <option value="4">4 hari</option>
                      <option value="5">5 hari</option>
                    </select>
                  </div>

                  <!-- Calendar -->
                  <div>
                    <label class="block text-sm font-medium text-foreground mb-2">Pilih Tanggal Sewa</label>
                    <div class="border rounded-lg p-3 sm:p-4 bg-background">
                      <!-- Calendar Header -->
                      <div class="flex items-center justify-between mb-4">
                        <button
                          @click="previousMonth"
                          type="button"
                          class="p-2 hover:bg-accent rounded-md transition-colors"
                        >
                          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                          </svg>
                        </button>
                        <h3 class="text-lg font-semibold">{{ currentMonthYear }}</h3>
                        <button
                          @click="nextMonth"
                          type="button"
                          class="p-2 hover:bg-accent rounded-md transition-colors"
                        >
                          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                          </svg>
                        </button>
                      </div>

                      <!-- Calendar Days Header -->
                      <div class="grid grid-cols-7 gap-1 mb-2">
                        <div
                          v-for="day in ['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab']"
                          :key="day"
                          class="h-8 sm:h-10 flex items-center justify-center text-xs sm:text-sm font-medium text-muted-foreground"
                        >
                          {{ day }}
                        </div>
                      </div>

                      <!-- Calendar Dates Grid -->
                      <div class="grid grid-cols-7 gap-1 mb-4">
                        <button
                          v-for="(date, index) in calendarDates"
                          :key="`date-${index}`"
                          type="button"
                          @click="selectDate(date)"
                          :disabled="!date.isCurrentMonth || date.isPast || !date.isAvailable"
                          :class="getDateClasses(date)"
                          :title="getDateTitle(date)"
                        >
                          {{ date.day }}
                        </button>
                      </div>

                      <!-- Legend -->
                      <div class="flex items-center justify-center gap-6 text-sm">
                        <div class="flex items-center gap-2">
                          <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                          <span>Tersedia</span>
                        </div>
                        <div class="flex items-center gap-2">
                          <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                          <span>Tidak tersedia</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Cost Summary -->
                  <div v-if="selectedStartDate && selectedEndDate" class="bg-muted p-4 rounded-lg">
                    <h4 class="font-medium text-foreground mb-2">Ringkasan Biaya</h4>
                    <div class="space-y-1 text-sm">
                      <div class="flex justify-between">
                        <span>Harga sewa ({{ rentalDays }} hari)</span>
                        <span>Rp {{ totalPrice.toLocaleString() }}</span>
                      </div>
                      <div class="flex justify-between">
                        <span>DP (Uang Muka)</span>
                        <span>Rp {{ Number(product.deposit_amount).toLocaleString() }}</span>
                      </div>
                      <div class="border-t pt-1 flex justify-between font-medium">
                        <span>Total Pembayaran Awal</span>
                        <span>Rp {{ (totalPrice + Number(product.deposit_amount)).toLocaleString() }}</span>
                      </div>
                    </div>
                  </div>

                  <!-- Book Button -->
                  <button
                    v-if="product.is_available"
                    @click="addToBooking"
                    :disabled="!canBook"
                    class="btn-primary w-full text-lg py-6 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Pesan Sekarang
                  </button>
                  <button
                    v-else
                    disabled
                    class="btn-outline w-full text-lg py-6 opacity-50 cursor-not-allowed"
                  >
                    Tidak Tersedia
                  </button>
                </div>
              </div>
            </div>

            <!-- Additional Info Cards -->
            <div class="grid grid-cols-3 gap-4">
              <div class="text-center p-4 card">
                <div class="bg-primary/10 p-2 rounded-lg w-fit mx-auto mb-2">
                  <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                  </svg>
                </div>
                <p class="text-sm font-medium">Garansi Kualitas</p>
              </div>
              <div class="text-center p-4 card">
                <div class="bg-primary/10 p-2 rounded-lg w-fit mx-auto mb-2">
                  <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"></path>
                  </svg>
                </div>
                <p class="text-sm font-medium">Antar Jemput</p>
              </div>
              <div class="text-center p-4 card">
                <div class="bg-primary/10 p-2 rounded-lg w-fit mx-auto mb-2">
                  <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
                <p class="text-sm font-medium">Konsultasi Gratis</p>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="space-y-3">
              <button
                v-if="product.is_available"
                @click="addToBooking"
                :disabled="!canBook"
                class="btn-primary w-full text-lg py-3 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                </svg>
                Booking Sekarang
              </button>
              <button
                v-else
                disabled
                class="btn-outline w-full text-lg py-3 opacity-50 cursor-not-allowed"
              >
                Tidak Tersedia
              </button>

              <div class="flex gap-3">
                <button class="btn-outline flex-1 flex items-center justify-center">
                  <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                  </svg>
                  Favorit
                </button>
                <button @click="shareProduct" class="btn-outline flex-1 flex items-center justify-center">
                  <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                  </svg>
                  Bagikan
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Tabs Section -->
    <section class="py-12 bg-background">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Tab Navigation -->
        <div class="border-b border-border mb-6">
          <nav class="flex space-x-8">
            <button
              v-for="tab in tabs"
              :key="tab.id"
              @click="activeTab = tab.id"
              :class="[
                'py-2 px-1 border-b-2 font-medium text-sm transition-colors',
                activeTab === tab.id
                  ? 'border-primary text-primary'
                  : 'border-transparent text-muted-foreground hover:text-foreground hover:border-muted-foreground'
              ]"
            >
              {{ tab.label }}
            </button>
          </nav>
        </div>

        <!-- Tab Content -->
        <div class="tab-content">
          <!-- Description Tab -->
          <div v-if="activeTab === 'description'" class="card">
            <div class="card-header">
              <h2 class="text-2xl font-bold">Deskripsi Produk</h2>
            </div>
            <div class="card-content space-y-4">
              <p class="text-muted-foreground leading-relaxed">{{ product.description }}</p>

              <div class="grid md:grid-cols-2 gap-6 pt-4">
                <div>
                  <h3 class="font-semibold mb-3">Detail Produk</h3>
                  <dl class="space-y-2">
                    <div class="flex justify-between">
                      <dt class="text-muted-foreground">Kategori:</dt>
                      <dd class="font-medium">{{ product.category }}</dd>
                    </div>
                    <div class="flex justify-between">
                      <dt class="text-muted-foreground">Warna Utama:</dt>
                      <dd class="font-medium">{{ product.color }}</dd>
                    </div>
                    <div class="flex justify-between">
                      <dt class="text-muted-foreground">Motif:</dt>
                      <dd class="font-medium">{{ product.motif }}</dd>
                    </div>
                    <div class="flex justify-between">
                      <dt class="text-muted-foreground">Kondisi:</dt>
                      <dd class="font-medium capitalize">{{ product.condition }}</dd>
                    </div>
                    <div v-if="product.sizes && product.sizes.length > 0" class="flex justify-between">
                      <dt class="text-muted-foreground">Ukuran Tersedia:</dt>
                      <dd class="font-medium">{{ product.sizes.join(', ') }}</dd>
                    </div>
                    <div v-if="product.colors && product.colors.length > 0" class="flex justify-between">
                      <dt class="text-muted-foreground">Pilihan Warna:</dt>
                      <dd class="font-medium">{{ product.colors.join(', ') }}</dd>
                    </div>
                  </dl>
                </div>

                <div>
                  <h3 class="font-semibold mb-3">Informasi Sewa</h3>
                  <dl class="space-y-2">
                    <div class="flex justify-between">
                      <dt class="text-muted-foreground">Harga per Hari:</dt>
                      <dd class="font-medium">Rp {{ Number(product.price).toLocaleString() }}</dd>
                    </div>
                    <div class="flex justify-between">
                      <dt class="text-muted-foreground">Deposit:</dt>
                      <dd class="font-medium">{{ product.deposit_percentage }}%</dd>
                    </div>
                    <div class="flex justify-between">
                      <dt class="text-muted-foreground">Rating:</dt>
                      <dd class="font-medium">{{ product.rating }}/5 ({{ product.rating_count }} ulasan)</dd>
                    </div>
                    <div class="flex justify-between">
                      <dt class="text-muted-foreground">Total Disewa:</dt>
                      <dd class="font-medium">{{ product.total_rentals || 0 }} kali</dd>
                    </div>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <!-- Size Guide Tab -->
          <div v-if="activeTab === 'size-guide'" class="card">
            <div class="card-header">
              <h2 class="text-2xl font-bold">Panduan Ukuran</h2>
            </div>
            <div class="card-content">
              <div class="overflow-x-auto">
                <table class="w-full border-collapse border border-border">
                  <thead>
                    <tr class="bg-muted">
                      <th class="border border-border px-4 py-2 text-left">Ukuran</th>
                      <th class="border border-border px-4 py-2 text-left">Lingkar Dada (cm)</th>
                      <th class="border border-border px-4 py-2 text-left">Lingkar Pinggang (cm)</th>
                      <th class="border border-border px-4 py-2 text-left">Panjang Gaun (cm)</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td class="border border-border px-4 py-2 font-medium">S</td>
                      <td class="border border-border px-4 py-2">86-90</td>
                      <td class="border border-border px-4 py-2">66-70</td>
                      <td class="border border-border px-4 py-2">140</td>
                    </tr>
                    <tr>
                      <td class="border border-border px-4 py-2 font-medium">M</td>
                      <td class="border border-border px-4 py-2">90-94</td>
                      <td class="border border-border px-4 py-2">70-74</td>
                      <td class="border border-border px-4 py-2">142</td>
                    </tr>
                    <tr>
                      <td class="border border-border px-4 py-2 font-medium">L</td>
                      <td class="border border-border px-4 py-2">94-98</td>
                      <td class="border border-border px-4 py-2">74-78</td>
                      <td class="border border-border px-4 py-2">144</td>
                    </tr>
                    <tr>
                      <td class="border border-border px-4 py-2 font-medium">XL</td>
                      <td class="border border-border px-4 py-2">98-102</td>
                      <td class="border border-border px-4 py-2">78-82</td>
                      <td class="border border-border px-4 py-2">146</td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <p class="text-sm text-muted-foreground mt-4">
                * Ukuran dapat bervariasi ±2cm. Untuk konsultasi ukuran yang lebih akurat, silakan hubungi customer service kami.
              </p>
            </div>
          </div>

          <!-- Reviews Tab -->
          <div v-if="activeTab === 'reviews'" class="card">
            <div class="card-header">
              <h2 class="text-2xl font-bold">Ulasan Pelanggan</h2>
            </div>
            <div class="card-content">
              <div class="space-y-6">
                <div v-for="(review, index) in sampleReviews" :key="index" class="border-b border-border pb-4 last:border-b-0">
                  <div class="flex items-center justify-between mb-2">
                    <h5 class="font-medium text-foreground">{{ review.name }}</h5>
                    <span class="text-sm text-muted-foreground">{{ review.date }}</span>
                  </div>
                  <div class="flex items-center mb-2">
                    <span v-for="i in 5" :key="i" class="text-yellow-400">
                      <svg
                        :class="[
                          'h-4 w-4',
                          i <= review.rating ? 'fill-current' : 'text-gray-300'
                        ]"
                        fill="currentColor"
                        viewBox="0 0 20 20"
                      >
                        <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                      </svg>
                    </span>
                  </div>
                  <p class="text-muted-foreground">{{ review.comment }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Related Products -->
    <section v-if="relatedProducts.length > 0" class="py-12 bg-background">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-8">
          <h2 class="text-3xl font-bold text-foreground mb-4">Produk Serupa</h2>
          <p class="text-muted-foreground">Gaun lain yang mungkin Anda sukai</p>
        </div>

        <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div
            v-for="relatedProduct in relatedProducts"
            :key="relatedProduct.id"
            class="card group hover:card-shadow-lg transition-all duration-300 animate-fade-in"
          >
            <div class="relative overflow-hidden rounded-t-lg">
              <img
                :src="relatedProduct.image"
                :alt="relatedProduct.name"
                class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
              />
            </div>

            <div class="card-content">
              <h3 class="font-semibold text-foreground mb-2 line-clamp-2">{{ relatedProduct.name }}</h3>

              <div class="flex items-center mb-3">
                <div class="flex items-center">
                  <span v-for="i in 5" :key="i" class="text-yellow-400">
                    <svg
                      :class="[
                        'h-4 w-4',
                        i <= Math.floor(relatedProduct.rating) ? 'fill-current' : 'text-gray-300'
                      ]"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                  </span>
                </div>
                <span class="text-sm text-muted-foreground ml-2">({{ relatedProduct.rating }})</span>
              </div>

              <div class="flex items-center justify-between">
                <div>
                  <span class="text-lg font-bold text-primary">Rp {{ Number(relatedProduct.price).toLocaleString() }}</span>
                  <span class="text-sm text-muted-foreground">/hari</span>
                </div>
              </div>

              <Link
                :href="`/product/${relatedProduct.sku}`"
                class="btn-outline w-full mt-3 inline-flex items-center justify-center"
              >
                Lihat Detail
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  </AppLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import AppLayout from '@/Layouts/AppLayout.vue'

const props = defineProps({
  product: Object,
  relatedProducts: Array,
})

// Reactive data
const selectedImage = ref('')
const selectedStartDate = ref(null)
const selectedEndDate = ref(null)
const currentDate = ref(new Date())
const selectingStartDate = ref(true)
const rentalDuration = ref(1)
const activeTab = ref('description')

// Availability data from API
const unavailableDates = ref([])
const loadingAvailability = ref(false)

// Tab data
const tabs = ref([
  { id: 'description', label: 'Deskripsi' },
  { id: 'size-guide', label: 'Panduan Ukuran' },
  { id: 'reviews', label: 'Ulasan' }
])

// Sample reviews data
const sampleReviews = ref([
  {
    name: 'Siti Aminah',
    rating: 5,
    date: '15 Februari 2024',
    comment: 'Gaun sangat cantik dan berkualitas. Sesuai dengan foto dan deskripsi. Pelayanan juga sangat baik!'
  },
  {
    name: 'Fatimah Zahra',
    rating: 5,
    date: '10 Februari 2024',
    comment: 'Alhamdulillah gaun untuk akad nikah saya sangat sempurna. Terima kasih GaunSyariJogja!'
  },
  {
    name: 'Khadijah Rahman',
    rating: 4,
    date: '5 Februari 2024',
    comment: 'Gaun bagus, hanya saja pengiriman agak terlambat. Tapi overall puas dengan kualitasnya.'
  }
])

// Computed properties
const currentMonthYear = computed(() => {
  return currentDate.value.toLocaleDateString('id-ID', {
    month: 'long',
    year: 'numeric'
  })
})

const calendarDates = computed(() => {
  const year = currentDate.value.getFullYear()
  const month = currentDate.value.getMonth()

  const firstDay = new Date(year, month, 1)
  const lastDay = new Date(year, month + 1, 0)
  const daysInMonth = lastDay.getDate()
  const startingDayOfWeek = firstDay.getDay()

  const dates = []
  const today = new Date()
  today.setHours(0, 0, 0, 0)

  // Previous month's days
  const prevMonthLastDay = new Date(year, month, 0).getDate()
  for (let i = startingDayOfWeek - 1; i >= 0; i--) {
    const day = prevMonthLastDay - i
    dates.push({
      day,
      date: new Date(year, month - 1, day),
      isCurrentMonth: false,
      isPast: true,
      isAvailable: false,
      isSelected: false,
      isInRange: false
    })
  }

  // Current month's days
  for (let day = 1; day <= daysInMonth; day++) {
    const date = new Date(year, month, day)
    const dateString = date.toISOString().split('T')[0]
    const isPast = date < today
    const isUnavailable = unavailableDates.value.includes(dateString)
    const isAvailable = !isPast && !isUnavailable

    let isSelected = false
    let isInRange = false

    if (selectedStartDate.value) {
      const startDate = new Date(selectedStartDate.value)
      startDate.setHours(0, 0, 0, 0)
      const currentDateCopy = new Date(date)
      currentDateCopy.setHours(0, 0, 0, 0)

      if (selectedEndDate.value) {
        const endDate = new Date(selectedEndDate.value)
        endDate.setHours(0, 0, 0, 0)

        isSelected = currentDateCopy.getTime() === startDate.getTime() ||
                    currentDateCopy.getTime() === endDate.getTime()
        isInRange = currentDateCopy > startDate && currentDateCopy < endDate
      } else {
        isSelected = currentDateCopy.getTime() === startDate.getTime()

        // Show preview of duration range
        const previewEnd = new Date(startDate)
        previewEnd.setDate(previewEnd.getDate() + rentalDuration.value - 1)
        isInRange = currentDateCopy > startDate && currentDateCopy <= previewEnd
      }
    }

    dates.push({
      day,
      date,
      isCurrentMonth: true,
      isPast,
      isAvailable,
      isSelected,
      isInRange
    })
  }

  // Next month's days to complete the grid
  const remainingCells = 42 - dates.length
  for (let day = 1; day <= remainingCells; day++) {
    dates.push({
      day,
      date: new Date(year, month + 1, day),
      isCurrentMonth: false,
      isPast: false,
      isAvailable: false,
      isSelected: false,
      isInRange: false
    })
  }

  return dates
})

const rentalDays = computed(() => {
  if (!selectedStartDate.value) return rentalDuration.value

  if (selectedEndDate.value) {
    const start = new Date(selectedStartDate.value)
    const end = new Date(selectedEndDate.value)
    const diffTime = Math.abs(end - start)
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays > 0 ? diffDays : rentalDuration.value
  }

  return rentalDuration.value
})

const totalPrice = computed(() => {
  return rentalDays.value * props.product.price
})

const canBook = computed(() => {
  return selectedStartDate.value &&
         selectedEndDate.value &&
         rentalDays.value > 0
})

// Methods
const loadAvailability = async () => {
  loadingAvailability.value = true
  try {
    const year = currentDate.value.getFullYear()
    const month = currentDate.value.getMonth() + 1

    const response = await fetch(`/api/product/${props.product.sku}/availability?year=${year}&month=${month}`)
    const data = await response.json()

    unavailableDates.value = data.unavailable_dates || []
  } catch (error) {
    console.error('Error loading availability:', error)
    unavailableDates.value = []
  } finally {
    loadingAvailability.value = false
  }
}

const previousMonth = () => {
  currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() - 1, 1)
  loadAvailability()
}

const nextMonth = () => {
  currentDate.value = new Date(currentDate.value.getFullYear(), currentDate.value.getMonth() + 1, 1)
  loadAvailability()
}

const getDateClasses = (date) => {
  const baseClasses = 'h-8 sm:h-10 w-full text-xs sm:text-sm rounded-md transition-colors flex items-center justify-center font-medium'

  if (!date.isCurrentMonth) {
    return `${baseClasses} text-muted-foreground/30 cursor-not-allowed`
  }

  if (date.isPast) {
    return `${baseClasses} text-muted-foreground/50 cursor-not-allowed bg-muted/30`
  }

  if (!date.isAvailable) {
    return `${baseClasses} bg-red-100 text-red-700 cursor-not-allowed`
  }

  if (date.isSelected) {
    return `${baseClasses} bg-primary text-primary-foreground font-semibold`
  }

  if (date.isInRange) {
    return `${baseClasses} bg-primary/20 text-primary font-medium`
  }

  return `${baseClasses} hover:bg-accent hover:text-accent-foreground bg-background border border-border hover:border-primary/50`
}

const getDateTitle = (date) => {
  if (!date.isCurrentMonth) return ''

  const dateStr = date.date.toLocaleDateString('id-ID', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })

  if (date.isPast) return `${dateStr} - Sudah berlalu`
  if (!date.isAvailable) return `${dateStr} - Tidak tersedia`
  if (date.isSelected) return `${dateStr} - Dipilih`

  return `${dateStr} - Tersedia`
}

const selectDate = (dateObj) => {
  if (!dateObj.isCurrentMonth || dateObj.isPast || !dateObj.isAvailable) return

  // Set start date
  selectedStartDate.value = new Date(dateObj.date)

  // Calculate end date based on rental duration
  const endDate = new Date(dateObj.date)
  endDate.setDate(endDate.getDate() + rentalDuration.value - 1)

  // Check if all dates in the range are available
  const isRangeAvailable = checkDateRangeAvailability(dateObj.date, endDate)

  if (isRangeAvailable) {
    selectedEndDate.value = endDate
  } else {
    // Find the first available range starting from selected date
    const availableEndDate = findAvailableEndDate(dateObj.date, rentalDuration.value)
    if (availableEndDate) {
      selectedEndDate.value = availableEndDate
    } else {
      // If no available range found, clear selection
      selectedStartDate.value = null
      selectedEndDate.value = null
      alert('Tidak ada periode yang tersedia untuk durasi yang dipilih. Silakan pilih tanggal lain atau kurangi durasi sewa.')
    }
  }
}

const checkDateRangeAvailability = (startDate, endDate) => {
  const current = new Date(startDate)
  const end = new Date(endDate)

  while (current <= end) {
    const dateString = current.toISOString().split('T')[0]
    if (unavailableDates.value.includes(dateString)) {
      return false
    }
    current.setDate(current.getDate() + 1)
  }
  return true
}

const findAvailableEndDate = (startDate, duration) => {
  const maxAttempts = 30 // Maximum days to search forward
  let currentStart = new Date(startDate)

  for (let i = 0; i < maxAttempts; i++) {
    const potentialEnd = new Date(currentStart)
    potentialEnd.setDate(potentialEnd.getDate() + duration - 1)

    if (checkDateRangeAvailability(currentStart, potentialEnd)) {
      return potentialEnd
    }

    currentStart.setDate(currentStart.getDate() + 1)
  }

  return null
}

const formatDate = (date) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString('id-ID', {
    day: 'numeric',
    month: 'long',
    year: 'numeric'
  })
}

const shareProduct = () => {
  if (navigator.share) {
    navigator.share({
      title: props.product.name,
      text: `Lihat gaun syar'i ${props.product.name} di Gaun Syari Jogja`,
      url: window.location.href
    })
  } else {
    // Fallback: copy to clipboard
    navigator.clipboard.writeText(window.location.href)
    alert('Link produk telah disalin ke clipboard!')
  }
}

const addToBooking = () => {
  if (!canBook.value) return

  const bookingData = {
    product_id: props.product.id,
    product_sku: props.product.sku,
    rental_start_date: selectedStartDate.value.toISOString().split('T')[0],
    rental_end_date: selectedEndDate.value.toISOString().split('T')[0],
    rental_days: rentalDays.value,
    unit_price: props.product.price,
    total_price: totalPrice.value,
    deposit_amount: props.product.deposit_amount,
  }

  // Redirect to booking page with data
  router.post('/booking', bookingData)
}

// Initialize
onMounted(() => {
  // Set default image
  selectedImage.value = props.product.images[0]?.image_path || '/placeholder.jpg'

  // Load initial availability
  loadAvailability()
})
</script>
