<template>
  <AppLayout>
    <!-- Breadcrumb -->
    <section class="bg-muted py-4">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <nav class="flex items-center space-x-2 text-sm">
          <Link href="/" class="text-muted-foreground hover:text-foreground">Beranda</Link>
          <span class="text-muted-foreground">/</span>
          <Link href="/catalog" class="text-muted-foreground hover:text-foreground">Katalog</Link>
          <span class="text-muted-foreground">/</span>
          <span class="text-foreground font-medium">{{ product.name }}</span>
        </nav>
      </div>
    </section>

    <!-- Product Detail -->
    <section class="py-12 bg-background">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid lg:grid-cols-2 gap-12">
          <!-- Product Images -->
          <div class="space-y-4">
            <!-- Main Image -->
            <div class="aspect-square bg-muted rounded-lg overflow-hidden">
              <img
                :src="selectedImage"
                :alt="product.name"
                class="w-full h-full object-cover"
              />
            </div>

            <!-- Thumbnail Images -->
            <div v-if="product.images.length > 1" class="grid grid-cols-4 gap-2">
              <button
                v-for="image in product.images"
                :key="image.id"
                @click="selectedImage = image.image_path"
                :class="[
                  'aspect-square bg-muted rounded-lg overflow-hidden border-2 transition-colors',
                  selectedImage === image.image_path ? 'border-primary' : 'border-transparent hover:border-muted-foreground'
                ]"
              >
                <img
                  :src="image.image_path"
                  :alt="image.alt_text"
                  class="w-full h-full object-cover"
                />
              </button>
            </div>
          </div>

          <!-- Product Info -->
          <div class="space-y-6">
            <!-- Product Header -->
            <div class="space-y-4">
              <div class="flex items-center gap-2">
                <span class="text-sm text-muted-foreground">SKU: {{ product.sku }}</span>
                <span
                  :class="[
                    'px-2 py-1 rounded-full text-xs font-medium',
                    product.is_available ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  ]"
                >
                  {{ product.is_available ? 'Tersedia' : 'Tidak Tersedia' }}
                </span>
              </div>

              <h1 class="text-3xl font-bold text-foreground">{{ product.name }}</h1>

              <div class="flex items-center gap-4">
                <div class="flex items-center">
                  <span v-for="i in 5" :key="i" class="text-yellow-400">
                    <svg 
                      :class="[
                        'h-5 w-5',
                        i <= Math.floor(product.rating) ? 'fill-current' : 'text-gray-300'
                      ]"
                      fill="currentColor" 
                      viewBox="0 0 20 20"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                  </span>
                </div>
                <span class="text-sm text-muted-foreground">({{ product.rating }} dari {{ product.rating_count }} ulasan)</span>
              </div>

              <div class="space-y-2">
                <div class="flex items-baseline gap-2">
                  <span class="text-3xl font-bold text-primary">Rp {{ Number(product.price).toLocaleString() }}</span>
                  <span class="text-muted-foreground">/hari</span>
                </div>
                <p class="text-sm text-muted-foreground">
                  Deposit: Rp {{ Number(product.deposit_amount).toLocaleString() }} ({{ product.deposit_percentage }}%)
                </p>
              </div>
            </div>

            <!-- Product Options -->
            <div class="space-y-4">
              <!-- Size Selection -->
              <div v-if="product.sizes && product.sizes.length > 0">
                <label class="block text-sm font-medium text-foreground mb-2">Ukuran</label>
                <div class="flex flex-wrap gap-2">
                  <button
                    v-for="size in product.sizes"
                    :key="size"
                    @click="selectedSize = size"
                    :class="[
                      'px-4 py-2 border rounded-md text-sm font-medium transition-colors',
                      selectedSize === size
                        ? 'border-primary bg-primary text-primary-foreground'
                        : 'border-input bg-background hover:bg-accent hover:text-accent-foreground'
                    ]"
                  >
                    {{ size }}
                  </button>
                </div>
              </div>

              <!-- Color Selection -->
              <div v-if="product.colors && product.colors.length > 0">
                <label class="block text-sm font-medium text-foreground mb-2">Warna</label>
                <div class="flex flex-wrap gap-2">
                  <button
                    v-for="color in product.colors"
                    :key="color"
                    @click="selectedColor = color"
                    :class="[
                      'px-4 py-2 border rounded-md text-sm font-medium transition-colors',
                      selectedColor === color
                        ? 'border-primary bg-primary text-primary-foreground'
                        : 'border-input bg-background hover:bg-accent hover:text-accent-foreground'
                    ]"
                  >
                    {{ color }}
                  </button>
                </div>
              </div>

              <!-- Rental Period -->
              <div>
                <label class="block text-sm font-medium text-foreground mb-2">Periode Sewa</label>
                <div class="grid grid-cols-2 gap-4">
                  <div>
                    <label class="block text-xs text-muted-foreground mb-1">Tanggal Mulai</label>
                    <input
                      v-model="rentalStartDate"
                      type="date"
                      :min="minDate"
                      class="input-field"
                    />
                  </div>
                  <div>
                    <label class="block text-xs text-muted-foreground mb-1">Tanggal Selesai</label>
                    <input
                      v-model="rentalEndDate"
                      type="date"
                      :min="rentalStartDate || minDate"
                      class="input-field"
                    />
                  </div>
                </div>
                <p v-if="rentalDays > 0" class="text-sm text-muted-foreground mt-2">
                  Durasi: {{ rentalDays }} hari - Total: Rp {{ totalPrice.toLocaleString() }}
                </p>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="space-y-3">
              <button
                v-if="product.is_available"
                @click="addToBooking"
                :disabled="!canBook"
                class="btn-primary w-full text-lg py-3 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                </svg>
                Booking Sekarang
              </button>
              <button
                v-else
                disabled
                class="btn-outline w-full text-lg py-3 opacity-50 cursor-not-allowed"
              >
                Tidak Tersedia
              </button>

              <div class="flex gap-3">
                <button class="btn-outline flex-1 flex items-center justify-center">
                  <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                  </svg>
                  Favorit
                </button>
                <button class="btn-outline flex-1 flex items-center justify-center">
                  <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                  </svg>
                  Bagikan
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Product Description & Details -->
    <section class="py-12 bg-muted/30">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid lg:grid-cols-3 gap-8">
          <!-- Description -->
          <div class="lg:col-span-2">
            <div class="card">
              <div class="card-header">
                <h2 class="text-2xl font-bold">Deskripsi Produk</h2>
              </div>
              <div class="card-content space-y-4">
                <p class="text-muted-foreground leading-relaxed">{{ product.description }}</p>

                <div class="grid md:grid-cols-2 gap-6 pt-4">
                  <div>
                    <h3 class="font-semibold mb-3">Detail Produk</h3>
                    <dl class="space-y-2">
                      <div class="flex justify-between">
                        <dt class="text-muted-foreground">Kategori:</dt>
                        <dd class="font-medium">{{ product.category }}</dd>
                      </div>
                      <div class="flex justify-between">
                        <dt class="text-muted-foreground">Warna Utama:</dt>
                        <dd class="font-medium">{{ product.color }}</dd>
                      </div>
                      <div class="flex justify-between">
                        <dt class="text-muted-foreground">Motif:</dt>
                        <dd class="font-medium">{{ product.motif }}</dd>
                      </div>
                      <div class="flex justify-between">
                        <dt class="text-muted-foreground">Kondisi:</dt>
                        <dd class="font-medium capitalize">{{ product.condition }}</dd>
                      </div>
                    </dl>
                  </div>

                  <div>
                    <h3 class="font-semibold mb-3">Informasi Sewa</h3>
                    <dl class="space-y-2">
                      <div class="flex justify-between">
                        <dt class="text-muted-foreground">Harga per Hari:</dt>
                        <dd class="font-medium">Rp {{ Number(product.price).toLocaleString() }}</dd>
                      </div>
                      <div class="flex justify-between">
                        <dt class="text-muted-foreground">Deposit:</dt>
                        <dd class="font-medium">{{ product.deposit_percentage }}%</dd>
                      </div>
                      <div class="flex justify-between">
                        <dt class="text-muted-foreground">Rating:</dt>
                        <dd class="font-medium">{{ product.rating }}/5 ({{ product.rating_count }} ulasan)</dd>
                      </div>
                      <div class="flex justify-between">
                        <dt class="text-muted-foreground">Total Disewa:</dt>
                        <dd class="font-medium">{{ product.total_rentals || 0 }} kali</dd>
                      </div>
                    </dl>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Rental Info -->
          <div>
            <div class="card">
              <div class="card-header">
                <h3 class="text-lg font-semibold">Informasi Penyewaan</h3>
              </div>
              <div class="card-content space-y-4">
                <div class="space-y-3">
                  <div class="flex items-start gap-3">
                    <div class="bg-primary/10 p-2 rounded-lg">
                      <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                    </div>
                    <div>
                      <h4 class="font-medium">Kualitas Terjamin</h4>
                      <p class="text-sm text-muted-foreground">Gaun telah melalui quality control ketat</p>
                    </div>
                  </div>

                  <div class="flex items-start gap-3">
                    <div class="bg-primary/10 p-2 rounded-lg">
                      <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                      </svg>
                    </div>
                    <div>
                      <h4 class="font-medium">Fleksibel</h4>
                      <p class="text-sm text-muted-foreground">Minimal sewa 1 hari, maksimal 7 hari</p>
                    </div>
                  </div>

                  <div class="flex items-start gap-3">
                    <div class="bg-primary/10 p-2 rounded-lg">
                      <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                      </svg>
                    </div>
                    <div>
                      <h4 class="font-medium">Pembayaran Mudah</h4>
                      <p class="text-sm text-muted-foreground">Transfer bank, e-wallet, atau cash</p>
                    </div>
                  </div>

                  <div class="flex items-start gap-3">
                    <div class="bg-primary/10 p-2 rounded-lg">
                      <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"></path>
                      </svg>
                    </div>
                    <div>
                      <h4 class="font-medium">Antar Jemput</h4>
                      <p class="text-sm text-muted-foreground">Layanan antar jemput area Yogyakarta</p>
                    </div>
                  </div>
                </div>

                <div class="pt-4 border-t">
                  <p class="text-sm text-muted-foreground">
                    <strong>Catatan:</strong> Deposit akan dikembalikan setelah gaun dikembalikan dalam kondisi baik.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Related Products -->
    <section v-if="relatedProducts.length > 0" class="py-12 bg-background">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-8">
          <h2 class="text-3xl font-bold text-foreground mb-4">Produk Serupa</h2>
          <p class="text-muted-foreground">Gaun lain yang mungkin Anda sukai</p>
        </div>

        <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div
            v-for="relatedProduct in relatedProducts"
            :key="relatedProduct.id"
            class="card group hover:card-shadow-lg transition-all duration-300 animate-fade-in"
          >
            <div class="relative overflow-hidden rounded-t-lg">
              <img
                :src="relatedProduct.image"
                :alt="relatedProduct.name"
                class="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
              />
            </div>

            <div class="card-content">
              <h3 class="font-semibold text-foreground mb-2 line-clamp-2">{{ relatedProduct.name }}</h3>

              <div class="flex items-center mb-3">
                <div class="flex items-center">
                  <span v-for="i in 5" :key="i" class="text-yellow-400">
                    <svg
                      :class="[
                        'h-4 w-4',
                        i <= Math.floor(relatedProduct.rating) ? 'fill-current' : 'text-gray-300'
                      ]"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                    </svg>
                  </span>
                </div>
                <span class="text-sm text-muted-foreground ml-2">({{ relatedProduct.rating }})</span>
              </div>

              <div class="flex items-center justify-between">
                <div>
                  <span class="text-lg font-bold text-primary">Rp {{ Number(relatedProduct.price).toLocaleString() }}</span>
                  <span class="text-sm text-muted-foreground">/hari</span>
                </div>
              </div>

              <Link
                :href="`/product/${relatedProduct.sku}`"
                class="btn-outline w-full mt-3 inline-flex items-center justify-center"
              >
                Lihat Detail
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  </AppLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import AppLayout from '@/Layouts/AppLayout.vue'

const props = defineProps({
  product: Object,
  relatedProducts: Array,
})

// Reactive data
const selectedImage = ref('')
const selectedSize = ref('')
const selectedColor = ref('')
const rentalStartDate = ref('')
const rentalEndDate = ref('')

// Computed properties
const minDate = computed(() => {
  const today = new Date()
  return today.toISOString().split('T')[0]
})

const rentalDays = computed(() => {
  if (!rentalStartDate.value || !rentalEndDate.value) return 0
  
  const start = new Date(rentalStartDate.value)
  const end = new Date(rentalEndDate.value)
  const diffTime = Math.abs(end - start)
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  return diffDays > 0 ? diffDays : 0
})

const totalPrice = computed(() => {
  return rentalDays.value * props.product.price
})

const canBook = computed(() => {
  return rentalStartDate.value && 
         rentalEndDate.value && 
         rentalDays.value > 0 &&
         (props.product.sizes?.length === 0 || selectedSize.value) &&
         (props.product.colors?.length === 0 || selectedColor.value)
})

// Methods
const addToBooking = () => {
  if (!canBook.value) return

  const bookingData = {
    product_id: props.product.id,
    product_sku: props.product.sku,
    rental_start_date: rentalStartDate.value,
    rental_end_date: rentalEndDate.value,
    rental_days: rentalDays.value,
    selected_size: selectedSize.value,
    selected_color: selectedColor.value,
    unit_price: props.product.price,
    total_price: totalPrice.value,
    deposit_amount: props.product.deposit_amount,
  }

  // Redirect to booking page with data
  router.post('/booking', bookingData)
}

// Initialize
onMounted(() => {
  // Set default image
  selectedImage.value = props.product.images[0]?.image_path || '/placeholder.jpg'
  
  // Set default selections
  if (props.product.sizes?.length > 0) {
    selectedSize.value = props.product.sizes[0]
  }
  if (props.product.colors?.length > 0) {
    selectedColor.value = props.product.colors[0]
  }
})
</script>
