<template>
  <AppLayout>
    <!-- Header -->
    <section class="bg-brand-light py-12">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
          <h1 class="text-4xl font-bold text-brand-dark mb-4">Katalog Gaun Syar'i</h1>
          <p class="text-xl text-brand-medium">Temukan gaun syar'i impian Anda untuk acara spesial</p>
        </div>
      </div>
    </section>

    <!-- Filters and Search -->
    <section class="py-8 bg-white border-b">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col lg:flex-row gap-4 items-center justify-between">
          <!-- Search -->
          <div class="flex-1 max-w-md">
            <input
              v-model="searchQuery"
              @input="updateFilters"
              type="text"
              placeholder="Cari gaun..."
              class="w-full px-4 py-2 border border-brand-light rounded-lg focus:ring-2 focus:ring-brand-medium focus:border-transparent"
            >
          </div>

          <!-- Category Filter -->
          <div class="flex gap-4">
            <select
              v-model="selectedCategory"
              @change="updateFilters"
              class="px-4 py-2 border border-brand-light rounded-lg focus:ring-2 focus:ring-brand-medium"
            >
              <option value="">Semua Kategori</option>
              <option v-for="category in categories" :key="category.id" :value="category.slug">
                {{ category.name }} ({{ category.active_products_count }})
              </option>
            </select>

            <!-- Status Filter -->
            <select
              v-model="selectedStatus"
              @change="updateFilters"
              class="px-4 py-2 border border-brand-light rounded-lg focus:ring-2 focus:ring-brand-medium"
            >
              <option value="">Semua Status</option>
              <option value="available">Tersedia</option>
              <option value="rented">Disewa</option>
            </select>

            <!-- Sort -->
            <select
              v-model="sortBy"
              @change="updateFilters"
              class="px-4 py-2 border border-brand-light rounded-lg focus:ring-2 focus:ring-brand-medium"
            >
              <option value="name">Nama A-Z</option>
              <option value="price">Harga</option>
              <option value="rating">Rating</option>
            </select>
          </div>
        </div>
      </div>
    </section>

    <!-- Products Grid -->
    <section class="py-12 bg-brand-bg">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div v-if="products.data.length === 0" class="text-center py-12">
          <p class="text-xl text-brand-medium">Tidak ada gaun yang ditemukan.</p>
        </div>
        
        <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          <div
            v-for="product in products.data"
            :key="product.id"
            class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow"
          >
            <div class="aspect-w-3 aspect-h-4 bg-gray-200">
              <img :src="product.image" :alt="product.name" class="w-full h-64 object-cover">
            </div>
            <div class="p-4">
              <h3 class="text-lg font-semibold text-brand-dark mb-2 line-clamp-2">{{ product.name }}</h3>
              <p class="text-sm text-brand-medium mb-2">{{ product.category }}</p>
              
              <div class="flex items-center mb-3">
                <span class="text-yellow-400 mr-1">★</span>
                <span class="text-sm text-brand-medium">{{ product.rating }}</span>
                <span class="ml-auto text-sm px-2 py-1 rounded-full" 
                      :class="product.status === 'Tersedia' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'">
                  {{ product.status }}
                </span>
              </div>

              <div class="flex items-center justify-between mb-3">
                <span class="text-sm text-brand-medium">{{ product.color }}</span>
                <span class="text-sm text-brand-medium">{{ product.motif }}</span>
              </div>
              
              <div class="flex justify-between items-center">
                <span class="text-xl font-bold text-brand-dark">Rp {{ Number(product.price).toLocaleString() }}</span>
                <Link
                  :href="`/product/${product.sku}`"
                  class="bg-brand-dark text-white px-3 py-2 rounded text-sm hover:bg-brand-medium transition-colors"
                >
                  Detail
                </Link>
              </div>
            </div>
          </div>
        </div>

        <!-- Pagination -->
        <div v-if="products.last_page > 1" class="mt-12 flex justify-center">
          <nav class="flex items-center space-x-2">
            <Link
              v-if="products.prev_page_url"
              :href="products.prev_page_url"
              class="px-3 py-2 bg-white border border-brand-light rounded-lg hover:bg-brand-light transition-colors"
            >
              Previous
            </Link>
            
            <span v-for="page in paginationPages" :key="page" class="px-3 py-2">
              <Link
                v-if="page !== '...'"
                :href="getPageUrl(page)"
                :class="[
                  'px-3 py-2 rounded-lg transition-colors',
                  page === products.current_page
                    ? 'bg-brand-dark text-white'
                    : 'bg-white border border-brand-light hover:bg-brand-light'
                ]"
              >
                {{ page }}
              </Link>
              <span v-else class="px-3 py-2">...</span>
            </span>
            
            <Link
              v-if="products.next_page_url"
              :href="products.next_page_url"
              class="px-3 py-2 bg-white border border-brand-light rounded-lg hover:bg-brand-light transition-colors"
            >
              Next
            </Link>
          </nav>
        </div>
      </div>
    </section>
  </AppLayout>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { Link, router } from '@inertiajs/vue3'
import AppLayout from '@/Layouts/AppLayout.vue'

const props = defineProps({
  products: Object,
  categories: Array,
  filters: Object,
})

// Reactive filters
const searchQuery = ref(props.filters.search || '')
const selectedCategory = ref(props.filters.category || '')
const selectedStatus = ref(props.filters.status || '')
const sortBy = ref(props.filters.sort || 'name')

// Debounced filter update
let filterTimeout = null
const updateFilters = () => {
  clearTimeout(filterTimeout)
  filterTimeout = setTimeout(() => {
    router.get('/catalog', {
      search: searchQuery.value,
      category: selectedCategory.value,
      status: selectedStatus.value,
      sort: sortBy.value,
    }, {
      preserveState: true,
      preserveScroll: true,
    })
  }, 300)
}

// Pagination helper
const paginationPages = computed(() => {
  const pages = []
  const current = props.products.current_page
  const last = props.products.last_page
  
  if (last <= 7) {
    for (let i = 1; i <= last; i++) {
      pages.push(i)
    }
  } else {
    if (current <= 4) {
      for (let i = 1; i <= 5; i++) pages.push(i)
      pages.push('...')
      pages.push(last)
    } else if (current >= last - 3) {
      pages.push(1)
      pages.push('...')
      for (let i = last - 4; i <= last; i++) pages.push(i)
    } else {
      pages.push(1)
      pages.push('...')
      for (let i = current - 1; i <= current + 1; i++) pages.push(i)
      pages.push('...')
      pages.push(last)
    }
  }
  
  return pages
})

const getPageUrl = (page) => {
  const url = new URL(window.location)
  url.searchParams.set('page', page)
  return url.pathname + url.search
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
