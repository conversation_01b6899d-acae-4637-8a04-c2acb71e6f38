"use client"

import { useState } from "react"
import { ChevronLeft, ChevronRight, Plus, Eye } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"

interface InventoryCalendarProps {
  inventoryData: any[]
}

export function InventoryCalendar({ inventoryData }: InventoryCalendarProps) {
  const [currentDate, setCurrentDate] = useState(new Date())
  const [selectedGown, setSelectedGown] = useState("all")
  const [viewMode, setViewMode] = useState<"month" | "week">("month")

  const monthNames = [
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>",
    "April",
    "<PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON>",
    "<PERSON><PERSON><PERSON>",
    "September",
    "<PERSON><PERSON><PERSON>",
    "November",
    "Desember",
  ]

  const dayNames = ["<PERSON>", "Sen", "Sel", "Rab", "Kam", "Jum", "Sab"]

  const getDaysInMonth = (date: Date) => {
    const year = date.getFullYear()
    const month = date.getMonth()
    const firstDay = new Date(year, month, 1)
    const lastDay = new Date(year, month + 1, 0)
    const daysInMonth = lastDay.getDate()
    const startingDayOfWeek = firstDay.getDay()

    const days = []

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < startingDayOfWeek; i++) {
      days.push(null)
    }

    // Add days of the month
    for (let day = 1; day <= daysInMonth; day++) {
      days.push(new Date(year, month, day))
    }

    return days
  }

  const navigateMonth = (direction: "prev" | "next") => {
    setCurrentDate((prev) => {
      const newDate = new Date(prev)
      if (direction === "prev") {
        newDate.setMonth(prev.getMonth() - 1)
      } else {
        newDate.setMonth(prev.getMonth() + 1)
      }
      return newDate
    })
  }

  const getBookingsForDate = (date: Date) => {
    const dateStr = date.toISOString().split("T")[0]
    const bookings: any[] = []

    inventoryData.forEach((gown) => {
      // Check if gown is rented on this date
      if (gown.status === "rented" && gown.lastRented === dateStr) {
        bookings.push({
          gown: gown,
          type: "rental",
          customer: "Current Rental",
        })
      }

      // Check upcoming bookings
      gown.upcomingBookings?.forEach((booking: any) => {
        const bookingDate = new Date(booking.date)
        if (bookingDate.toDateString() === date.toDateString()) {
          bookings.push({
            gown: gown,
            type: "booking",
            customer: booking.customer,
          })
        }
      })
    })

    return bookings
  }

  const days = getDaysInMonth(currentDate)
  const filteredInventory =
    selectedGown === "all" ? inventoryData : inventoryData.filter((item) => item.id === selectedGown)

  return (
    <div className="space-y-6">
      {/* Calendar Controls */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Kalender Ketersediaan Gaun</CardTitle>
            <div className="flex items-center gap-4">
              <Select value={selectedGown} onValueChange={setSelectedGown}>
                <SelectTrigger className="w-64">
                  <SelectValue placeholder="Pilih gaun" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Gaun</SelectItem>
                  {inventoryData.map((item) => (
                    <SelectItem key={item.id} value={item.id}>
                      {item.name} ({item.id})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={viewMode} onValueChange={(value: "month" | "week") => setViewMode(value)}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="month">Bulan</SelectItem>
                  <SelectItem value="week">Minggu</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Calendar Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              <Button variant="outline" size="sm" onClick={() => navigateMonth("prev")}>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <h2 className="text-xl font-semibold">
                {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
              </h2>
              <Button variant="outline" size="sm" onClick={() => navigateMonth("next")}>
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
            <Button className="bg-rose-600 hover:bg-rose-700">
              <Plus className="h-4 w-4 mr-2" />
              Blokir Tanggal
            </Button>
          </div>

          {/* Calendar Grid */}
          <div className="grid grid-cols-7 gap-1 mb-4">
            {dayNames.map((day) => (
              <div key={day} className="p-2 text-center text-sm font-medium text-gray-500 bg-gray-50 rounded">
                {day}
              </div>
            ))}
          </div>

          <div className="grid grid-cols-7 gap-1">
            {days.map((day, index) => {
              if (!day) {
                return <div key={index} className="h-24 bg-gray-50 rounded"></div>
              }

              const bookings = getBookingsForDate(day)
              const isToday = day.toDateString() === new Date().toDateString()

              return (
                <Dialog key={index}>
                  <DialogTrigger asChild>
                    <div
                      className={`h-24 p-2 border rounded cursor-pointer hover:bg-gray-50 transition-colors ${
                        isToday ? "bg-rose-50 border-rose-200" : "bg-white border-gray-200"
                      }`}
                    >
                      <div className="flex items-center justify-between mb-1">
                        <span className={`text-sm font-medium ${isToday ? "text-rose-600" : "text-gray-900"}`}>
                          {day.getDate()}
                        </span>
                        {bookings.length > 0 && (
                          <Badge variant="secondary" className="text-xs px-1 py-0">
                            {bookings.length}
                          </Badge>
                        )}
                      </div>
                      <div className="space-y-1">
                        {bookings.slice(0, 2).map((booking, idx) => (
                          <div
                            key={idx}
                            className={`text-xs p-1 rounded truncate ${
                              booking.type === "rental" ? "bg-blue-100 text-blue-800" : "bg-green-100 text-green-800"
                            }`}
                          >
                            {booking.gown.name.substring(0, 15)}...
                          </div>
                        ))}
                        {bookings.length > 2 && (
                          <div className="text-xs text-gray-500">+{bookings.length - 2} lainnya</div>
                        )}
                      </div>
                    </div>
                  </DialogTrigger>
                  <DialogContent className="max-w-2xl">
                    <DialogHeader>
                      <DialogTitle>
                        Jadwal Tanggal {day.getDate()} {monthNames[day.getMonth()]} {day.getFullYear()}
                      </DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                      {bookings.length === 0 ? (
                        <div className="text-center py-8 text-gray-500">
                          <p>Tidak ada jadwal pada tanggal ini</p>
                          <Button className="mt-4 bg-rose-600 hover:bg-rose-700">
                            <Plus className="h-4 w-4 mr-2" />
                            Tambah Booking
                          </Button>
                        </div>
                      ) : (
                        <div className="space-y-3">
                          {bookings.map((booking, idx) => (
                            <Card key={idx}>
                              <CardContent className="p-4">
                                <div className="flex items-center justify-between">
                                  <div className="flex items-center space-x-3">
                                    <img
                                      src={booking.gown.image || "/placeholder.svg"}
                                      alt={booking.gown.name}
                                      className="w-12 h-12 object-cover rounded"
                                    />
                                    <div>
                                      <h4 className="font-medium">{booking.gown.name}</h4>
                                      <p className="text-sm text-gray-600">
                                        {booking.gown.id} • {booking.gown.size} • {booking.gown.color}
                                      </p>
                                      <p className="text-sm text-gray-500">Pelanggan: {booking.customer}</p>
                                    </div>
                                  </div>
                                  <div className="flex items-center gap-2">
                                    <Badge variant={booking.type === "rental" ? "default" : "secondary"}>
                                      {booking.type === "rental" ? "Sedang Disewa" : "Booking"}
                                    </Badge>
                                    <Button variant="outline" size="sm">
                                      <Eye className="h-3 w-3 mr-1" />
                                      Detail
                                    </Button>
                                  </div>
                                </div>
                              </CardContent>
                            </Card>
                          ))}
                        </div>
                      )}
                    </div>
                  </DialogContent>
                </Dialog>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Legend */}
      <Card>
        <CardHeader>
          <CardTitle>Keterangan</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4">
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-blue-100 border border-blue-200 rounded"></div>
              <span className="text-sm">Sedang Disewa</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-green-100 border border-green-200 rounded"></div>
              <span className="text-sm">Booking Terkonfirmasi</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-yellow-100 border border-yellow-200 rounded"></div>
              <span className="text-sm">Maintenance/Cleaning</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-red-100 border border-red-200 rounded"></div>
              <span className="text-sm">Tidak Tersedia</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-4 h-4 bg-rose-50 border border-rose-200 rounded"></div>
              <span className="text-sm">Hari Ini</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
