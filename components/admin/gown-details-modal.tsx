"use client"

import { useState } from "react"
import { X, Edit, Calendar, TrendingUp, <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle, Clock, Wrench } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"

interface GownDetailsModalProps {
  gown: any
  isOpen: boolean
  onClose: () => void
}

export function GownDetailsModal({ gown, isOpen, onClose }: GownDetailsModalProps) {
  const [newStatus, setNewStatus] = useState(gown.status)
  const [notes, setNotes] = useState(gown.notes || "")

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "available":
        return (
          <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
            <CheckCircle className="h-3 w-3 mr-1" />
            Tersedia
          </Badge>
        )
      case "rented":
        return (
          <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">
            <Clock className="h-3 w-3 mr-1" />
            Disewa
          </Badge>
        )
      case "cleaning":
        return (
          <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
            <Clock className="h-3 w-3 mr-1" />
            Dibersihkan
          </Badge>
        )
      case "maintenance":
        return (
          <Badge className="bg-orange-100 text-orange-800 hover:bg-orange-100">
            <Wrench className="h-3 w-3 mr-1" />
            Maintenance
          </Badge>
        )
      case "damaged":
        return (
          <Badge className="bg-red-100 text-red-800 hover:bg-red-100">
            <AlertTriangle className="h-3 w-3 mr-1" />
            Rusak
          </Badge>
        )
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const getConditionBadge = (condition: string) => {
    switch (condition) {
      case "excellent":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Sangat Baik</Badge>
      case "good":
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">Baik</Badge>
      case "fair":
        return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">Cukup</Badge>
      case "poor":
        return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Buruk</Badge>
      default:
        return <Badge variant="secondary">{condition}</Badge>
    }
  }

  const handleSaveStatus = () => {
    // Handle status update
    console.log("Updating status to:", newStatus)
    console.log("Notes:", notes)
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <div className="flex items-center justify-between">
            <DialogTitle className="text-xl font-bold">{gown.name}</DialogTitle>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </DialogHeader>

        <div className="grid lg:grid-cols-3 gap-6">
          {/* Gown Image and Basic Info */}
          <div className="lg:col-span-1">
            <Card>
              <CardContent className="p-4">
                <img
                  src={gown.image || "/placeholder.svg"}
                  alt={gown.name}
                  className="w-full h-64 object-cover rounded-lg mb-4"
                />
                <div className="space-y-3">
                  <div>
                    <h3 className="font-semibold text-gray-900">{gown.name}</h3>
                    <p className="text-sm text-gray-600">ID: {gown.id}</p>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusBadge(gown.status)}
                    {getConditionBadge(gown.condition)}
                  </div>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Kategori:</span>
                      <span className="font-medium">{gown.category}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Ukuran:</span>
                      <span className="font-medium">{gown.size}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Warna:</span>
                      <span className="font-medium">{gown.color}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Total Sewa:</span>
                      <span className="font-medium">{gown.totalRentals} kali</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Terakhir Disewa:</span>
                      <span className="font-medium">{gown.lastRented}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Status Update */}
            <Card className="mt-4">
              <CardHeader>
                <CardTitle className="text-lg">Update Status</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="status">Status Gaun</Label>
                  <Select value={newStatus} onValueChange={setNewStatus}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="available">Tersedia</SelectItem>
                      <SelectItem value="rented">Disewa</SelectItem>
                      <SelectItem value="cleaning">Dibersihkan</SelectItem>
                      <SelectItem value="maintenance">Maintenance</SelectItem>
                      <SelectItem value="damaged">Rusak</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="notes">Catatan</Label>
                  <Textarea
                    id="notes"
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    placeholder="Tambahkan catatan tentang kondisi atau status gaun..."
                    rows={3}
                  />
                </div>
                <Button onClick={handleSaveStatus} className="w-full bg-rose-600 hover:bg-rose-700">
                  <Edit className="h-4 w-4 mr-2" />
                  Update Status
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Detailed Information */}
          <div className="lg:col-span-2">
            <Tabs defaultValue="history" className="space-y-4">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="history">Riwayat Sewa</TabsTrigger>
                <TabsTrigger value="schedule">Jadwal Mendatang</TabsTrigger>
                <TabsTrigger value="analytics">Analitik</TabsTrigger>
              </TabsList>

              <TabsContent value="history">
                <Card>
                  <CardHeader>
                    <CardTitle>Riwayat Penyewaan</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {gown.rentalHistory && gown.rentalHistory.length > 0 ? (
                      <div className="space-y-4">
                        {gown.rentalHistory.map((rental: any, index: number) => (
                          <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div>
                              <p className="font-medium">{rental.customer}</p>
                              <p className="text-sm text-gray-600">
                                {rental.date} • {rental.duration} hari
                              </p>
                            </div>
                            <Button variant="outline" size="sm">
                              <TrendingUp className="h-3 w-3 mr-1" />
                              Detail
                            </Button>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8 text-gray-500">
                        <p>Belum ada riwayat penyewaan</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="schedule">
                <Card>
                  <CardHeader>
                    <CardTitle>Jadwal Mendatang</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {gown.upcomingBookings && gown.upcomingBookings.length > 0 ? (
                      <div className="space-y-4">
                        {gown.upcomingBookings.map((booking: any, index: number) => (
                          <div key={index} className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                            <div>
                              <p className="font-medium">{booking.customer}</p>
                              <p className="text-sm text-gray-600">
                                {booking.date} • {booking.duration} hari
                              </p>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">Terkonfirmasi</Badge>
                              <Button variant="outline" size="sm">
                                <Calendar className="h-3 w-3 mr-1" />
                                Lihat
                              </Button>
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8 text-gray-500">
                        <p>Tidak ada jadwal mendatang</p>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="analytics">
                <div className="grid md:grid-cols-2 gap-4">
                  <Card>
                    <CardHeader>
                      <CardTitle>Statistik Penyewaan</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">Total Penyewaan</span>
                          <span className="text-lg font-bold">{gown.totalRentals}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">Rata-rata per Bulan</span>
                          <span className="text-lg font-bold">{Math.round(gown.totalRentals / 12)}</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">Tingkat Okupansi</span>
                          <span className="text-lg font-bold text-green-600">
                            {Math.round((gown.totalRentals / 365) * 100)}%
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  <Card>
                    <CardHeader>
                      <CardTitle>Kondisi & Maintenance</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">Kondisi Saat Ini</span>
                          {getConditionBadge(gown.condition)}
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">Maintenance Terakhir</span>
                          <span className="text-sm">15 Feb 2024</span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-sm text-gray-600">Perlu Maintenance</span>
                          <Badge variant={gown.totalRentals > 10 ? "destructive" : "secondary"}>
                            {gown.totalRentals > 10 ? "Ya" : "Tidak"}
                          </Badge>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
