"use client"

import { useState, useEffect } from "react"
import { Search, Plus, Package, Star, AlertCircle, CheckCircle, X, Filter } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Checkbox } from "@/components/ui/checkbox"
import { Slider } from "@/components/ui/slider"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"

const mockProducts = [
  {
    id: "GSJ-001",
    name: "Gaun Pengantin Syar'i Mewah Premium",
    category: "Baju Pengantin",
    subcategory: "Gaun Akad",
    price: 750000,
    originalPrice: 850000,
    sizes: [
      { size: "S", available: true, stock: 2 },
      { size: "M", available: true, stock: 3 },
      { size: "L", available: true, stock: 1 },
      { size: "XL", available: false, stock: 0 },
    ],
    colors: [
      { color: "Putih Gading", hex: "#FFF8DC", available: true },
      { color: "Krem", hex: "#F5F5DC", available: true },
      { color: "Champagne", hex: "#F7E7CE", available: false },
    ],
    images: [
      "/placeholder.svg?height=300&width=300&text=Gaun+Pengantin+1",
      "/placeholder.svg?height=300&width=300&text=Gaun+Pengantin+2",
      "/placeholder.svg?height=300&width=300&text=Gaun+Pengantin+3",
    ],
    description: "Gaun pengantin syar'i dengan detail bordir mewah dan bahan premium. Cocok untuk acara akad nikah.",
    features: ["Bahan Premium", "Bordir Tangan", "Lining Berkualitas", "Aksesoris Lengkap"],
    rating: 4.8,
    reviews: 24,
    available: true,
    isPopular: true,
    isNew: false,
    rentalDuration: { min: 1, max: 7 },
    bookedDates: ["2024-01-15", "2024-01-20", "2024-02-14"],
  },
  {
    id: "GSJ-002",
    name: "Gaun Ibu Pengantin Elegant Modern",
    category: "Baju Orang Tua (Ibu)",
    subcategory: "Gaun Pesta",
    price: 500000,
    originalPrice: 500000,
    sizes: [
      { size: "M", available: true, stock: 2 },
      { size: "L", available: true, stock: 4 },
      { size: "XL", available: true, stock: 2 },
      { size: "XXL", available: true, stock: 1 },
    ],
    colors: [
      { color: "Navy Blue", hex: "#000080", available: true },
      { color: "Maroon", hex: "#800000", available: true },
      { color: "Hijau Tua", hex: "#006400", available: true },
    ],
    images: [
      "/placeholder.svg?height=300&width=300&text=Gaun+Ibu+1",
      "/placeholder.svg?height=300&width=300&text=Gaun+Ibu+2",
    ],
    description: "Gaun elegant untuk ibu pengantin dengan desain modern dan nyaman digunakan.",
    features: ["Desain Modern", "Bahan Nyaman", "Cutting Bagus", "Warna Elegan"],
    rating: 4.6,
    reviews: 18,
    available: true,
    isPopular: false,
    isNew: true,
    rentalDuration: { min: 1, max: 5 },
    bookedDates: ["2024-01-18", "2024-02-10"],
  },
  {
    id: "GSJ-003",
    name: "Baju Koko Ayah Premium Gold",
    category: "Baju Ayah",
    subcategory: "Koko Premium",
    price: 437500,
    originalPrice: 500000,
    sizes: [
      { size: "M", available: true, stock: 3 },
      { size: "L", available: true, stock: 2 },
      { size: "XL", available: false, stock: 0 },
      { size: "XXL", available: true, stock: 1 },
    ],
    colors: [
      { color: "Putih", hex: "#FFFFFF", available: true },
      { color: "Krem", hex: "#F5F5DC", available: true },
      { color: "Abu-abu", hex: "#808080", available: false },
    ],
    images: ["/placeholder.svg?height=300&width=300&text=Koko+Ayah+1"],
    description: "Baju koko premium untuk ayah pengantin dengan detail bordir emas yang elegan.",
    features: ["Bordir Emas", "Bahan Premium", "Cutting Presisi", "Nyaman Digunakan"],
    rating: 4.7,
    reviews: 12,
    available: true,
    isPopular: true,
    isNew: false,
    rentalDuration: { min: 1, max: 3 },
    bookedDates: ["2024-01-25"],
  },
  {
    id: "GSJ-004",
    name: "Gaun Wisuda Premium Elegant",
    category: "Baju Wisuda",
    subcategory: "Gaun Wisuda",
    price: 525000,
    originalPrice: 600000,
    sizes: [
      { size: "S", available: true, stock: 1 },
      { size: "M", available: true, stock: 2 },
      { size: "L", available: false, stock: 0 },
    ],
    colors: [
      { color: "Hitam", hex: "#000000", available: true },
      { color: "Navy", hex: "#000080", available: true },
      { color: "Maroon", hex: "#800000", available: false },
    ],
    images: [
      "/placeholder.svg?height=300&width=300&text=Gaun+Wisuda+1",
      "/placeholder.svg?height=300&width=300&text=Gaun+Wisuda+2",
    ],
    description: "Gaun wisuda premium dengan desain elegant dan formal untuk momen kelulusan yang berkesan.",
    features: ["Desain Formal", "Bahan Berkualitas", "Cutting Sempurna", "Aksesoris Toga"],
    rating: 4.9,
    reviews: 31,
    available: true,
    isPopular: true,
    isNew: true,
    rentalDuration: { min: 1, max: 2 },
    bookedDates: ["2024-02-01", "2024-02-15", "2024-03-01"],
  },
]

interface ProductSelectorProps {
  onProductAdd: (product: any) => void
}

export function ProductSelector({ onProductAdd }: ProductSelectorProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [selectedProduct, setSelectedProduct] = useState<any>(null)
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [priceRange, setPriceRange] = useState([0, 1000000])
  const [showAvailableOnly, setShowAvailableOnly] = useState(true)
  const [sortBy, setSortBy] = useState("popular")
  const [productConfig, setProductConfig] = useState({
    size: "",
    color: "",
    quantity: 1,
    startDate: "",
    endDate: "",
    customPrice: "",
    notes: "",
  })
  const [availabilityCheck, setAvailabilityCheck] = useState<{
    isChecking: boolean
    isAvailable: boolean
    conflicts: string[]
  }>({
    isChecking: false,
    isAvailable: true,
    conflicts: [],
  })

  const categories = [
    "all",
    "Baju Pengantin",
    "Baju Orang Tua (Ibu)",
    "Baju Ayah",
    "Baju Wisuda",
    "Aksesoris",
    "Paket Lengkap",
  ]

  const filteredProducts = mockProducts
    .filter((product) => {
      const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase())
      const matchesCategory = selectedCategory === "all" || product.category === selectedCategory
      const matchesPrice = product.price >= priceRange[0] && product.price <= priceRange[1]
      const matchesAvailability = !showAvailableOnly || product.available
      return matchesSearch && matchesCategory && matchesPrice && matchesAvailability
    })
    .sort((a, b) => {
      switch (sortBy) {
        case "price_low":
          return a.price - b.price
        case "price_high":
          return b.price - a.price
        case "rating":
          return b.rating - a.rating
        case "popular":
          return b.isPopular ? 1 : -1
        case "newest":
          return b.isNew ? 1 : -1
        default:
          return 0
      }
    })

  const handleProductSelect = (product: any) => {
    setSelectedProduct(product)
    setCurrentImageIndex(0)
    setProductConfig({
      size: "",
      color: "",
      quantity: 1,
      startDate: "",
      endDate: "",
      customPrice: product.price.toString(),
      notes: "",
    })
  }

  const checkAvailability = async () => {
    if (!productConfig.startDate || !productConfig.endDate) return

    setAvailabilityCheck({ isChecking: true, isAvailable: true, conflicts: [] })

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 1000))

    const conflicts = selectedProduct.bookedDates.filter((date: string) => {
      const bookingDate = new Date(date)
      const startDate = new Date(productConfig.startDate)
      const endDate = new Date(productConfig.endDate)
      return bookingDate >= startDate && bookingDate <= endDate
    })

    setAvailabilityCheck({
      isChecking: false,
      isAvailable: conflicts.length === 0,
      conflicts,
    })
  }

  useEffect(() => {
    if (productConfig.startDate && productConfig.endDate) {
      checkAvailability()
    }
  }, [productConfig.startDate, productConfig.endDate])

  const calculateRentalDays = () => {
    if (!productConfig.startDate || !productConfig.endDate) return 0
    const start = new Date(productConfig.startDate)
    const end = new Date(productConfig.endDate)
    const diffTime = Math.abs(end.getTime() - start.getTime())
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1
  }

  const calculateTotalPrice = () => {
    const basePrice = Number(productConfig.customPrice) || selectedProduct?.price || 0
    const days = calculateRentalDays()
    return basePrice * productConfig.quantity * (days > 0 ? days : 1)
  }

  const handleAddProduct = () => {
    if (
      selectedProduct &&
      productConfig.size &&
      productConfig.color &&
      productConfig.startDate &&
      productConfig.endDate &&
      availabilityCheck.isAvailable
    ) {
      const productToAdd = {
        ...selectedProduct,
        size: productConfig.size,
        color: productConfig.color,
        quantity: productConfig.quantity,
        startDate: productConfig.startDate,
        endDate: productConfig.endDate,
        rentalDays: calculateRentalDays(),
        price: Number(productConfig.customPrice) || selectedProduct.price,
        totalPrice: calculateTotalPrice(),
        notes: productConfig.notes,
      }
      onProductAdd(productToAdd)
      setSelectedProduct(null)
      setProductConfig({
        size: "",
        color: "",
        quantity: 1,
        startDate: "",
        endDate: "",
        customPrice: "",
        notes: "",
      })
      setIsOpen(false)
    }
  }

  const resetFilters = () => {
    setSearchTerm("")
    setSelectedCategory("all")
    setPriceRange([0, 1000000])
    setShowAvailableOnly(true)
    setSortBy("popular")
  }

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium">Pilih Produk</h3>
          <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
              <Button className="bg-rose-600 hover:bg-rose-700">
                <Plus className="h-4 w-4 mr-2" />
                Tambah Produk
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-7xl max-h-[95vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  <Package className="h-5 w-5" />
                  Pilih Produk untuk Pesanan
                </DialogTitle>
              </DialogHeader>

              <Tabs defaultValue="browse" className="space-y-6">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="browse">Browse Produk</TabsTrigger>
                  <TabsTrigger value="configure" disabled={!selectedProduct}>
                    Konfigurasi Produk
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="browse" className="space-y-6">
                  {/* Search and Filters */}
                  <div className="space-y-4">
                    <div className="flex flex-wrap gap-4">
                      <div className="flex-1 min-w-64">
                        <div className="relative">
                          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                          <Input
                            placeholder="Cari produk berdasarkan nama atau kode..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="pl-10"
                          />
                        </div>
                      </div>
                      <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                        <SelectTrigger className="w-48">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {categories.map((category) => (
                            <SelectItem key={category} value={category}>
                              {category === "all" ? "Semua Kategori" : category}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      <Select value={sortBy} onValueChange={setSortBy}>
                        <SelectTrigger className="w-48">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="popular">Terpopuler</SelectItem>
                          <SelectItem value="newest">Terbaru</SelectItem>
                          <SelectItem value="rating">Rating Tertinggi</SelectItem>
                          <SelectItem value="price_low">Harga Terendah</SelectItem>
                          <SelectItem value="price_high">Harga Tertinggi</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Advanced Filters */}
                    <div className="flex items-center gap-6 p-4 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-2">
                        <Checkbox
                          id="available-only"
                          checked={showAvailableOnly}
                          onCheckedChange={setShowAvailableOnly}
                        />
                        <Label htmlFor="available-only" className="text-sm">
                          Hanya yang tersedia
                        </Label>
                      </div>
                      <div className="flex-1">
                        <Label className="text-sm font-medium">
                          Rentang Harga: Rp {priceRange[0].toLocaleString()} - Rp {priceRange[1].toLocaleString()}
                        </Label>
                        <Slider
                          value={priceRange}
                          onValueChange={setPriceRange}
                          max={1000000}
                          step={50000}
                          className="mt-2"
                        />
                      </div>
                      <Button variant="outline" size="sm" onClick={resetFilters}>
                        <Filter className="h-4 w-4 mr-2" />
                        Reset Filter
                      </Button>
                    </div>
                  </div>

                  {/* Product Grid */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    {filteredProducts.map((product) => (
                      <Card
                        key={product.id}
                        className={`cursor-pointer transition-all hover:shadow-lg ${
                          selectedProduct?.id === product.id ? "ring-2 ring-rose-500" : ""
                        } ${!product.available ? "opacity-60" : ""}`}
                        onClick={() => product.available && handleProductSelect(product)}
                      >
                        <CardContent className="p-0">
                          <div className="relative">
                            <img
                              src={product.images[0] || "/placeholder.svg"}
                              alt={product.name}
                              className="w-full h-48 object-cover rounded-t-lg"
                            />
                            <div className="absolute top-2 left-2 flex gap-1">
                              {product.isNew && <Badge className="bg-green-600 hover:bg-green-700">Baru</Badge>}
                              {product.isPopular && (
                                <Badge className="bg-orange-600 hover:bg-orange-700">Populer</Badge>
                              )}
                            </div>
                            <div className="absolute top-2 right-2">
                              {product.available ? (
                                <Badge variant="secondary" className="bg-green-100 text-green-800">
                                  <CheckCircle className="h-3 w-3 mr-1" />
                                  Tersedia
                                </Badge>
                              ) : (
                                <Badge variant="secondary" className="bg-red-100 text-red-800">
                                  <X className="h-3 w-3 mr-1" />
                                  Tidak Tersedia
                                </Badge>
                              )}
                            </div>
                          </div>
                          <div className="p-4">
                            <div className="flex items-start justify-between mb-2">
                              <h4 className="font-medium text-sm leading-tight">{product.name}</h4>
                            </div>
                            <p className="text-xs text-gray-600 mb-2">{product.category}</p>
                            <div className="flex items-center gap-1 mb-2">
                              <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                              <span className="text-xs font-medium">{product.rating}</span>
                              <span className="text-xs text-gray-500">({product.reviews})</span>
                            </div>
                            <div className="flex items-center justify-between">
                              <div>
                                <span className="font-bold text-rose-600">Rp {product.price.toLocaleString()}</span>
                                {product.originalPrice > product.price && (
                                  <span className="text-xs text-gray-500 line-through ml-1">
                                    Rp {product.originalPrice.toLocaleString()}
                                  </span>
                                )}
                              </div>
                              <Badge variant="outline" className="text-xs">
                                {product.id}
                              </Badge>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>

                  {filteredProducts.length === 0 && (
                    <div className="text-center py-12">
                      <Package className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">Tidak ada produk ditemukan</h3>
                      <p className="text-gray-500">Coba ubah filter atau kata kunci pencarian</p>
                      <Button variant="outline" onClick={resetFilters} className="mt-4 bg-transparent">
                        Reset Filter
                      </Button>
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="configure" className="space-y-6">
                  {selectedProduct && (
                    <div className="grid lg:grid-cols-2 gap-8">
                      {/* Product Details */}
                      <div className="space-y-6">
                        <div className="relative">
                          <img
                            src={selectedProduct.images[currentImageIndex] || "/placeholder.svg"}
                            alt={selectedProduct.name}
                            className="w-full h-80 object-cover rounded-lg"
                          />
                          {selectedProduct.images.length > 1 && (
                            <div className="flex gap-2 mt-4">
                              {selectedProduct.images.map((image: string, index: number) => (
                                <button
                                  key={index}
                                  onClick={() => setCurrentImageIndex(index)}
                                  className={`w-16 h-16 rounded-lg overflow-hidden border-2 ${
                                    currentImageIndex === index ? "border-rose-500" : "border-gray-200"
                                  }`}
                                >
                                  <img
                                    src={image || "/placeholder.svg"}
                                    alt=""
                                    className="w-full h-full object-cover"
                                  />
                                </button>
                              ))}
                            </div>
                          )}
                        </div>

                        <div>
                          <div className="flex items-start justify-between mb-4">
                            <div>
                              <h3 className="text-xl font-bold mb-2">{selectedProduct.name}</h3>
                              <p className="text-gray-600 mb-2">
                                {selectedProduct.category} • {selectedProduct.subcategory}
                              </p>
                              <div className="flex items-center gap-2 mb-2">
                                <div className="flex items-center gap-1">
                                  <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                                  <span className="font-medium">{selectedProduct.rating}</span>
                                  <span className="text-gray-500">({selectedProduct.reviews} ulasan)</span>
                                </div>
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="text-2xl font-bold text-rose-600">
                                Rp {selectedProduct.price.toLocaleString()}
                              </div>
                              {selectedProduct.originalPrice > selectedProduct.price && (
                                <div className="text-sm text-gray-500 line-through">
                                  Rp {selectedProduct.originalPrice.toLocaleString()}
                                </div>
                              )}
                              <div className="text-xs text-gray-500 mt-1">per hari</div>
                            </div>
                          </div>

                          <p className="text-gray-700 mb-4">{selectedProduct.description}</p>

                          <div className="space-y-3">
                            <h4 className="font-medium">Fitur Produk:</h4>
                            <div className="grid grid-cols-2 gap-2">
                              {selectedProduct.features.map((feature: string, index: number) => (
                                <div key={index} className="flex items-center gap-2">
                                  <CheckCircle className="h-4 w-4 text-green-600" />
                                  <span className="text-sm">{feature}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Configuration Form */}
                      <div className="space-y-6">
                        <Card>
                          <CardContent className="p-6 space-y-4">
                            <h4 className="font-medium text-lg">Konfigurasi Pesanan</h4>

                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <Label htmlFor="size">Ukuran *</Label>
                                <Select
                                  value={productConfig.size}
                                  onValueChange={(value) => setProductConfig({ ...productConfig, size: value })}
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder="Pilih ukuran" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {selectedProduct.sizes.map((sizeOption: any) => (
                                      <SelectItem
                                        key={sizeOption.size}
                                        value={sizeOption.size}
                                        disabled={!sizeOption.available}
                                      >
                                        <div className="flex items-center justify-between w-full">
                                          <span>{sizeOption.size}</span>
                                          <span className="text-xs text-gray-500 ml-2">
                                            {sizeOption.available ? `(${sizeOption.stock} tersedia)` : "(Habis)"}
                                          </span>
                                        </div>
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              </div>

                              <div>
                                <Label htmlFor="color">Warna *</Label>
                                <Select
                                  value={productConfig.color}
                                  onValueChange={(value) => setProductConfig({ ...productConfig, color: value })}
                                >
                                  <SelectTrigger>
                                    <SelectValue placeholder="Pilih warna" />
                                  </SelectTrigger>
                                  <SelectContent>
                                    {selectedProduct.colors.map((colorOption: any) => (
                                      <SelectItem
                                        key={colorOption.color}
                                        value={colorOption.color}
                                        disabled={!colorOption.available}
                                      >
                                        <div className="flex items-center gap-2">
                                          <div
                                            className="w-4 h-4 rounded-full border"
                                            style={{ backgroundColor: colorOption.hex }}
                                          />
                                          <span>{colorOption.color}</span>
                                          {!colorOption.available && (
                                            <span className="text-xs text-gray-500">(Tidak tersedia)</span>
                                          )}
                                        </div>
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                              </div>
                            </div>

                            <div className="grid grid-cols-2 gap-4">
                              <div>
                                <Label htmlFor="quantity">Jumlah</Label>
                                <Input
                                  id="quantity"
                                  type="number"
                                  min="1"
                                  max="5"
                                  value={productConfig.quantity}
                                  onChange={(e) =>
                                    setProductConfig({ ...productConfig, quantity: Number(e.target.value) })
                                  }
                                />
                              </div>

                              <div>
                                <Label htmlFor="customPrice">Harga Custom (Opsional)</Label>
                                <Input
                                  id="customPrice"
                                  type="number"
                                  value={productConfig.customPrice}
                                  onChange={(e) => setProductConfig({ ...productConfig, customPrice: e.target.value })}
                                  placeholder={selectedProduct.price.toString()}
                                />
                              </div>
                            </div>

                            <Separator />

                            <div className="space-y-4">
                              <h5 className="font-medium">Tanggal Sewa</h5>
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <Label htmlFor="startDate">Tanggal Mulai *</Label>
                                  <Input
                                    id="startDate"
                                    type="date"
                                    value={productConfig.startDate}
                                    onChange={(e) => setProductConfig({ ...productConfig, startDate: e.target.value })}
                                    min={new Date().toISOString().split("T")[0]}
                                  />
                                </div>

                                <div>
                                  <Label htmlFor="endDate">Tanggal Selesai *</Label>
                                  <Input
                                    id="endDate"
                                    type="date"
                                    value={productConfig.endDate}
                                    onChange={(e) => setProductConfig({ ...productConfig, endDate: e.target.value })}
                                    min={productConfig.startDate || new Date().toISOString().split("T")[0]}
                                  />
                                </div>
                              </div>

                              {/* Availability Check */}
                              {productConfig.startDate && productConfig.endDate && (
                                <div className="space-y-2">
                                  {availabilityCheck.isChecking ? (
                                    <Alert>
                                      <AlertCircle className="h-4 w-4" />
                                      <AlertDescription>Mengecek ketersediaan...</AlertDescription>
                                    </Alert>
                                  ) : availabilityCheck.isAvailable ? (
                                    <Alert className="border-green-200 bg-green-50">
                                      <CheckCircle className="h-4 w-4 text-green-600" />
                                      <AlertDescription className="text-green-800">
                                        Produk tersedia untuk tanggal yang dipilih ({calculateRentalDays()} hari)
                                      </AlertDescription>
                                    </Alert>
                                  ) : (
                                    <Alert className="border-red-200 bg-red-50">
                                      <AlertCircle className="h-4 w-4 text-red-600" />
                                      <AlertDescription className="text-red-800">
                                        Produk tidak tersedia pada tanggal: {availabilityCheck.conflicts.join(", ")}
                                      </AlertDescription>
                                    </Alert>
                                  )}
                                </div>
                              )}
                            </div>

                            <div>
                              <Label htmlFor="notes">Catatan Khusus (Opsional)</Label>
                              <Input
                                id="notes"
                                value={productConfig.notes}
                                onChange={(e) => setProductConfig({ ...productConfig, notes: e.target.value })}
                                placeholder="Catatan khusus untuk produk ini..."
                              />
                            </div>

                            <Separator />

                            <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                              <div className="flex justify-between text-sm">
                                <span>Harga per hari:</span>
                                <span>
                                  Rp {(Number(productConfig.customPrice) || selectedProduct.price).toLocaleString()}
                                </span>
                              </div>
                              <div className="flex justify-between text-sm">
                                <span>Jumlah:</span>
                                <span>{productConfig.quantity} pcs</span>
                              </div>
                              <div className="flex justify-between text-sm">
                                <span>Durasi sewa:</span>
                                <span>{calculateRentalDays()} hari</span>
                              </div>
                              <Separator />
                              <div className="flex justify-between font-bold text-lg">
                                <span>Total Harga:</span>
                                <span className="text-rose-600">Rp {calculateTotalPrice().toLocaleString()}</span>
                              </div>
                            </div>
                          </CardContent>
                        </Card>

                        <div className="flex justify-between gap-4">
                          <Button variant="outline" onClick={() => setSelectedProduct(null)} className="flex-1">
                            Kembali ke Daftar
                          </Button>
                          <Button
                            onClick={handleAddProduct}
                            disabled={
                              !productConfig.size ||
                              !productConfig.color ||
                              !productConfig.startDate ||
                              !productConfig.endDate ||
                              !availabilityCheck.isAvailable ||
                              availabilityCheck.isChecking
                            }
                            className="bg-rose-600 hover:bg-rose-700 flex-1"
                          >
                            <Package className="h-4 w-4 mr-2" />
                            Tambah ke Pesanan
                          </Button>
                        </div>
                      </div>
                    </div>
                  )}
                </TabsContent>
              </Tabs>
            </DialogContent>
          </Dialog>
        </div>

        <div className="text-center py-8 text-gray-500">
          <Package className="h-12 w-12 mx-auto mb-3 opacity-50" />
          <p>Belum ada produk yang dipilih</p>
          <p className="text-sm">Klik "Tambah Produk" untuk memilih produk</p>
        </div>
      </CardContent>
    </Card>
  )
}
