"use client"

import { useState } from "react"
import { Search, Plus, User, Phone, Mail, MapPin, Star, Calendar, Package } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

const mockCustomers = [
  {
    id: "CUST-001",
    name: "<PERSON><PERSON> <PERSON><PERSON>",
    email: "<EMAIL>",
    phone: "+62 812-3456-7890",
    address: "Jl. Malioboro No. 123, Yogyakarta",
    avatar: "/placeholder.svg?height=40&width=40",
    status: "vip",
    totalOrders: 8,
    totalSpent: 4500000,
    lastOrder: "2024-01-15",
    preferences: {
      size: "M",
      colors: ["Putih", "Krem"],
      categories: ["Baju Pengantin"],
    },
    notes: "Pelanggan setia, suka warna-warna soft",
    rating: 5,
    joinDate: "2023-06-15",
  },
  {
    id: "CUST-002",
    name: "Fatimah Zahra",
    email: "<EMAIL>",
    phone: "+62 813-9876-5432",
    address: "Jl. Sultan Agung No. 45, Yogyakarta",
    avatar: "/placeholder.svg?height=40&width=40",
    status: "regular",
    totalOrders: 3,
    totalSpent: 1800000,
    lastOrder: "2024-01-10",
    preferences: {
      size: "L",
      colors: ["Navy", "Maroon"],
      categories: ["Baju Orang Tua (Ibu)"],
    },
    notes: "Suka warna gelap, ukuran L",
    rating: 4.5,
    joinDate: "2023-11-20",
  },
  {
    id: "CUST-003",
    name: "Ahmad Rahman",
    email: "<EMAIL>",
    phone: "+62 814-5555-1234",
    address: "Jl. Kaliurang KM 5, Sleman, Yogyakarta",
    avatar: "/placeholder.svg?height=40&width=40",
    status: "new",
    totalOrders: 1,
    totalSpent: 437500,
    lastOrder: "2024-01-08",
    preferences: {
      size: "XL",
      colors: ["Putih", "Abu-abu"],
      categories: ["Baju Ayah"],
    },
    notes: "Pelanggan baru, perlu perhatian khusus",
    rating: 4,
    joinDate: "2024-01-05",
  },
  {
    id: "CUST-004",
    name: "Khadijah Rahman",
    email: "<EMAIL>",
    phone: "+62 815-7777-8888",
    address: "Jl. Parangtritis KM 3, Bantul, Yogyakarta",
    avatar: "/placeholder.svg?height=40&width=40",
    status: "regular",
    totalOrders: 5,
    totalSpent: 2625000,
    lastOrder: "2024-01-05",
    preferences: {
      size: "S",
      colors: ["Hitam", "Navy"],
      categories: ["Baju Wisuda", "Baju Pengantin"],
    },
    notes: "Mahasiswa, sering sewa untuk acara kampus",
    rating: 4.8,
    joinDate: "2023-09-10",
  },
]

interface CustomerSelectorProps {
  onCustomerSelect: (customer: any) => void
  selectedCustomer?: any
}

export function CustomerSelector({ onCustomerSelect, selectedCustomer }: CustomerSelectorProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [showNewCustomerForm, setShowNewCustomerForm] = useState(false)
  const [newCustomerData, setNewCustomerData] = useState({
    name: "",
    email: "",
    phone: "",
    address: "",
    notes: "",
  })

  const filteredCustomers = mockCustomers.filter((customer) => {
    const matchesSearch =
      customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.phone.includes(searchTerm)
    const matchesStatus = statusFilter === "all" || customer.status === statusFilter
    return matchesSearch && matchesStatus
  })

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "vip":
        return <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-100">VIP</Badge>
      case "regular":
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">Regular</Badge>
      case "new":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Baru</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  const handleCustomerSelect = (customer: any) => {
    onCustomerSelect(customer)
    setIsOpen(false)
  }

  const handleNewCustomerSubmit = () => {
    if (newCustomerData.name && newCustomerData.phone) {
      const newCustomer = {
        id: `CUST-${Date.now()}`,
        ...newCustomerData,
        avatar: "/placeholder.svg?height=40&width=40",
        status: "new",
        totalOrders: 0,
        totalSpent: 0,
        lastOrder: null,
        preferences: {
          size: "",
          colors: [],
          categories: [],
        },
        rating: 0,
        joinDate: new Date().toISOString().split("T")[0],
      }

      onCustomerSelect(newCustomer)
      setNewCustomerData({
        name: "",
        email: "",
        phone: "",
        address: "",
        notes: "",
      })
      setShowNewCustomerForm(false)
      setIsOpen(false)
    }
  }

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium">Informasi Pelanggan</h3>
          <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
              <Button variant="outline">{selectedCustomer ? "Ganti Pelanggan" : "Pilih Pelanggan"}</Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Pilih Pelanggan
                </DialogTitle>
              </DialogHeader>

              <Tabs defaultValue="existing" className="space-y-6">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="existing">Pelanggan Existing</TabsTrigger>
                  <TabsTrigger value="new">Pelanggan Baru</TabsTrigger>
                </TabsList>

                <TabsContent value="existing" className="space-y-6">
                  {/* Search and Filter */}
                  <div className="flex gap-4">
                    <div className="flex-1">
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                        <Input
                          placeholder="Cari berdasarkan nama, email, atau telepon..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="pl-10"
                        />
                      </div>
                    </div>
                    <Select value={statusFilter} onValueChange={setStatusFilter}>
                      <SelectTrigger className="w-48">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Semua Status</SelectItem>
                        <SelectItem value="vip">VIP</SelectItem>
                        <SelectItem value="regular">Regular</SelectItem>
                        <SelectItem value="new">Baru</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {/* Customer List */}
                  <div className="space-y-4 max-h-96 overflow-y-auto">
                    {filteredCustomers.map((customer) => (
                      <Card
                        key={customer.id}
                        className="cursor-pointer transition-all hover:shadow-md"
                        onClick={() => handleCustomerSelect(customer)}
                      >
                        <CardContent className="p-4">
                          <div className="flex items-start gap-4">
                            <Avatar className="h-12 w-12">
                              <AvatarImage src={customer.avatar || "/placeholder.svg"} alt={customer.name} />
                              <AvatarFallback>{customer.name.charAt(0)}</AvatarFallback>
                            </Avatar>

                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2 mb-1">
                                <h4 className="font-medium text-gray-900">{customer.name}</h4>
                                {getStatusBadge(customer.status)}
                              </div>

                              <div className="space-y-1 text-sm text-gray-600">
                                <div className="flex items-center gap-2">
                                  <Mail className="h-3 w-3" />
                                  <span>{customer.email}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                  <Phone className="h-3 w-3" />
                                  <span>{customer.phone}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                  <MapPin className="h-3 w-3" />
                                  <span className="truncate">{customer.address}</span>
                                </div>
                              </div>
                            </div>

                            <div className="text-right text-sm">
                              <div className="flex items-center gap-1 mb-1">
                                <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                                <span className="font-medium">{customer.rating}</span>
                              </div>
                              <div className="text-gray-600">
                                <div>{customer.totalOrders} pesanan</div>
                                <div className="font-medium text-green-600">
                                  Rp {customer.totalSpent.toLocaleString()}
                                </div>
                              </div>
                            </div>
                          </div>

                          {customer.notes && (
                            <div className="mt-3 pt-3 border-t">
                              <p className="text-sm text-gray-600 italic">"{customer.notes}"</p>
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    ))}
                  </div>

                  {filteredCustomers.length === 0 && (
                    <div className="text-center py-8">
                      <User className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">Tidak ada pelanggan ditemukan</h3>
                      <p className="text-gray-500">Coba ubah kata kunci pencarian atau filter</p>
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="new" className="space-y-6">
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="newName">Nama Lengkap *</Label>
                        <Input
                          id="newName"
                          value={newCustomerData.name}
                          onChange={(e) => setNewCustomerData({ ...newCustomerData, name: e.target.value })}
                          placeholder="Masukkan nama lengkap"
                        />
                      </div>
                      <div>
                        <Label htmlFor="newPhone">Nomor Telepon *</Label>
                        <Input
                          id="newPhone"
                          value={newCustomerData.phone}
                          onChange={(e) => setNewCustomerData({ ...newCustomerData, phone: e.target.value })}
                          placeholder="+62 812-3456-7890"
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="newEmail">Email</Label>
                      <Input
                        id="newEmail"
                        type="email"
                        value={newCustomerData.email}
                        onChange={(e) => setNewCustomerData({ ...newCustomerData, email: e.target.value })}
                        placeholder="<EMAIL>"
                      />
                    </div>

                    <div>
                      <Label htmlFor="newAddress">Alamat</Label>
                      <Input
                        id="newAddress"
                        value={newCustomerData.address}
                        onChange={(e) => setNewCustomerData({ ...newCustomerData, address: e.target.value })}
                        placeholder="Alamat lengkap"
                      />
                    </div>

                    <div>
                      <Label htmlFor="newNotes">Catatan</Label>
                      <Input
                        id="newNotes"
                        value={newCustomerData.notes}
                        onChange={(e) => setNewCustomerData({ ...newCustomerData, notes: e.target.value })}
                        placeholder="Catatan khusus tentang pelanggan"
                      />
                    </div>

                    <div className="flex justify-end gap-2">
                      <Button variant="outline" onClick={() => setShowNewCustomerForm(false)}>
                        Batal
                      </Button>
                      <Button
                        onClick={handleNewCustomerSubmit}
                        disabled={!newCustomerData.name || !newCustomerData.phone}
                        className="bg-rose-600 hover:bg-rose-700"
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        Tambah Pelanggan
                      </Button>
                    </div>
                  </div>
                </TabsContent>
              </Tabs>
            </DialogContent>
          </Dialog>
        </div>

        {selectedCustomer ? (
          <div className="space-y-4">
            <div className="flex items-start gap-4 p-4 bg-gray-50 rounded-lg">
              <Avatar className="h-12 w-12">
                <AvatarImage src={selectedCustomer.avatar || "/placeholder.svg"} alt={selectedCustomer.name} />
                <AvatarFallback>{selectedCustomer.name.charAt(0)}</AvatarFallback>
              </Avatar>

              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <h4 className="font-medium text-gray-900">{selectedCustomer.name}</h4>
                  {getStatusBadge(selectedCustomer.status)}
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <Mail className="h-3 w-3" />
                      <span>{selectedCustomer.email}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Phone className="h-3 w-3" />
                      <span>{selectedCustomer.phone}</span>
                    </div>
                  </div>
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <Package className="h-3 w-3" />
                      <span>{selectedCustomer.totalOrders} pesanan</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-3 w-3" />
                      <span>Terakhir: {selectedCustomer.lastOrder || "Belum pernah"}</span>
                    </div>
                  </div>
                </div>
              </div>

              <div className="text-right">
                <div className="flex items-center gap-1 mb-1">
                  <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  <span className="font-medium">{selectedCustomer.rating}</span>
                </div>
                <div className="text-sm font-medium text-green-600">
                  Rp {selectedCustomer.totalSpent.toLocaleString()}
                </div>
              </div>
            </div>

            {selectedCustomer.preferences && (
              <div className="p-4 bg-blue-50 rounded-lg">
                <h5 className="font-medium text-blue-900 mb-2">Preferensi Pelanggan</h5>
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="text-blue-700 font-medium">Ukuran:</span>
                    <span className="ml-1">{selectedCustomer.preferences.size || "Belum ada"}</span>
                  </div>
                  <div>
                    <span className="text-blue-700 font-medium">Warna:</span>
                    <span className="ml-1">{selectedCustomer.preferences.colors.join(", ") || "Belum ada"}</span>
                  </div>
                  <div>
                    <span className="text-blue-700 font-medium">Kategori:</span>
                    <span className="ml-1">{selectedCustomer.preferences.categories.join(", ") || "Belum ada"}</span>
                  </div>
                </div>
              </div>
            )}

            {selectedCustomer.notes && (
              <div className="p-3 bg-yellow-50 rounded-lg">
                <p className="text-sm text-yellow-800 italic">"{selectedCustomer.notes}"</p>
              </div>
            )}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <User className="h-12 w-12 mx-auto mb-3 opacity-50" />
            <p>Belum ada pelanggan yang dipilih</p>
            <p className="text-sm">Klik "Pilih Pelanggan" untuk memilih atau menambah pelanggan baru</p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
