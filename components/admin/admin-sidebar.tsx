"use client"

import { useState } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import {
  LayoutDashboard,
  Package,
  ShoppingCart,
  Users,
  BarChart3,
  Settings,
  Calendar,
  Bell,
  FileText,
  Plus,
  Wrench,
  Camera,
  QrCode,
  Clock,
  Copy,
  CreditCard,
  MessageSquare,
  ChevronDown,
  ChevronRight,
} from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"

const navigation = [
  {
    name: "Dashboard",
    href: "/admin",
    icon: LayoutDashboard,
    current: false,
  },
  {
    name: "Produk",
    icon: Package,
    current: false,
    children: [
      { name: "<PERSON><PERSON><PERSON>duk", href: "/admin/products" },
      { name: "Tambah Produk", href: "/admin/products/add" },
      { name: "<PERSON><PERSON><PERSON>", href: "/admin/products/categories" },
      { name: "<PERSON><PERSON>", href: "/admin/products/stock" },
    ],
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    icon: ShoppingCart,
    current: false,
    badge: "12",
    children: [
      { name: "Semua Pesanan", href: "/admin/orders" },
      { name: "Pesanan Manual", href: "/admin/orders/manual" },
      { name: "Pesanan Pending", href: "/admin/orders/pending" },
      { name: "Pesanan Aktif", href: "/admin/orders/active" },
      { name: "Riwayat Pesanan", href: "/admin/orders/history" },
    ],
  },
  {
    name: "Pelanggan",
    href: "/admin/users",
    icon: Users,
    current: false,
  },
  {
    name: "Inventori",
    href: "/admin/inventory",
    icon: Calendar,
    current: false,
    badge: "3",
  },
  {
    name: "Laporan",
    icon: BarChart3,
    current: false,
    children: [
      { name: "Laporan Penjualan", href: "/admin/reports/sales" },
      { name: "Laporan Produk", href: "/admin/reports/products" },
      { name: "Laporan Pelanggan", href: "/admin/reports/customers" },
      { name: "Laporan Keuangan", href: "/admin/reports/financial" },
    ],
  },
  {
    name: "Pembayaran",
    icon: CreditCard,
    current: false,
    children: [
      { name: "Transaksi", href: "/admin/payments/transactions" },
      { name: "Metode Pembayaran", href: "/admin/payments/methods" },
      { name: "Refund", href: "/admin/payments/refunds" },
    ],
  },
  {
    name: "Komunikasi",
    icon: MessageSquare,
    current: false,
    children: [
      { name: "WhatsApp", href: "/admin/communication/whatsapp" },
      { name: "Email", href: "/admin/communication/email" },
      { name: "SMS", href: "/admin/communication/sms" },
      { name: "Template", href: "/admin/communication/templates" },
    ],
  },
  {
    name: "Notifikasi",
    href: "/admin/notifications",
    icon: Bell,
    current: false,
    badge: "5",
  },
]

const toolsNavigation = [
  {
    name: "Manajemen Foto",
    href: "/admin/tools/photos",
    icon: Camera,
  },
  {
    name: "Barcode/QR",
    href: "/admin/tools/barcode",
    icon: QrCode,
  },
  {
    name: "Jadwal Maintenance",
    href: "/admin/tools/maintenance",
    icon: Wrench,
  },
  {
    name: "Operasi Bulk",
    href: "/admin/tools/bulk",
    icon: Copy,
  },
  {
    name: "Analitik Durasi",
    href: "/admin/tools/analytics",
    icon: Clock,
  },
]

const settingsNavigation = [
  {
    name: "Pengaturan Umum",
    href: "/admin/settings/general",
    icon: Settings,
  },
  {
    name: "Konfigurasi",
    href: "/admin/settings/config",
    icon: Settings,
  },
  {
    name: "Template Pesanan",
    href: "/admin/settings/templates",
    icon: FileText,
  },
]

export function AdminSidebar() {
  const pathname = usePathname()
  const [openSections, setOpenSections] = useState<string[]>(["Pesanan", "Produk"])

  const toggleSection = (sectionName: string) => {
    setOpenSections((prev) =>
      prev.includes(sectionName) ? prev.filter((name) => name !== sectionName) : [...prev, sectionName],
    )
  }

  const isCurrentPath = (href: string) => {
    if (href === "/admin") {
      return pathname === href
    }
    return pathname.startsWith(href)
  }

  return (
    <div className="fixed inset-y-0 left-0 z-50 w-64 bg-white border-r border-brand-light lg:block hidden">
      <div className="flex flex-col h-full">
        {/* Logo */}
        <div className="flex items-center h-16 px-6 border-b border-brand-light">
          <Link href="/admin" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-brand-dark rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">GSJ</span>
            </div>
            <div>
              <h1 className="text-lg font-bold text-brand-dark">GaunSyariJogja</h1>
              <p className="text-xs text-brand-medium">Admin Panel</p>
            </div>
          </Link>
        </div>

        {/* Navigation */}
        <nav className="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
          {/* Quick Actions */}
          <div className="mb-6">
            <p className="px-2 text-xs font-semibold text-brand-medium uppercase tracking-wider mb-3">Quick Actions</p>
            <div className="space-y-1">
              <Button asChild size="sm" className="w-full justify-start bg-brand-dark hover:bg-brand-medium text-white">
                <Link href="/admin/orders/manual">
                  <Plus className="h-4 w-4 mr-2" />
                  Pesanan Manual
                </Link>
              </Button>
              <Button
                asChild
                variant="outline"
                size="sm"
                className="w-full justify-start bg-transparent border-brand-light text-brand-dark hover:bg-brand-light"
              >
                <Link href="/admin/products/add">
                  <Plus className="h-4 w-4 mr-2" />
                  Tambah Produk
                </Link>
              </Button>
            </div>
          </div>

          {/* Main Navigation */}
          <div className="space-y-1">
            <p className="px-2 text-xs font-semibold text-brand-medium uppercase tracking-wider mb-3">Main Menu</p>
            {navigation.map((item) => {
              if (item.children) {
                const isOpen = openSections.includes(item.name)
                const hasCurrentChild = item.children.some((child) => isCurrentPath(child.href))

                return (
                  <Collapsible key={item.name} open={isOpen} onOpenChange={() => toggleSection(item.name)}>
                    <CollapsibleTrigger asChild>
                      <Button
                        variant="ghost"
                        className={cn(
                          "w-full justify-between px-2 py-2 text-left font-normal text-brand-dark hover:bg-brand-light",
                          (hasCurrentChild || isOpen) && "bg-brand-light text-brand-dark",
                        )}
                      >
                        <div className="flex items-center">
                          <item.icon className="h-4 w-4 mr-3" />
                          <span>{item.name}</span>
                          {item.badge && (
                            <Badge variant="secondary" className="ml-auto mr-2 bg-brand-medium text-white">
                              {item.badge}
                            </Badge>
                          )}
                        </div>
                        {isOpen ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
                      </Button>
                    </CollapsibleTrigger>
                    <CollapsibleContent className="space-y-1 ml-7 mt-1">
                      {item.children.map((child) => (
                        <Button
                          key={child.href}
                          asChild
                          variant="ghost"
                          size="sm"
                          className={cn(
                            "w-full justify-start px-2 py-1 text-sm font-normal text-brand-dark hover:bg-brand-light",
                            isCurrentPath(child.href) && "bg-brand-medium text-white border-r-2 border-brand-dark",
                          )}
                        >
                          <Link href={child.href}>{child.name}</Link>
                        </Button>
                      ))}
                    </CollapsibleContent>
                  </Collapsible>
                )
              }

              return (
                <Button
                  key={item.name}
                  asChild
                  variant="ghost"
                  className={cn(
                    "w-full justify-start px-2 py-2 font-normal text-brand-dark hover:bg-brand-light",
                    isCurrentPath(item.href) && "bg-brand-medium text-white border-r-2 border-brand-dark",
                  )}
                >
                  <Link href={item.href}>
                    <item.icon className="h-4 w-4 mr-3" />
                    <span>{item.name}</span>
                    {item.badge && (
                      <Badge variant="secondary" className="ml-auto bg-brand-medium text-white">
                        {item.badge}
                      </Badge>
                    )}
                  </Link>
                </Button>
              )
            })}
          </div>

          {/* Tools */}
          <div className="space-y-1 pt-6">
            <p className="px-2 text-xs font-semibold text-brand-medium uppercase tracking-wider mb-3">Tools</p>
            {toolsNavigation.map((item) => (
              <Button
                key={item.name}
                asChild
                variant="ghost"
                size="sm"
                className={cn(
                  "w-full justify-start px-2 py-1 font-normal text-sm text-brand-dark hover:bg-brand-light",
                  isCurrentPath(item.href) && "bg-brand-medium text-white border-r-2 border-brand-dark",
                )}
              >
                <Link href={item.href}>
                  <item.icon className="h-4 w-4 mr-3" />
                  <span>{item.name}</span>
                </Link>
              </Button>
            ))}
          </div>

          {/* Settings */}
          <div className="space-y-1 pt-6">
            <p className="px-2 text-xs font-semibold text-brand-medium uppercase tracking-wider mb-3">Settings</p>
            {settingsNavigation.map((item) => (
              <Button
                key={item.name}
                asChild
                variant="ghost"
                size="sm"
                className={cn(
                  "w-full justify-start px-2 py-1 font-normal text-sm text-brand-dark hover:bg-brand-light",
                  isCurrentPath(item.href) && "bg-brand-medium text-white border-r-2 border-brand-dark",
                )}
              >
                <Link href={item.href}>
                  <item.icon className="h-4 w-4 mr-3" />
                  <span>{item.name}</span>
                </Link>
              </Button>
            ))}
          </div>
        </nav>

        {/* Footer */}
        <div className="p-4 border-t border-brand-light">
          <div className="text-xs text-brand-medium text-center">
            <p>GaunSyariJogja Admin</p>
            <p>Version 1.0.0</p>
          </div>
        </div>
      </div>
    </div>
  )
}
