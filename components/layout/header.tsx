"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON>u, Heart, User, Search, Phone } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"

export default function Header() {
  const [isOpen, setIsOpen] = useState(false)

  const navigation = [
    { name: "<PERSON><PERSON><PERSON>", href: "/" },
    { name: "Katalog", href: "/catalog" },
    { name: "Tentang Kami", href: "/about" },
    { name: "Konta<PERSON>", href: "/contact" },
  ]

  return (
    <header className="bg-white shadow-sm border-b border-brand-light sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href="/" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-brand-dark rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-sm">GSJ</span>
            </div>
            <span className="font-bold text-xl text-brand-dark">GaunSyariJogja</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="text-brand-dark hover:text-brand-medium font-medium transition-colors"
              >
                {item.name}
              </Link>
            ))}
            <Link href="/admin" className="text-brand-dark hover:text-brand-medium font-medium transition-colors">
              Admin
            </Link>
          </nav>

          {/* Desktop Actions */}
          <div className="hidden md:flex items-center space-x-4">
            <Button
              variant="ghost"
              size="icon"
              className="text-brand-dark hover:text-brand-medium hover:bg-brand-light"
            >
              <Search className="h-5 w-5" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="text-brand-dark hover:text-brand-medium hover:bg-brand-light"
            >
              <Heart className="h-5 w-5" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="text-brand-dark hover:text-brand-medium hover:bg-brand-light"
            >
              <User className="h-5 w-5" />
            </Button>
            <Button asChild className="bg-brand-dark hover:bg-brand-medium text-white">
              <Link href="tel:+6281234567890" className="flex items-center gap-2">
                <Phone className="h-4 w-4" />
                Hubungi Kami
              </Link>
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="icon" className="md:hidden text-brand-dark">
                <Menu className="h-6 w-6" />
              </Button>
            </SheetTrigger>
            <SheetContent side="right" className="w-80 bg-white">
              <div className="flex items-center justify-between mb-8">
                <Link href="/" className="flex items-center space-x-2">
                  <div className="w-8 h-8 bg-brand-dark rounded-lg flex items-center justify-center">
                    <span className="text-white font-bold text-sm">GSJ</span>
                  </div>
                  <span className="font-bold text-xl text-brand-dark">GaunSyariJogja</span>
                </Link>
              </div>

              <nav className="space-y-4">
                {navigation.map((item) => (
                  <Link
                    key={item.name}
                    href={item.href}
                    className="block text-lg font-medium text-brand-dark hover:text-brand-medium transition-colors"
                    onClick={() => setIsOpen(false)}
                  >
                    {item.name}
                  </Link>
                ))}
              </nav>

              <div className="mt-8 space-y-4">
                <Button
                  variant="outline"
                  className="w-full justify-start bg-transparent border-brand-light text-brand-dark hover:bg-brand-light"
                >
                  <Search className="h-4 w-4 mr-2" />
                  Cari Gaun
                </Button>
                <Button
                  variant="outline"
                  className="w-full justify-start bg-transparent border-brand-light text-brand-dark hover:bg-brand-light"
                >
                  <Heart className="h-4 w-4 mr-2" />
                  Wishlist
                </Button>
                <Button
                  variant="outline"
                  className="w-full justify-start bg-transparent border-brand-light text-brand-dark hover:bg-brand-light"
                >
                  <User className="h-4 w-4 mr-2" />
                  Akun Saya
                </Button>
                <Button asChild className="w-full bg-brand-dark hover:bg-brand-medium text-white">
                  <Link href="tel:+6281234567890" className="flex items-center justify-center gap-2">
                    <Phone className="h-4 w-4" />
                    Hubungi Kami
                  </Link>
                </Button>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  )
}
