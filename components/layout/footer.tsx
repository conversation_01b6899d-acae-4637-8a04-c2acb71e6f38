import Link from "next/link"
import { MapPin, Phone, Mail, Instagram, Facebook, Youtube } from "lucide-react"

export default function Footer() {
  return (
    <footer className="bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 py-12">
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="space-y-4">
            <Link href="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-rose-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">GSJ</span>
              </div>
              <span className="font-bold text-xl">GaunSyariJogja</span>
            </Link>
            <p className="text-gray-300 text-sm leading-relaxed">
              Penyedia layanan sewa gaun syar'i terpercaya di Yogyakarta. <PERSON>leksi terlengkap dengan kualitas premium
              untuk acara spesial Anda.
            </p>
            <div className="flex space-x-4">
              <Link href="#" className="text-gray-400 hover:text-rose-400 transition-colors">
                <Instagram className="h-5 w-5" />
              </Link>
              <Link href="#" className="text-gray-400 hover:text-rose-400 transition-colors">
                <Facebook className="h-5 w-5" />
              </Link>
              <Link href="#" className="text-gray-400 hover:text-rose-400 transition-colors">
                <Youtube className="h-5 w-5" />
              </Link>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="font-semibold text-lg mb-4">Menu Utama</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/" className="text-gray-300 hover:text-white transition-colors">
                  Beranda
                </Link>
              </li>
              <li>
                <Link href="/catalog" className="text-gray-300 hover:text-white transition-colors">
                  Katalog Gaun
                </Link>
              </li>
              <li>
                <Link href="/about" className="text-gray-300 hover:text-white transition-colors">
                  Tentang Kami
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-gray-300 hover:text-white transition-colors">
                  Kontak
                </Link>
              </li>
            </ul>
          </div>

          {/* Categories */}
          <div>
            <h3 className="font-semibold text-lg mb-4">Kategori</h3>
            <ul className="space-y-2">
              <li>
                <Link href="/catalog?category=pengantin" className="text-gray-300 hover:text-white transition-colors">
                  Baju Pengantin
                </Link>
              </li>
              <li>
                <Link href="/catalog?category=ibu" className="text-gray-300 hover:text-white transition-colors">
                  Baju Orang Tua (Ibu)
                </Link>
              </li>
              <li>
                <Link href="/catalog?category=ayah" className="text-gray-300 hover:text-white transition-colors">
                  Baju Ayah
                </Link>
              </li>
              <li>
                <Link href="/catalog?category=akad" className="text-gray-300 hover:text-white transition-colors">
                  Gaun Akad
                </Link>
              </li>
              <li>
                <Link href="/catalog?category=pesta" className="text-gray-300 hover:text-white transition-colors">
                  Gaun Pesta
                </Link>
              </li>
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="font-semibold text-lg mb-4">Kontak Kami</h3>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <MapPin className="h-5 w-5 text-rose-400 mt-0.5 flex-shrink-0" />
                <p className="text-gray-300 text-sm">
                  Jl. Malioboro No. 123
                  <br />
                  Yogyakarta 55271
                </p>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="h-5 w-5 text-rose-400 flex-shrink-0" />
                <p className="text-gray-300 text-sm">+62 812-3456-7890</p>
              </div>
              <div className="flex items-center space-x-3">
                <Mail className="h-5 w-5 text-rose-400 flex-shrink-0" />
                <p className="text-gray-300 text-sm"><EMAIL></p>
              </div>
            </div>
          </div>
        </div>

        <div className="border-t border-gray-800 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">© 2024 GaunSyariJogja.com. All rights reserved.</p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <Link href="/privacy" className="text-gray-400 hover:text-white text-sm transition-colors">
                Kebijakan Privasi
              </Link>
              <Link href="/terms" className="text-gray-400 hover:text-white text-sm transition-colors">
                Syarat & Ketentuan
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
