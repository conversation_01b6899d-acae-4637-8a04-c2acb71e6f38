<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Product;
use App\Models\ProductAvailability;
use Carbon\Carbon;

class ProductAvailabilitySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $products = Product::all();

        foreach ($products as $product) {
            // Simulate some unavailable dates for each product
            $unavailableDates = $this->generateUnavailableDates();

            foreach ($unavailableDates as $dateInfo) {
                ProductAvailability::updateOrCreate(
                    [
                        'product_id' => $product->id,
                        'unavailable_date' => $dateInfo['date'],
                    ],
                    [
                        'reason' => $dateInfo['reason'],
                        'notes' => $dateInfo['notes'],
                    ]
                );
            }
        }
    }

    private function generateUnavailableDates(): array
    {
        $dates = [];
        $today = Carbon::today();

        // Generate unavailable dates for the next 3 months
        for ($month = 0; $month < 3; $month++) {
            $currentMonth = $today->copy()->addMonths($month);

            // Add some random booked dates (simulating existing bookings)
            $bookedDates = $this->generateBookedDates($currentMonth);
            $dates = array_merge($dates, $bookedDates);

            // Add some maintenance dates
            $maintenanceDates = $this->generateMaintenanceDates($currentMonth);
            $dates = array_merge($dates, $maintenanceDates);

            // Add some blocked dates (holidays, etc.)
            $blockedDates = $this->generateBlockedDates($currentMonth);
            $dates = array_merge($dates, $blockedDates);
        }

        return $dates;
    }

    private function generateBookedDates(Carbon $month): array
    {
        $dates = [];
        $daysInMonth = $month->daysInMonth;

        // Generate 3-5 random booking periods per month
        $bookingCount = rand(3, 5);

        for ($i = 0; $i < $bookingCount; $i++) {
            $startDay = rand(1, $daysInMonth - 3);
            $duration = rand(1, 3); // 1-3 days booking

            for ($day = 0; $day < $duration; $day++) {
                $date = $month->copy()->day($startDay + $day);

                // Only add future dates
                if ($date->isFuture() || $date->isToday()) {
                    $dates[] = [
                        'date' => $date->format('Y-m-d'),
                        'reason' => 'booked',
                        'notes' => 'Sudah dibooking oleh customer lain'
                    ];
                }
            }
        }

        return $dates;
    }

    private function generateMaintenanceDates(Carbon $month): array
    {
        $dates = [];

        // Add 1-2 maintenance days per month
        $maintenanceCount = rand(1, 2);

        for ($i = 0; $i < $maintenanceCount; $i++) {
            $day = rand(1, $month->daysInMonth);
            $date = $month->copy()->day($day);

            // Only add future dates
            if ($date->isFuture() || $date->isToday()) {
                $dates[] = [
                    'date' => $date->format('Y-m-d'),
                    'reason' => 'maintenance',
                    'notes' => 'Sedang dalam perawatan/pembersihan'
                ];
            }
        }

        return $dates;
    }

    private function generateBlockedDates(Carbon $month): array
    {
        $dates = [];

        // Add some specific blocked dates (holidays, special events)
        $specialDates = [
            // Valentine's Day (high demand, block some products)
            '2025-02-14' => 'Hari Valentine - permintaan tinggi',
            '2025-02-15' => 'Hari setelah Valentine - pembersihan khusus',

            // Indonesian holidays
            '2025-03-29' => 'Hari Raya Nyepi',
            '2025-04-10' => 'Wafat Isa Al Masih',
            '2025-05-29' => 'Hari Raya Waisak',

            // Ramadan and Eid (high demand period)
            '2025-03-30' => 'Awal Ramadan - persiapan khusus',
            '2025-04-29' => 'Persiapan Hari Raya Idul Fitri',
            '2025-04-30' => 'Hari Raya Idul Fitri',
            '2025-05-01' => 'Hari Buruh / Hari kedua Idul Fitri',
        ];

        foreach ($specialDates as $dateStr => $note) {
            $date = Carbon::parse($dateStr);

            // Only add if it's in the current month and future
            if ($date->month === $month->month &&
                $date->year === $month->year &&
                ($date->isFuture() || $date->isToday())) {

                // Only block some products randomly for special dates
                if (rand(1, 3) === 1) { // 33% chance to block
                    $dates[] = [
                        'date' => $dateStr,
                        'reason' => 'blocked',
                        'notes' => $note
                    ];
                }
            }
        }

        return $dates;
    }
}
