<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $products = [
            [
                'sku' => 'GSJ-001',
                'name' => 'Gaun Pengantin Syar\'i Mewah',
                'description' => 'Gaun pengantin syar\'i dengan bordir emas dan detail yang mewah. Dilengkapi dengan hijab pengantin dan aksesoris.',
                'category_id' => 1, // Baju Pengantin
                'price' => 300000,
                'deposit_percentage' => 50.00,
                'sizes' => ['S', 'M', 'L', 'XL'],
                'colors' => ['Putih Gading', 'Broken White', 'Cream'],
                'color' => 'Putih Gading',
                'motif' => 'Bordir Emas',
                'condition' => 'excellent',
                'status' => 'available',
                'notes' => 'Gaun premium dengan bordir emas',
                'total_rentals' => 15,
                'last_rented' => '2024-03-10',
                'rating' => 4.9,
                'rating_count' => 12,
                'is_featured' => true,
                'is_active' => true,
            ],
            [
                'sku' => 'GSJ-002',
                'name' => 'Gaun Ibu Pengantin Elegant',
                'description' => 'Gaun untuk ibu pengantin dengan desain elegant dan detail renda premium. Nyaman digunakan seharian.',
                'category_id' => 2, // Baju Orang Tua (Ibu)
                'price' => 200000,
                'deposit_percentage' => 50.00,
                'sizes' => ['M', 'L', 'XL', 'XXL'],
                'colors' => ['Navy Blue', 'Maroon', 'Dark Green'],
                'color' => 'Navy Blue',
                'motif' => 'Lace Premium',
                'condition' => 'good',
                'status' => 'available',
                'notes' => 'Detail renda premium',
                'total_rentals' => 12,
                'last_rented' => '2024-03-15',
                'rating' => 4.8,
                'rating_count' => 10,
                'is_featured' => true,
                'is_active' => true,
            ],
            [
                'sku' => 'GSJ-003',
                'name' => 'Baju Koko Ayah Premium',
                'description' => 'Baju koko premium untuk ayah pengantin dengan bahan berkualitas tinggi dan desain yang elegan.',
                'category_id' => 3, // Baju Ayah
                'price' => 175000,
                'deposit_percentage' => 50.00,
                'sizes' => ['M', 'L', 'XL', 'XXL'],
                'colors' => ['Hitam', 'Navy', 'Abu-abu'],
                'color' => 'Hitam',
                'motif' => 'Polos Elegan',
                'condition' => 'excellent',
                'status' => 'available',
                'notes' => 'Bahan premium, nyaman digunakan',
                'total_rentals' => 8,
                'last_rented' => '2024-03-08',
                'rating' => 4.7,
                'rating_count' => 6,
                'is_featured' => true,
                'is_active' => true,
            ],
        ];

        foreach ($products as $product) {
            $createdProduct = \App\Models\Product::create($product);

            // Add sample images for each product
            \App\Models\ProductImage::create([
                'product_id' => $createdProduct->id,
                'image_path' => '/placeholder.jpg',
                'alt_text' => $product['name'],
                'sort_order' => 1,
                'is_primary' => true,
            ]);
        }
    }
}
