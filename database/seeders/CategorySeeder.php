<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Baju Pengantin',
                'slug' => 'baju-pengantin',
                'description' => 'Koleksi gaun pengantin syar\'i yang elegan dan mewah',
                'is_active' => true,
            ],
            [
                'name' => 'Baju Orang Tua (Ibu)',
                'slug' => 'baju-orang-tua-ibu',
                'description' => 'Gaun untuk ibu pengantin dengan desain yang anggun',
                'is_active' => true,
            ],
            [
                'name' => 'Baju Ayah',
                'slug' => 'baju-ayah',
                'description' => 'Baju koko dan kemeja untuk ayah pengantin',
                'is_active' => true,
            ],
            [
                'name' => 'Baju Wisuda',
                'slug' => 'baju-wisuda',
                'description' => '<PERSON>au<PERSON> syar\'i untuk acara wisuda',
                'is_active' => true,
            ],
            [
                'name' => 'Baju Pesta',
                'slug' => 'baju-pesta',
                'description' => 'Gaun syar\'i untuk acara pesta dan formal',
                'is_active' => true,
            ],
        ];

        foreach ($categories as $category) {
            \App\Models\Category::create($category);
        }
    }
}
