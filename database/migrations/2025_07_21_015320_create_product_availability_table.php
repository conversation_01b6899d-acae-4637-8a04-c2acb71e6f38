<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('product_availabilities', function (Blueprint $table) {
            $table->id();
            $table->foreignId('product_id')->constrained()->onDelete('cascade');
            $table->date('unavailable_date');
            $table->enum('reason', ['booked', 'maintenance', 'blocked'])->default('booked');
            $table->string('notes')->nullable();
            $table->timestamps();

            $table->unique(['product_id', 'unavailable_date']);
            $table->index(['product_id', 'unavailable_date']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('product_availabilities');
    }
};
