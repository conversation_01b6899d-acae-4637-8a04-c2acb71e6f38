<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->string('sku')->unique(); // GSJ-001, GSJ-002, etc.
            $table->string('name');
            $table->text('description')->nullable();
            $table->foreignId('category_id')->constrained()->onDelete('cascade');
            $table->decimal('price', 10, 2); // Daily rental price
            $table->decimal('deposit_percentage', 5, 2)->default(50.00); // Deposit percentage
            $table->json('sizes')->nullable(); // Available sizes: S, M, L, XL
            $table->json('colors')->nullable(); // Available colors
            $table->string('color')->nullable(); // Primary color
            $table->string('motif')->nullable(); // Pattern/motif
            $table->enum('condition', ['excellent', 'good', 'fair', 'poor'])->default('excellent');
            $table->enum('status', ['available', 'rented', 'maintenance', 'cleaning', 'damaged'])->default('available');
            $table->text('notes')->nullable();
            $table->integer('total_rentals')->default(0);
            $table->date('last_rented')->nullable();
            $table->decimal('rating', 3, 2)->default(0.00);
            $table->integer('rating_count')->default(0);
            $table->boolean('is_featured')->default(false);
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
