<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('order_id')->constrained()->onDelete('cascade');
            $table->string('payment_reference')->unique(); // Payment reference number
            $table->enum('payment_type', ['deposit', 'full_payment', 'remaining_payment'])->default('deposit');
            $table->enum('payment_method', ['bank_transfer', 'cash', 'e_wallet', 'credit_card'])->default('bank_transfer');
            $table->decimal('amount', 12, 2);
            $table->enum('status', ['pending', 'waiting_confirmation', 'confirmed', 'failed', 'refunded'])->default('pending');
            $table->string('bank_name')->nullable();
            $table->string('account_name')->nullable();
            $table->string('account_number')->nullable();
            $table->string('proof_of_payment')->nullable(); // File path to payment proof
            $table->text('notes')->nullable();
            $table->timestamp('paid_at')->nullable();
            $table->timestamp('confirmed_at')->nullable();
            $table->foreignId('confirmed_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};
