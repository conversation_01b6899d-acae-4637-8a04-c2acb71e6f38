<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->string('order_number')->unique(); // GSJ1710501234
            $table->foreignId('customer_id')->constrained()->onDelete('cascade');
            $table->date('rental_start_date');
            $table->date('rental_end_date');
            $table->integer('rental_days');
            $table->decimal('subtotal', 12, 2);
            $table->decimal('deposit_amount', 12, 2);
            $table->decimal('total_amount', 12, 2);
            $table->enum('status', ['pending_payment', 'confirmed', 'processing', 'shipped', 'delivered', 'completed', 'cancelled'])->default('pending_payment');
            $table->enum('payment_status', ['pending', 'waiting_confirmation', 'confirmed', 'failed', 'refunded'])->default('pending');
            $table->text('delivery_address');
            $table->enum('delivery_method', ['pickup', 'delivery'])->default('delivery');
            $table->text('notes')->nullable();
            $table->timestamp('confirmed_at')->nullable();
            $table->timestamp('shipped_at')->nullable();
            $table->timestamp('delivered_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
