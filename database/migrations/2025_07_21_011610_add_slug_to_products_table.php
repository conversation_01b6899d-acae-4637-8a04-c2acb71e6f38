<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Generate slugs for existing products
        $products = \App\Models\Product::all();
        foreach ($products as $product) {
            if (empty($product->slug)) {
                $product->slug = \App\Models\Product::generateSlug($product->name);
                $product->save();
            }
        }

        // Now make slug unique
        Schema::table('products', function (Blueprint $table) {
            $table->unique('slug');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('products', function (Blueprint $table) {
            $table->dropUnique(['slug']);
        });
    }
};
