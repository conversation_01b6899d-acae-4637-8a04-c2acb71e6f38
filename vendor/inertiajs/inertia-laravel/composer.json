{"name": "inertiajs/inertia-laravel", "type": "library", "description": "The Laravel adapter for Inertia.js.", "keywords": ["laravel", "inertia"], "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://reinink.ca"}], "autoload": {"psr-4": {"Inertia\\": "src"}, "files": ["./helpers.php"]}, "autoload-dev": {"psr-4": {"Inertia\\Tests\\": "tests/"}}, "require": {"php": "^8.1.0", "ext-json": "*", "laravel/framework": "^10.0|^11.0|^12.0", "symfony/console": "^6.2|^7.0"}, "require-dev": {"guzzlehttp/guzzle": "^7.2", "roave/security-advisories": "dev-master", "orchestra/testbench": "^8.0|^9.2|^10.0", "mockery/mockery": "^1.3.3", "phpunit/phpunit": "^10.4|^11.5", "laravel/pint": "^1.16"}, "suggest": {"ext-pcntl": "Recommended when running the Inertia SSR server via the `inertia:start-ssr` artisan command."}, "extra": {"laravel": {"providers": ["Inertia\\ServiceProvider"]}}, "minimum-stability": "dev", "prefer-stable": true}