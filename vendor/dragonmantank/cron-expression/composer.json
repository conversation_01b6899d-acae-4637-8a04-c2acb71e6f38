{"name": "dragonmantank/cron-expression", "type": "library", "description": "CRON for PHP: Calculate the next or previous run date and determine if a CRON expression is due", "keywords": ["cron", "schedule"], "license": "MIT", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/dragonmantank"}], "require": {"php": "^7.2|^8.0", "webmozart/assert": "^1.0"}, "require-dev": {"phpstan/phpstan": "^1.0", "phpunit/phpunit": "^7.0|^8.0|^9.0", "phpstan/extension-installer": "^1.0"}, "autoload": {"psr-4": {"Cron\\": "src/Cron/"}}, "autoload-dev": {"psr-4": {"Cron\\Tests\\": "tests/Cron/"}}, "replace": {"mtdowling/cron-expression": "^1.0"}, "scripts": {"phpstan": "./vendor/bin/phpstan analyze", "test": "phpunit"}, "extra": {"branch-alias": {"dev-master": "3.x-dev"}}, "config": {"allow-plugins": {"ocramius/package-versions": true, "phpstan/extension-installer": true}}}