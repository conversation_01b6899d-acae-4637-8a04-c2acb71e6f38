"use client"

import { useSearchParams } from "next/navigation"
import Link from "next/link"
import { CheckCircle, Calendar, CreditCard, Phone, Mail, ArrowRight, Download, Share2 } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"

export default function ConfirmationPage() {
  const searchParams = useSearchParams()
  const orderId = searchParams.get("orderId") || "GSJ" + Date.now()

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 py-12">
        {/* Success Header */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
            <CheckCircle className="h-8 w-8 text-green-600" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Pemesanan Berhasil Dibuat!</h1>
          <p className="text-lg text-gray-600">Terima kasih telah mempercayai GaunSyariJogja.com</p>
        </div>

        {/* Order Details */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Detail Pemesanan</span>
              <Badge variant="outline" className="text-lg px-3 py-1">
                {orderId}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-gray-900 mb-3">Informasi Gaun</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Nama Gaun:</span>
                    <span>Gaun Pengantin Syar'i Mewah</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Kategori:</span>
                    <span>Baju Pengantin</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Warna:</span>
                    <span>Putih Gading</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Durasi Sewa:</span>
                    <span>3 hari</span>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="font-semibold text-gray-900 mb-3">Tanggal Sewa</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    <span className="text-gray-600">Mulai:</span>
                    <span>Sabtu, 15 Maret 2024</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    <span className="text-gray-600">Selesai:</span>
                    <span>Senin, 17 Maret 2024</span>
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            <div>
              <h3 className="font-semibold text-gray-900 mb-3">Rincian Biaya</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Harga sewa (3 hari)</span>
                  <span>Rp 900.000</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">DP (Uang Muka)</span>
                  <span>Rp 150.000</span>
                </div>
                <Separator />
                <div className="flex justify-between font-semibold text-lg">
                  <span>Total Pembayaran</span>
                  <span className="text-rose-600">Rp 1.050.000</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Payment Instructions */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Instruksi Pembayaran
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
              <p className="text-yellow-800 font-medium mb-2">⏰ Batas Waktu Pembayaran: 24 jam dari sekarang</p>
              <p className="text-yellow-700 text-sm">
                Harap selesaikan pembayaran sebelum {new Date(Date.now() + 24 * 60 * 60 * 1000).toLocaleString("id-ID")}
              </p>
            </div>

            <div className="space-y-4">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Transfer ke Rekening:</h4>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-600">Bank</p>
                      <p className="font-medium">Bank Mandiri</p>
                    </div>
                    <div>
                      <p className="text-gray-600">No. Rekening</p>
                      <p className="font-medium">**********</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Atas Nama</p>
                      <p className="font-medium">GaunSyariJogja.com</p>
                    </div>
                    <div>
                      <p className="text-gray-600">Jumlah Transfer</p>
                      <p className="font-medium text-rose-600">Rp 1.050.000</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="text-sm text-gray-600 space-y-1">
                <p>• Pastikan jumlah transfer sesuai dengan total pembayaran</p>
                <p>• Simpan bukti transfer untuk konfirmasi</p>
                <p>• Setelah transfer, konfirmasi pembayaran melalui WhatsApp atau email</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Next Steps */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Langkah Selanjutnya</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-rose-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                  1
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Lakukan Pembayaran</h4>
                  <p className="text-sm text-gray-600">Transfer sesuai instruksi di atas dalam 24 jam</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-rose-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                  2
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Konfirmasi Pembayaran</h4>
                  <p className="text-sm text-gray-600">Kirim bukti transfer via WhatsApp atau email</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-rose-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                  3
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Tunggu Konfirmasi</h4>
                  <p className="text-sm text-gray-600">Tim kami akan mengkonfirmasi dalam 2-4 jam</p>
                </div>
              </div>

              <div className="flex items-start gap-3">
                <div className="w-6 h-6 bg-rose-600 text-white rounded-full flex items-center justify-center text-sm font-medium">
                  4
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Pengambilan/Pengiriman</h4>
                  <p className="text-sm text-gray-600">Gaun siap diambil atau dikirim sesuai jadwal</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Contact Information */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Butuh Bantuan?</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                  <Phone className="h-5 w-5 text-green-600" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">WhatsApp</p>
                  <p className="text-sm text-gray-600">+62 812-3456-7890</p>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                  <Mail className="h-5 w-5 text-blue-600" />
                </div>
                <div>
                  <p className="font-medium text-gray-900">Email</p>
                  <p className="text-sm text-gray-600"><EMAIL></p>
                </div>
              </div>
            </div>

            <p className="text-sm text-gray-600 mt-4">
              Tim customer service kami siap membantu Anda 24/7. Jangan ragu untuk menghubungi kami jika ada pertanyaan.
            </p>
          </CardContent>
        </Card>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button asChild className="bg-rose-600 hover:bg-rose-700">
            <Link href={`/payment-confirmation?orderId=${orderId}`} className="flex items-center gap-2">
              Konfirmasi Pembayaran
              <ArrowRight className="h-4 w-4" />
            </Link>
          </Button>

          <Button variant="outline" className="flex items-center gap-2 bg-transparent">
            <Download className="h-4 w-4" />
            Download Detail Pesanan
          </Button>

          <Button variant="outline" className="flex items-center gap-2 bg-transparent">
            <Share2 className="h-4 w-4" />
            Bagikan Pesanan
          </Button>

          <Button asChild variant="outline" className="bg-transparent">
            <Link href="/catalog" className="flex items-center gap-2">
              Lihat Koleksi Lainnya
              <ArrowRight className="h-4 w-4" />
            </Link>
          </Button>
        </div>

        {/* Email Notification */}
        <div className="text-center mt-8 p-4 bg-blue-50 rounded-lg">
          <p className="text-sm text-blue-800">
            📧 Detail pemesanan telah dikirim ke email Anda. Periksa folder inbox atau spam.
          </p>
        </div>
      </div>
    </div>
  )
}
