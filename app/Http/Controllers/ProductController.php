<?php

namespace App\Http\Controllers;

use App\Models\Product;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class ProductController extends Controller
{
    public function show(Product $product): Response
    {
        $product->load(['category', 'images']);

        $productData = [
            'id' => $product->id,
            'sku' => $product->sku,
            'slug' => $product->slug,
            'name' => $product->name,
            'description' => $product->description,
            'category' => $product->category->name,
            'price' => $product->price,
            'deposit_amount' => $product->getDepositAmountAttribute(),
            'deposit_percentage' => $product->deposit_percentage,
            'sizes' => $product->sizes,
            'colors' => $product->colors,
            'color' => $product->color,
            'motif' => $product->motif,
            'condition' => $product->condition,
            'status' => $product->status,
            'rating' => $product->rating,
            'rating_count' => $product->rating_count,
            'total_rentals' => $product->total_rentals,
            'last_rented' => $product->last_rented,
            'images' => $product->images->map(function ($image) {
                return [
                    'id' => $image->id,
                    'image_path' => $image->image_path,
                    'alt_text' => $image->alt_text,
                    'is_primary' => $image->is_primary,
                ];
            }),
            'is_available' => $product->isAvailable(),
        ];

        // Get related products from same category
        $relatedProducts = Product::with(['category', 'images'])
            ->where('category_id', $product->category_id)
            ->where('id', '!=', $product->id)
            ->available()
            ->take(4)
            ->get()
            ->map(function ($relatedProduct) {
                return [
                    'id' => $relatedProduct->id,
                    'sku' => $relatedProduct->sku,
                    'slug' => $relatedProduct->slug,
                    'name' => $relatedProduct->name,
                    'price' => $relatedProduct->price,
                    'image' => $relatedProduct->images->first()?->image_path ?? '/placeholder.jpg',
                    'rating' => $relatedProduct->rating,
                ];
            });

        return Inertia::render('ProductDetail', [
            'product' => $productData,
            'relatedProducts' => $relatedProducts,
        ]);
    }
}
