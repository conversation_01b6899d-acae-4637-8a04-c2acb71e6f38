<?php

namespace App\Http\Controllers;

use App\Models\Order;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class OrderController extends Controller
{
    public function track(Request $request): Response
    {
        $order = null;

        if ($request->filled('order_number')) {
            $order = Order::with(['customer', 'orderItems.product', 'payments'])
                ->where('order_number', $request->order_number)
                ->first();
        }

        return Inertia::render('OrderTracking', [
            'order' => $order ? [
                'id' => $order->id,
                'order_number' => $order->order_number,
                'status' => $order->status,
                'payment_status' => $order->payment_status,
                'rental_start_date' => $order->rental_start_date,
                'rental_end_date' => $order->rental_end_date,
                'rental_days' => $order->rental_days,
                'subtotal' => $order->subtotal,
                'deposit_amount' => $order->deposit_amount,
                'total_amount' => $order->total_amount,
                'delivery_method' => $order->delivery_method,
                'delivery_address' => $order->delivery_address,
                'notes' => $order->notes,
                'created_at' => $order->created_at,
                'customer' => [
                    'full_name' => $order->customer->full_name,
                    'email' => $order->customer->email,
                    'phone' => $order->customer->phone,
                ],
                'items' => $order->orderItems->map(function ($item) {
                    return [
                        'product_name' => $item->product_name,
                        'product_sku' => $item->product_sku,
                        'unit_price' => $item->unit_price,
                        'quantity' => $item->quantity,
                        'rental_days' => $item->rental_days,
                        'total_price' => $item->total_price,
                        'selected_size' => $item->selected_size,
                        'selected_color' => $item->selected_color,
                    ];
                }),
                'payments' => $order->payments->map(function ($payment) {
                    return [
                        'payment_reference' => $payment->payment_reference,
                        'payment_type' => $payment->payment_type,
                        'payment_method' => $payment->payment_method,
                        'amount' => $payment->amount,
                        'status' => $payment->status,
                        'paid_at' => $payment->paid_at,
                        'confirmed_at' => $payment->confirmed_at,
                    ];
                }),
            ] : null,
            'search_query' => $request->order_number,
        ]);
    }

    public function show(Order $order): Response
    {
        $order->load(['customer', 'orderItems.product', 'payments']);

        return Inertia::render('OrderDetail', [
            'order' => [
                'id' => $order->id,
                'order_number' => $order->order_number,
                'status' => $order->status,
                'payment_status' => $order->payment_status,
                'rental_start_date' => $order->rental_start_date,
                'rental_end_date' => $order->rental_end_date,
                'rental_days' => $order->rental_days,
                'subtotal' => $order->subtotal,
                'deposit_amount' => $order->deposit_amount,
                'total_amount' => $order->total_amount,
                'delivery_method' => $order->delivery_method,
                'delivery_address' => $order->delivery_address,
                'notes' => $order->notes,
                'created_at' => $order->created_at,
                'confirmed_at' => $order->confirmed_at,
                'shipped_at' => $order->shipped_at,
                'delivered_at' => $order->delivered_at,
                'completed_at' => $order->completed_at,
                'customer' => [
                    'full_name' => $order->customer->full_name,
                    'email' => $order->customer->email,
                    'phone' => $order->customer->phone,
                    'address' => $order->customer->address,
                ],
                'items' => $order->orderItems->map(function ($item) {
                    return [
                        'product_name' => $item->product_name,
                        'product_sku' => $item->product_sku,
                        'unit_price' => $item->unit_price,
                        'quantity' => $item->quantity,
                        'rental_days' => $item->rental_days,
                        'total_price' => $item->total_price,
                        'selected_size' => $item->selected_size,
                        'selected_color' => $item->selected_color,
                        'special_requests' => $item->special_requests,
                    ];
                }),
                'payments' => $order->payments->map(function ($payment) {
                    return [
                        'id' => $payment->id,
                        'payment_reference' => $payment->payment_reference,
                        'payment_type' => $payment->payment_type,
                        'payment_method' => $payment->payment_method,
                        'amount' => $payment->amount,
                        'status' => $payment->status,
                        'bank_name' => $payment->bank_name,
                        'account_name' => $payment->account_name,
                        'account_number' => $payment->account_number,
                        'proof_of_payment' => $payment->proof_of_payment,
                        'notes' => $payment->notes,
                        'paid_at' => $payment->paid_at,
                        'confirmed_at' => $payment->confirmed_at,
                    ];
                }),
            ],
        ]);
    }
}
