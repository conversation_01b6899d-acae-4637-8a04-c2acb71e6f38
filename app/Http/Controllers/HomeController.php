<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Category;
use Inertia\Inertia;
use Inertia\Response;

class HomeController extends Controller
{
    public function index(): Response
    {
        $featuredProducts = Product::with(['category', 'images'])
            ->featured()
            ->take(6)
            ->get()
            ->map(function ($product) {
                return [
                    'id' => $product->id,
                    'sku' => $product->sku,
                    'slug' => $product->slug,
                    'name' => $product->name,
                    'price' => $product->price,
                    'image' => $product->images->first()?->image_path ?? '/placeholder.jpg',
                    'category' => $product->category->name,
                    'rating' => $product->rating,
                    'status' => $product->status === 'available' ? 'Tersedia' : 'Disewa',
                ];
            });

        $categories = Category::where('is_active', true)
            ->withCount('activeProducts')
            ->get();

        return Inertia::render('Home', [
            'featuredProducts' => $featuredProducts,
            'categories' => $categories,
        ]);
    }
}
