<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Category;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class CatalogController extends Controller
{
    public function index(Request $request): Response
    {
        $query = Product::with(['category', 'images'])
            ->where('is_active', true);

        // Filter by category
        if ($request->filled('category')) {
            $query->whereHas('category', function ($q) use ($request) {
                $q->where('slug', $request->category);
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Search
        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%')
                  ->orWhere('color', 'like', '%' . $request->search . '%');
            });
        }

        // Sort
        $sortBy = $request->get('sort', 'name');
        $sortOrder = $request->get('order', 'asc');

        if ($sortBy === 'price') {
            $query->orderBy('price', $sortOrder);
        } elseif ($sortBy === 'rating') {
            $query->orderBy('rating', 'desc');
        } else {
            $query->orderBy('name', $sortOrder);
        }

        $products = $query->paginate(12)->through(function ($product) {
            return [
                'id' => $product->id,
                'sku' => $product->sku,
                'slug' => $product->slug,
                'name' => $product->name,
                'price' => $product->price,
                'image' => $product->images->first()?->image_path ?? '/placeholder.jpg',
                'category' => $product->category->name,
                'rating' => $product->rating,
                'status' => $product->status === 'available' ? 'Tersedia' : 'Disewa',
                'color' => $product->color,
                'motif' => $product->motif,
            ];
        });

        $categories = Category::where('is_active', true)
            ->withCount('activeProducts')
            ->get();

        return Inertia::render('Catalog', [
            'products' => $products,
            'categories' => $categories,
            'filters' => $request->only(['category', 'status', 'search', 'sort', 'order']),
        ]);
    }
}
