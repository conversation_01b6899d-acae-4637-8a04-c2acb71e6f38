<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Carbon\Carbon;

class ProductAvailabilityController extends Controller
{
    public function getAvailability(Product $product, Request $request): JsonResponse
    {
        $request->validate([
            'year' => 'required|integer|min:2025',
            'month' => 'required|integer|min:1|max:12',
        ]);

        $year = $request->year;
        $month = $request->month;

        // Get all orders for this product in the specified month
        $orders = Order::whereHas('orderItems', function ($query) use ($product) {
                $query->where('product_id', $product->id);
            })
            ->where('status', '!=', 'cancelled')
            ->where(function ($query) use ($year, $month) {
                $startOfMonth = Carbon::create($year, $month, 1)->startOfDay();
                $endOfMonth = Carbon::create($year, $month, 1)->endOfMonth()->endOfDay();

                $query->whereBetween('rental_start_date', [$startOfMonth, $endOfMonth])
                      ->orWhereBetween('rental_end_date', [$startOfMonth, $endOfMonth])
                      ->orWhere(function ($q) use ($startOfMonth, $endOfMonth) {
                          $q->where('rental_start_date', '<=', $startOfMonth)
                            ->where('rental_end_date', '>=', $endOfMonth);
                      });
            })
            ->get(['rental_start_date', 'rental_end_date']);

        // Generate unavailable dates
        $unavailableDates = [];

        foreach ($orders as $order) {
            $start = Carbon::parse($order->rental_start_date);
            $end = Carbon::parse($order->rental_end_date);

            // Add buffer days (1 day before and after for cleaning/preparation)
            $start->subDay();
            $end->addDay();

            while ($start->lte($end)) {
                $unavailableDates[] = $start->format('Y-m-d');
                $start->addDay();
            }
        }

        // Remove duplicates
        $unavailableDates = array_unique($unavailableDates);

        return response()->json([
            'unavailable_dates' => array_values($unavailableDates),
            'product_id' => $product->id,
            'year' => $year,
            'month' => $month,
        ]);
    }

    public function checkDateRange(Product $product, Request $request): JsonResponse
    {
        $request->validate([
            'start_date' => 'required|date|after_or_equal:today',
            'end_date' => 'required|date|after:start_date',
        ]);

        $startDate = Carbon::parse($request->start_date);
        $endDate = Carbon::parse($request->end_date);

        // Check if there are any conflicting orders
        $conflictingOrders = Order::whereHas('orderItems', function ($query) use ($product) {
                $query->where('product_id', $product->id);
            })
            ->where('status', '!=', 'cancelled')
            ->where(function ($query) use ($startDate, $endDate) {
                $query->whereBetween('rental_start_date', [$startDate, $endDate])
                      ->orWhereBetween('rental_end_date', [$startDate, $endDate])
                      ->orWhere(function ($q) use ($startDate, $endDate) {
                          $q->where('rental_start_date', '<=', $startDate)
                            ->where('rental_end_date', '>=', $endDate);
                      });
            })
            ->exists();

        $isAvailable = !$conflictingOrders;
        $rentalDays = $startDate->diffInDays($endDate) + 1;
        $totalPrice = $rentalDays * $product->price;

        return response()->json([
            'is_available' => $isAvailable,
            'rental_days' => $rentalDays,
            'total_price' => $totalPrice,
            'start_date' => $startDate->format('Y-m-d'),
            'end_date' => $endDate->format('Y-m-d'),
        ]);
    }
}
