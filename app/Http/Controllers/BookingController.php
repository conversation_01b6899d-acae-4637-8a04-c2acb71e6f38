<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Customer;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;
use Illuminate\Http\RedirectResponse;

class BookingController extends Controller
{
    public function show(Product $product): Response
    {
        $product->load(['category', 'images']);

        return Inertia::render('Booking', [
            'product' => [
                'id' => $product->id,
                'sku' => $product->sku,
                'name' => $product->name,
                'price' => $product->price,
                'deposit_amount' => $product->getDepositAmountAttribute(),
                'deposit_percentage' => $product->deposit_percentage,
                'image' => $product->images->first()?->image_path ?? '/placeholder.jpg',
                'category' => $product->category->name,
                'is_available' => $product->isAvailable(),
            ]
        ]);
    }

    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'product_id' => 'required|exists:products,id',
            'rental_start_date' => 'required|date|after_or_equal:today',
            'rental_end_date' => 'required|date|after:rental_start_date',
            'rental_days' => 'required|integer|min:1|max:7',
            'selected_size' => 'nullable|string',
            'selected_color' => 'nullable|string',
            'unit_price' => 'required|numeric|min:0',
            'total_price' => 'required|numeric|min:0',
            'deposit_amount' => 'required|numeric|min:0',
            'customer_name' => 'required|string|max:255',
            'customer_email' => 'required|email|max:255',
            'customer_phone' => 'required|string|max:20',
            'customer_address' => 'required|string',
            'delivery_method' => 'required|in:pickup,delivery',
            'delivery_address' => 'required_if:delivery_method,delivery|string',
            'notes' => 'nullable|string',
        ]);

        // Create or find customer
        $customer = Customer::firstOrCreate(
            ['email' => $validated['customer_email']],
            [
                'full_name' => $validated['customer_name'],
                'phone' => $validated['customer_phone'],
                'address' => $validated['customer_address'],
            ]
        );

        // Create order
        $order = Order::create([
            'order_number' => Order::generateOrderNumber(),
            'customer_id' => $customer->id,
            'rental_start_date' => $validated['rental_start_date'],
            'rental_end_date' => $validated['rental_end_date'],
            'rental_days' => $validated['rental_days'],
            'subtotal' => $validated['total_price'],
            'deposit_amount' => $validated['deposit_amount'],
            'total_amount' => $validated['total_price'] + $validated['deposit_amount'],
            'status' => 'pending_payment',
            'payment_status' => 'pending',
            'delivery_method' => $validated['delivery_method'],
            'delivery_address' => $validated['delivery_address'] ?? $validated['customer_address'],
            'notes' => $validated['notes'],
        ]);

        // Create order item
        OrderItem::create([
            'order_id' => $order->id,
            'product_id' => $validated['product_id'],
            'product_name' => Product::find($validated['product_id'])->name,
            'product_sku' => Product::find($validated['product_id'])->sku,
            'unit_price' => $validated['unit_price'],
            'quantity' => 1,
            'rental_days' => $validated['rental_days'],
            'total_price' => $validated['total_price'],
            'selected_size' => $validated['selected_size'],
            'selected_color' => $validated['selected_color'],
            'special_requests' => $validated['notes'],
        ]);

        return redirect()->route('orders.show', $order)
            ->with('success', 'Booking berhasil dibuat! Silakan lakukan pembayaran.');
    }
}
