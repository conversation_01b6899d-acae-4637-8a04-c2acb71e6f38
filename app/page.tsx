import Image from "next/image"
import Link from "next/link"
import { Star, ArrowRight, Heart, Users, Award, Clock } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

const featuredDresses = [
  {
    id: 1,
    name: "Gaun Pengantin Syar'i Mewah",
    price: "300.000",
    image: "/placeholder.svg?height=400&width=300",
    status: "Tersedia",
    rating: 4.9,
    category: "Baju Pengantin",
  },
  {
    id: 2,
    name: "Gaun Ibu Pengantin Elegant",
    price: "200.000",
    image: "/placeholder.svg?height=400&width=300",
    status: "Tersedia",
    rating: 4.8,
    category: "Baju Orang Tua (Ibu)",
  },
  {
    id: 3,
    name: "Baju Koko Ayah Premium",
    price: "175.000",
    image: "/placeholder.svg?height=400&width=300",
    status: "Disewa",
    rating: 4.7,
    category: "Baju Ayah",
  },
]

const testimonials = [
  {
    name: "<PERSON><PERSON>",
    text: "Gaun yang saya sewa sangat cantik dan berkualitas. Pelayanannya juga sangat memuaskan!",
    rating: 5,
    event: "Akad Nikah",
  },
  {
    name: "Fatimah Zahra",
    text: "Proses booking mudah dan gaun sesuai ekspektasi. Terima kasih GaunSyariJogja!",
    rating: 5,
    event: "Wisuda",
  },
  {
    name: "Khadijah Rahman",
    text: "Koleksi gaun syar'i terlengkap di Jogja. Pasti akan sewa lagi untuk acara berikutnya.",
    rating: 5,
    event: "Pesta",
  },
]

export default function HomePage() {
  return (
    <div className="min-h-screen bg-brand-bg">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-white to-brand-bg py-20 px-4">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div className="space-y-4">
                <h1 className="text-4xl lg:text-6xl font-bold text-brand-dark leading-tight">
                  Gaun Syar'i
                  <span className="text-brand-medium block">Terbaik di Jogja</span>
                </h1>
                <p className="text-lg text-gray-600 leading-relaxed">
                  Sewa gaun syar'i berkualitas premium untuk acara spesial Anda. Koleksi terlengkap dengan desain elegan
                  dan modern.
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button asChild size="lg" className="bg-brand-dark hover:bg-brand-medium text-white">
                  <Link href="/catalog">
                    Lihat Koleksi Terbaru
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
                </Button>
                <Button
                  asChild
                  variant="outline"
                  size="lg"
                  className="border-brand-dark text-brand-dark hover:bg-brand-dark hover:text-white bg-transparent"
                >
                  <Link href="#about">Tentang Kami</Link>
                </Button>
              </div>

              <div className="flex items-center gap-8 pt-4">
                <div className="flex items-center gap-2">
                  <Users className="h-5 w-5 text-brand-medium" />
                  <span className="text-sm text-gray-600">500+ Pelanggan Puas</span>
                </div>
                <div className="flex items-center gap-2">
                  <Award className="h-5 w-5 text-brand-medium" />
                  <span className="text-sm text-gray-600">Premium Quality</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="h-5 w-5 text-brand-medium" />
                  <span className="text-sm text-gray-600">Booking Mudah</span>
                </div>
              </div>
            </div>

            <div className="relative">
              <div className="relative z-10">
                <Image
                  src="/placeholder.svg?height=600&width=500"
                  alt="Model wearing elegant Islamic dress"
                  width={500}
                  height={600}
                  className="rounded-2xl shadow-2xl"
                />
              </div>
              <div className="absolute -top-4 -right-4 w-full h-full bg-brand-light rounded-2xl -z-10"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Dresses */}
      <section className="py-20 px-4 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-brand-dark mb-4">Koleksi Pilihan Terbaik</h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Gaun syar'i premium dengan desain terkini untuk berbagai acara spesial Anda
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredDresses.map((dress) => (
              <Card
                key={dress.id}
                className="group hover:shadow-xl transition-all duration-300 overflow-hidden border-brand-light"
              >
                <div className="relative">
                  <Image
                    src={dress.image || "/placeholder.svg"}
                    alt={dress.name}
                    width={300}
                    height={400}
                    className="w-full h-80 object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  <div className="absolute top-4 right-4">
                    <span
                      className={`px-3 py-1 rounded-full text-xs font-medium ${
                        dress.status === "Tersedia" ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"
                      }`}
                    >
                      {dress.status}
                    </span>
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    className="absolute top-4 left-4 bg-white/80 hover:bg-white text-brand-dark"
                  >
                    <Heart className="h-4 w-4" />
                  </Button>
                </div>

                <CardContent className="p-6">
                  <div className="space-y-3">
                    <h3 className="font-semibold text-lg text-brand-dark group-hover:text-brand-medium transition-colors">
                      {dress.name}
                    </h3>

                    <div className="flex items-center gap-2">
                      <div className="flex items-center">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`h-4 w-4 ${
                              i < Math.floor(dress.rating) ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
                            }`}
                          />
                        ))}
                      </div>
                      <span className="text-sm text-gray-600">({dress.rating})</span>
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <span className="text-2xl font-bold text-brand-medium">Rp {dress.price}</span>
                        <span className="text-sm text-gray-500 ml-1">/hari</span>
                      </div>
                    </div>

                    <Button asChild className="w-full bg-brand-dark hover:bg-brand-medium text-white">
                      <Link href={`/dress/${dress.id}`}>Lihat Detail</Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="text-center mt-12">
            <Button
              asChild
              variant="outline"
              size="lg"
              className="border-brand-dark text-brand-dark hover:bg-brand-dark hover:text-white bg-transparent"
            >
              <Link href="/catalog">
                Lihat Semua Koleksi
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-20 px-4 bg-brand-bg">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-brand-dark mb-4">Testimoni Pelanggan</h2>
            <p className="text-lg text-gray-600">Kepuasan pelanggan adalah prioritas utama kami</p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="bg-white border-brand-light">
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="flex items-center">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      ))}
                    </div>

                    <p className="text-gray-600 italic">"{testimonial.text}"</p>

                    <div className="border-t border-brand-light pt-4">
                      <p className="font-semibold text-brand-dark">{testimonial.name}</p>
                      <p className="text-sm text-gray-500">{testimonial.event}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="py-20 px-4 bg-white">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <h2 className="text-3xl lg:text-4xl font-bold text-brand-dark">Mengapa Memilih GaunSyariJogja.com?</h2>

              <div className="space-y-4">
                <div className="flex items-start gap-4">
                  <div className="bg-brand-light p-2 rounded-lg">
                    <Award className="h-6 w-6 text-brand-dark" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-brand-dark mb-2">Kualitas Premium</h3>
                    <p className="text-gray-600">
                      Semua gaun dibuat dengan bahan berkualitas tinggi dan jahitan yang rapi
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="bg-brand-light p-2 rounded-lg">
                    <Users className="h-6 w-6 text-brand-dark" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-brand-dark mb-2">Pelayanan Terbaik</h3>
                    <p className="text-gray-600">Tim customer service yang ramah dan responsif siap membantu Anda</p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="bg-brand-light p-2 rounded-lg">
                    <Clock className="h-6 w-6 text-brand-dark" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-brand-dark mb-2">Proses Mudah</h3>
                    <p className="text-gray-600">Booking online yang mudah dengan sistem pembayaran yang fleksibel</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="relative">
              <Image
                src="/placeholder.svg?height=500&width=600"
                alt="GaunSyariJogja Collection"
                width={600}
                height={500}
                className="rounded-2xl shadow-xl"
              />
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
