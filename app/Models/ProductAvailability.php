<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProductAvailability extends Model
{
    protected $fillable = [
        'product_id',
        'unavailable_date',
        'reason',
        'notes',
    ];

    protected $casts = [
        'unavailable_date' => 'date',
    ];

    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }
}
