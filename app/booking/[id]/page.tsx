"use client"

import type React from "react"

import { useState } from "react"
import { useParams, useSearchParams } from "next/navigation"
import Image from "next/image"
import Link from "next/link"
import { ArrowLeft, Calendar, CreditCard, MapPin, User, Phone, CheckCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Separator } from "@/components/ui/separator"

const dressData = {
  1: {
    id: 1,
    name: "Gaun Pengantin Syar'i Mewah",
    price: 300000,
    dp: 150000,
    image: "/placeholder.svg?height=300&width=200",
    color: "Putih Gading",
    category: "Baju Pengantin",
  },
  2: {
    id: 2,
    name: "Gaun I<PERSON> Pengantin Elegant",
    price: 200000,
    dp: 100000,
    image: "/placeholder.svg?height=300&width=200",
    color: "Navy Blue",
    category: "Baju Orang Tua (Ibu)",
  },
  3: {
    id: 3,
    name: "Baju Koko Ayah Premium",
    price: 175000,
    dp: 87500,
    image: "/placeholder.svg?height=300&width=200",
    color: "Hitam",
    category: "Baju Ayah",
  },
}

export default function BookingPage() {
  const params = useParams()
  const searchParams = useSearchParams()
  const dressId = Number.parseInt(params.id as string)
  const dress = dressData[dressId as keyof typeof dressData]

  const size = "" // Remove this line entirely
  const days = Number.parseInt(searchParams.get("days") || "1")
  const fromDate = searchParams.get("from") ? new Date(searchParams.get("from")!) : null
  const toDate = searchParams.get("to") ? new Date(searchParams.get("to")!) : null

  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    phone: "",
    address: "",
    notes: "",
  })
  const [agreedToTerms, setAgreedToTerms] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)

  if (!dress || !fromDate || !toDate) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Data booking tidak lengkap</h1>
          <Button asChild>
            <Link href="/catalog">Kembali ke Katalog</Link>
          </Button>
        </div>
      </div>
    )
  }

  const totalRental = dress.price * days
  const totalPayment = totalRental + dress.dp

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!agreedToTerms) return

    setIsSubmitting(true)

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 2000))

    // Redirect to confirmation page
    window.location.href = `/confirmation?orderId=GSJ${Date.now()}`
  }

  const isFormValid = formData.fullName && formData.email && formData.phone && formData.address && agreedToTerms

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Link href="/" className="hover:text-rose-600">
              Beranda
            </Link>
            <span>/</span>
            <Link href="/catalog" className="hover:text-rose-600">
              Katalog
            </Link>
            <span>/</span>
            <Link href={`/dress/${dress.id}`} className="hover:text-rose-600">
              {dress.name}
            </Link>
            <span>/</span>
            <span className="text-gray-900">Booking</span>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-8">
        <Button asChild variant="ghost" className="mb-6">
          <Link href={`/dress/${dress.id}`}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Kembali ke Detail Gaun
          </Link>
        </Button>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Booking Form */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Informasi Penyewa
                </CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="fullName">Nama Lengkap *</Label>
                      <Input
                        id="fullName"
                        value={formData.fullName}
                        onChange={(e) => handleInputChange("fullName", e.target.value)}
                        placeholder="Masukkan nama lengkap"
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="email">Email *</Label>
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => handleInputChange("email", e.target.value)}
                        placeholder="<EMAIL>"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="phone">Nomor Telepon *</Label>
                    <Input
                      id="phone"
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => handleInputChange("phone", e.target.value)}
                      placeholder="08xxxxxxxxxx"
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="address">Alamat Lengkap *</Label>
                    <Textarea
                      id="address"
                      value={formData.address}
                      onChange={(e) => handleInputChange("address", e.target.value)}
                      placeholder="Masukkan alamat lengkap untuk pengiriman/pengambilan"
                      rows={3}
                      required
                    />
                  </div>

                  <div>
                    <Label htmlFor="notes">Catatan Tambahan</Label>
                    <Textarea
                      id="notes"
                      value={formData.notes}
                      onChange={(e) => handleInputChange("notes", e.target.value)}
                      placeholder="Catatan khusus atau permintaan tambahan (opsional)"
                      rows={2}
                    />
                  </div>

                  <Separator />

                  {/* Payment Method */}
                  <div>
                    <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                      <CreditCard className="h-5 w-5" />
                      Metode Pembayaran
                    </h3>

                    <Card className="bg-blue-50 border-blue-200">
                      <CardContent className="p-4">
                        <div className="flex items-center gap-3 mb-3">
                          <div className="w-8 h-8 bg-blue-600 rounded flex items-center justify-center">
                            <CreditCard className="h-4 w-4 text-white" />
                          </div>
                          <h4 className="font-medium">Transfer Bank</h4>
                        </div>

                        <div className="space-y-2 text-sm">
                          <div className="grid grid-cols-2 gap-4">
                            <div>
                              <p className="text-gray-600">Bank</p>
                              <p className="font-medium">Bank Mandiri</p>
                            </div>
                            <div>
                              <p className="text-gray-600">No. Rekening</p>
                              <p className="font-medium">**********</p>
                            </div>
                          </div>
                          <div>
                            <p className="text-gray-600">Atas Nama</p>
                            <p className="font-medium">GaunSyariJogja.com</p>
                          </div>
                          <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded">
                            <p className="text-sm text-yellow-800">
                              <strong>Jumlah Transfer:</strong> Rp {totalPayment.toLocaleString()}
                            </p>
                            <p className="text-xs text-yellow-700 mt-1">
                              Harap selesaikan pembayaran dalam 1x24 jam setelah konfirmasi booking
                            </p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  <Separator />

                  {/* Terms and Conditions */}
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Syarat & Ketentuan</h3>

                    <Card className="max-h-48 overflow-y-auto">
                      <CardContent className="p-4 text-sm text-gray-600 space-y-2">
                        <p>
                          <strong>1. Pembayaran:</strong>
                        </p>
                        <ul className="list-disc list-inside ml-4 space-y-1">
                          <li>DP (Uang Muka) wajib dibayar untuk mengkonfirmasi booking</li>
                          <li>Sisa pembayaran dilunasi saat pengambilan gaun</li>
                          <li>Pembayaran melalui transfer bank sesuai rekening yang tertera</li>
                        </ul>

                        <p>
                          <strong>2. Penggunaan Gaun:</strong>
                        </p>
                        <ul className="list-disc list-inside ml-4 space-y-1">
                          <li>Gaun harus dikembalikan dalam kondisi bersih dan tidak rusak</li>
                          <li>Dilarang melakukan perubahan atau modifikasi pada gaun</li>
                          <li>Keterlambatan pengembalian dikenakan denda Rp 50.000/hari</li>
                        </ul>

                        <p>
                          <strong>3. Pembatalan:</strong>
                        </p>
                        <ul className="list-disc list-inside ml-4 space-y-1">
                          <li>Pembatalan H-7 atau lebih: DP dikembalikan 50%</li>
                          <li>Pembatalan H-3 sampai H-6: DP dikembalikan 25%</li>
                          <li>Pembatalan H-2 atau kurang: DP tidak dapat dikembalikan</li>
                        </ul>

                        <p>
                          <strong>4. Kerusakan:</strong>
                        </p>
                        <ul className="list-disc list-inside ml-4 space-y-1">
                          <li>Kerusakan ringan: biaya perbaikan sesuai estimasi</li>
                          <li>Kerusakan berat: ganti rugi maksimal 80% dari harga beli gaun</li>
                          <li>Kehilangan: ganti rugi 100% dari harga beli gaun</li>
                        </ul>
                      </CardContent>
                    </Card>

                    <div className="flex items-start space-x-2 mt-4">
                      <Checkbox id="terms" checked={agreedToTerms} onCheckedChange={setAgreedToTerms} />
                      <Label htmlFor="terms" className="text-sm leading-relaxed">
                        Saya telah membaca dan menyetujui semua syarat & ketentuan yang berlaku
                      </Label>
                    </div>
                  </div>

                  <Button
                    type="submit"
                    className="w-full bg-rose-600 hover:bg-rose-700 text-lg py-6"
                    disabled={!isFormValid || isSubmitting}
                  >
                    {isSubmitting ? "Memproses..." : "Konfirmasi Booking"}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <Card className="sticky top-4">
              <CardHeader>
                <CardTitle>Ringkasan Pesanan</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-4">
                  <div className="relative w-20 h-24 flex-shrink-0">
                    <Image
                      src={dress.image || "/placeholder.svg"}
                      alt={dress.name}
                      fill
                      className="object-cover rounded-lg"
                    />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900">{dress.name}</h3>
                    <p className="text-sm text-gray-600">Kategori: {dress.category}</p>
                    <p className="text-sm text-gray-600">Warna: {dress.color}</p>
                  </div>
                </div>

                <Separator />

                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    <span>Tanggal Sewa</span>
                  </div>
                  <div className="text-sm text-gray-600">
                    <p>
                      {fromDate.toLocaleDateString("id-ID", {
                        weekday: "long",
                        year: "numeric",
                        month: "long",
                        day: "numeric",
                      })}
                    </p>
                    <p className="text-xs">sampai</p>
                    <p>
                      {toDate.toLocaleDateString("id-ID", {
                        weekday: "long",
                        year: "numeric",
                        month: "long",
                        day: "numeric",
                      })}
                    </p>
                  </div>
                  <p className="text-sm font-medium">Durasi: {days} hari</p>
                </div>

                <Separator />

                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Harga sewa ({days} hari)</span>
                    <span>Rp {totalRental.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>DP (Uang Muka)</span>
                    <span>Rp {dress.dp.toLocaleString()}</span>
                  </div>
                  <Separator />
                  <div className="flex justify-between font-semibold text-lg">
                    <span>Total Pembayaran</span>
                    <span className="text-rose-600">Rp {totalPayment.toLocaleString()}</span>
                  </div>
                </div>

                <div className="bg-blue-50 p-3 rounded-lg">
                  <div className="flex items-start gap-2">
                    <CheckCircle className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                    <div className="text-xs text-blue-800">
                      <p className="font-medium mb-1">Yang perlu Anda bayar sekarang:</p>
                      <p>Rp {totalPayment.toLocaleString()} (Sewa + DP)</p>
                    </div>
                  </div>
                </div>

                <div className="space-y-2 text-xs text-gray-600">
                  <div className="flex items-start gap-2">
                    <MapPin className="h-3 w-3 mt-0.5 flex-shrink-0" />
                    <span>Gratis antar jemput dalam radius 10km dari pusat kota Yogyakarta</span>
                  </div>
                  <div className="flex items-start gap-2">
                    <Phone className="h-3 w-3 mt-0.5 flex-shrink-0" />
                    <span>Customer service siap membantu 24/7</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
