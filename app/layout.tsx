import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import Header from "@/components/layout/header"
import Footer from "@/components/layout/footer"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "GaunSyariJogja.com - Sewa Gaun Syar'i Terbaik di Yogyakarta",
  description:
    "Sewa gaun syar'i berkualitas premium untuk acara spesial Anda. Koleksi terlengkap dengan desain elegan dan modern di Yogyakarta.",
  keywords: "sewa gaun syari, gaun muslim, gaun akad, gaun pesta syari, yogyakarta",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="id">
      <body className={inter.className}>
        <Header />
        <main>{children}</main>
        <Footer />
      </body>
    </html>
  )
}
