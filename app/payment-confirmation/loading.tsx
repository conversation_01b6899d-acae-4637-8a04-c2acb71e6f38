import { Skeleton } from "@/components/ui/skeleton"
import { <PERSON>, Card<PERSON>ontent, CardHeader } from "@/components/ui/card"

export default function PaymentConfirmationLoading() {
  return (
    <div className="min-h-screen bg-brand-bg">
      <div className="max-w-4xl mx-auto px-4 py-8">
        <Skeleton className="h-10 w-48 mb-6" />

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Form */}
          <div className="lg:col-span-2">
            <Card className="border-brand-light">
              <CardHeader>
                <Skeleton className="h-6 w-48" />
                <Skeleton className="h-4 w-80" />
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  <div>
                    <Skeleton className="h-4 w-32 mb-2" />
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                      <Skeleton className="h-12 w-12 mx-auto mb-4" />
                      <Skeleton className="h-5 w-48 mx-auto mb-2" />
                      <Skeleton className="h-4 w-64 mx-auto mb-4" />
                      <Skeleton className="h-3 w-40 mx-auto" />
                    </div>
                  </div>

                  <div className="space-y-4">
                    <Skeleton className="h-5 w-32" />

                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <Skeleton className="h-4 w-24 mb-2" />
                        <Skeleton className="h-10 w-full" />
                      </div>
                      <div>
                        <Skeleton className="h-4 w-24 mb-2" />
                        <Skeleton className="h-10 w-full" />
                      </div>
                    </div>

                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <Skeleton className="h-4 w-28 mb-2" />
                        <Skeleton className="h-10 w-full" />
                      </div>
                      <div>
                        <Skeleton className="h-4 w-28 mb-2" />
                        <Skeleton className="h-10 w-full" />
                      </div>
                    </div>

                    <div>
                      <Skeleton className="h-4 w-40 mb-2" />
                      <Skeleton className="h-24 w-full" />
                    </div>
                  </div>

                  <Skeleton className="h-12 w-full" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <Card className="sticky top-4 border-brand-light">
              <CardHeader>
                <Skeleton className="h-6 w-32" />
                <Skeleton className="h-6 w-24" />
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Skeleton className="h-5 w-48 mb-2" />
                  <div className="space-y-1">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-4 w-40" />
                  </div>
                </div>

                <div className="border-t border-brand-light pt-4">
                  <Skeleton className="h-5 w-40 mb-2" />
                  <div className="space-y-1">
                    {[1, 2, 3].map((i) => (
                      <div key={i} className="flex justify-between">
                        <Skeleton className="h-4 w-24" />
                        <Skeleton className="h-4 w-20" />
                      </div>
                    ))}
                    <div className="border-t border-brand-light pt-1 flex justify-between">
                      <Skeleton className="h-5 w-20" />
                      <Skeleton className="h-5 w-24" />
                    </div>
                  </div>
                </div>

                <div className="border-t border-brand-light pt-4">
                  <Skeleton className="h-5 w-24 mb-2" />
                  <div className="space-y-1">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-4 w-28" />
                    <Skeleton className="h-4 w-36" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
