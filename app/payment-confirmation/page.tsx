"use client"

import type React from "react"

import { useState } from "react"
import { useSearchParams } from "next/navigation"
import Link from "next/link"
import { Upload, CheckCircle, AlertCircle, ArrowLeft, Camera, FileText, CreditCard } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"

export default function PaymentConfirmationPage() {
  const searchParams = useSearchParams()
  const orderId = searchParams.get("orderId") || "GSJ" + Date.now()

  const [uploadedFile, setUploadedFile] = useState<File | null>(null)
  const [transferDetails, setTransferDetails] = useState({
    bankName: "",
    accountName: "",
    transferAmount: "",
    transferDate: "",
    notes: "",
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      setUploadedFile(file)
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setTransferDetails((prev) => ({ ...prev, [field]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!uploadedFile) return

    setIsSubmitting(true)

    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 2000))

    setIsSubmitted(true)
    setIsSubmitting(false)
  }

  if (isSubmitted) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="max-w-md w-full mx-4">
          <CardContent className="text-center p-8">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-green-100 rounded-full mb-4">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Konfirmasi Terkirim!</h1>
            <p className="text-gray-600 mb-6">
              Bukti pembayaran Anda telah berhasil dikirim. Tim kami akan memverifikasi dalam 2-4 jam kerja.
            </p>
            <div className="space-y-3">
              <Button asChild className="w-full bg-rose-600 hover:bg-rose-700">
                <Link href={`/order-tracking?orderId=${orderId}`}>Lacak Status Pesanan</Link>
              </Button>
              <Button asChild variant="outline" className="w-full bg-transparent">
                <Link href="/catalog">Lihat Koleksi Lainnya</Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 py-8">
        <Button asChild variant="ghost" className="mb-6">
          <Link href={`/confirmation?orderId=${orderId}`}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Kembali ke Detail Pesanan
          </Link>
        </Button>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Main Form */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="h-5 w-5" />
                  Konfirmasi Pembayaran
                </CardTitle>
                <p className="text-sm text-gray-600">Upload bukti transfer dan lengkapi detail pembayaran Anda</p>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* File Upload */}
                  <div>
                    <Label htmlFor="payment-proof">Bukti Transfer *</Label>
                    <div className="mt-2">
                      {!uploadedFile ? (
                        <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-rose-400 transition-colors">
                          <input
                            id="payment-proof"
                            type="file"
                            accept="image/*,.pdf"
                            onChange={handleFileUpload}
                            className="hidden"
                            required
                          />
                          <label htmlFor="payment-proof" className="cursor-pointer">
                            <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                            <p className="text-lg font-medium text-gray-900 mb-2">Upload Bukti Transfer</p>
                            <p className="text-sm text-gray-600 mb-4">Drag & drop file atau klik untuk memilih</p>
                            <p className="text-xs text-gray-500">Format: JPG, PNG, PDF (Max 5MB)</p>
                          </label>
                        </div>
                      ) : (
                        <div className="border border-gray-300 rounded-lg p-4">
                          <div className="flex items-center gap-3">
                            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                              {uploadedFile.type.startsWith("image/") ? (
                                <Camera className="h-5 w-5 text-green-600" />
                              ) : (
                                <FileText className="h-5 w-5 text-green-600" />
                              )}
                            </div>
                            <div className="flex-1">
                              <p className="font-medium text-gray-900">{uploadedFile.name}</p>
                              <p className="text-sm text-gray-600">{(uploadedFile.size / 1024 / 1024).toFixed(2)} MB</p>
                            </div>
                            <Button type="button" variant="outline" size="sm" onClick={() => setUploadedFile(null)}>
                              Ganti
                            </Button>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Transfer Details */}
                  <div className="space-y-4">
                    <h3 className="font-semibold text-gray-900">Detail Transfer</h3>

                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="bankName">Bank Pengirim *</Label>
                        <Input
                          id="bankName"
                          value={transferDetails.bankName}
                          onChange={(e) => handleInputChange("bankName", e.target.value)}
                          placeholder="Contoh: Bank BCA"
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="accountName">Nama Pengirim *</Label>
                        <Input
                          id="accountName"
                          value={transferDetails.accountName}
                          onChange={(e) => handleInputChange("accountName", e.target.value)}
                          placeholder="Nama sesuai rekening"
                          required
                        />
                      </div>
                    </div>

                    <div className="grid md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="transferAmount">Jumlah Transfer *</Label>
                        <Input
                          id="transferAmount"
                          value={transferDetails.transferAmount}
                          onChange={(e) => handleInputChange("transferAmount", e.target.value)}
                          placeholder="525000"
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="transferDate">Tanggal Transfer *</Label>
                        <Input
                          id="transferDate"
                          type="datetime-local"
                          value={transferDetails.transferDate}
                          onChange={(e) => handleInputChange("transferDate", e.target.value)}
                          required
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="notes">Catatan Tambahan</Label>
                      <Textarea
                        id="notes"
                        value={transferDetails.notes}
                        onChange={(e) => handleInputChange("notes", e.target.value)}
                        placeholder="Catatan atau informasi tambahan (opsional)"
                        rows={3}
                      />
                    </div>
                  </div>

                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      Pastikan semua informasi yang Anda masukkan sudah benar. Tim kami akan memverifikasi pembayaran
                      dalam 2-4 jam kerja.
                    </AlertDescription>
                  </Alert>

                  <Button
                    type="submit"
                    className="w-full bg-rose-600 hover:bg-rose-700 text-lg py-6"
                    disabled={
                      !uploadedFile ||
                      !transferDetails.bankName ||
                      !transferDetails.accountName ||
                      !transferDetails.transferAmount ||
                      !transferDetails.transferDate ||
                      isSubmitting
                    }
                  >
                    {isSubmitting ? "Mengirim..." : "Kirim Konfirmasi Pembayaran"}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <Card className="sticky top-4">
              <CardHeader>
                <CardTitle>Detail Pesanan</CardTitle>
                <Badge variant="outline" className="w-fit">
                  {orderId}
                </Badge>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <h4 className="font-medium text-gray-900 mb-2">Gaun Pengantin Syar'i Mewah</h4>
                  <div className="text-sm text-gray-600 space-y-1">
                    <p>Kategori: Baju Pengantin</p>
                    <p>Durasi: 3 hari</p>
                    <p>Tanggal: 15-17 Maret 2024</p>
                  </div>
                </div>

                <div className="border-t pt-4">
                  <h4 className="font-medium text-gray-900 mb-2">Rincian Pembayaran</h4>
                  <div className="space-y-1 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Sewa (3 hari)</span>
                      <span>Rp 900.000</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">DP</span>
                      <span>Rp 150.000</span>
                    </div>
                    <div className="border-t pt-1 flex justify-between font-semibold">
                      <span>Total</span>
                      <span className="text-rose-600">Rp 1.050.000</span>
                    </div>
                  </div>
                </div>

                <div className="border-t pt-4">
                  <h4 className="font-medium text-gray-900 mb-2">Transfer ke:</h4>
                  <div className="text-sm space-y-1">
                    <p>
                      <strong>Bank Mandiri</strong>
                    </p>
                    <p>**********</p>
                    <p>GaunSyariJogja.com</p>
                  </div>
                </div>

                <Alert>
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription className="text-xs">
                    Pastikan jumlah transfer sesuai dengan total pembayaran: <strong>Rp 1.050.000</strong>
                  </AlertDescription>
                </Alert>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
