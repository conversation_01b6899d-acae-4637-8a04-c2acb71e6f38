"use client"

import { useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { Search, Filter, Star, Heart, Grid, List } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Slider } from "@/components/ui/slider"
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet"

const dresses = [
  {
    id: 1,
    name: "Gaun Pengantin Syar'i Mewah",
    price: 300000,
    image: "/placeholder.svg?height=400&width=300&text=Gaun+Pengantin+Syari+Mewah",
    status: "Tersedia",
    rating: 4.9,
    category: "Baju Pengantin",
    color: "Putih Gading",
    motif: "Bordir Emas",
  },
  {
    id: 2,
    name: "Gaun Ibu Pengantin Elegant",
    price: 200000,
    image: "/placeholder.svg?height=400&width=300&text=Gaun+Ibu+Pengantin+Elegant",
    status: "Tersedia",
    rating: 4.8,
    category: "Baju Orang Tua (Ibu)",
    color: "Navy Blue",
    motif: "Lace Premium",
  },
  {
    id: 3,
    name: "Baju Koko Ayah Premium",
    price: 175000,
    image: "/placeholder.svg?height=400&width=300&text=Baju+Koko+Ayah+Premium",
    status: "Disewa",
    rating: 4.7,
    category: "Baju Ayah",
    color: "Hitam",
    motif: "Polos Elegan",
  },
  {
    id: 4,
    name: "Gaun Akad Elegant Rose",
    price: 150000,
    image: "/placeholder.svg?height=400&width=300&text=Gaun+Akad+Elegant+Rose",
    status: "Tersedia",
    rating: 4.8,
    category: "Gaun Akad",
    color: "Rose Gold",
    motif: "Polos",
  },
  {
    id: 5,
    name: "Gaun Pesta Syar'i Emerald",
    price: 200000,
    image: "/placeholder.svg?height=400&width=300&text=Gaun+Pesta+Syari+Emerald",
    status: "Tersedia",
    rating: 4.9,
    category: "Gaun Pesta",
    color: "Emerald",
    motif: "Bordir",
  },
  {
    id: 6,
    name: "Gaun Wisuda Premium Gold",
    price: 175000,
    image: "/placeholder.svg?height=400&width=300&text=Gaun+Wisuda+Premium+Gold",
    status: "Tersedia",
    rating: 4.7,
    category: "Gaun Wisuda",
    color: "Gold",
    motif: "Sequin",
  },
  {
    id: 7,
    name: "Gaun Lamaran Dusty Pink",
    price: 180000,
    image: "/placeholder.svg?height=400&width=300&text=Gaun+Lamaran+Dusty+Pink",
    status: "Tersedia",
    rating: 4.6,
    category: "Gaun Lamaran",
    color: "Dusty Pink",
    motif: "Lace",
  },
  {
    id: 8,
    name: "Baju Koko Ayah Klasik",
    price: 150000,
    image: "/placeholder.svg?height=400&width=300&text=Baju+Koko+Ayah+Klasik",
    status: "Tersedia",
    rating: 4.5,
    category: "Baju Ayah",
    color: "Putih",
    motif: "Bordir Halus",
  },
]

const categories = [
  "Semua",
  "Baju Pengantin",
  "Baju Orang Tua (Ibu)",
  "Baju Ayah",
  "Gaun Akad",
  "Gaun Pesta",
  "Gaun Wisuda",
  "Gaun Lamaran",
]
const colors = ["Semua", "Rose Gold", "Emerald", "Gold", "Dusty Pink", "Navy", "Cream"]
const motifs = ["Semua", "Polos", "Bordir", "Sequin", "Lace", "Batik"]

export default function CatalogPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("Semua")
  const [selectedColors, setSelectedColors] = useState<string[]>([])
  const [selectedMotifs, setSelectedMotifs] = useState<string[]>([])
  const [priceRange, setPriceRange] = useState([0, 300000])
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [sortBy, setSortBy] = useState("name")

  const filteredDresses = dresses.filter((dress) => {
    const matchesSearch = dress.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = selectedCategory === "Semua" || dress.category === selectedCategory
    const matchesColor = selectedColors.length === 0 || selectedColors.includes(dress.color)
    const matchesMotif = selectedMotifs.length === 0 || selectedMotifs.includes(dress.motif)
    const matchesPrice = dress.price >= priceRange[0] && dress.price <= priceRange[1]

    return matchesSearch && matchesCategory && matchesColor && matchesMotif && matchesPrice
  })

  const sortedDresses = [...filteredDresses].sort((a, b) => {
    switch (sortBy) {
      case "price-low":
        return a.price - b.price
      case "price-high":
        return b.price - a.price
      case "rating":
        return b.rating - a.rating
      default:
        return a.name.localeCompare(b.name)
    }
  })

  const FilterContent = () => (
    <div className="space-y-6">
      <div>
        <h3 className="font-semibold mb-3 text-brand-dark">Kategori</h3>
        <div className="space-y-2">
          {categories.map((category) => (
            <div key={category} className="flex items-center space-x-2">
              <Checkbox
                id={category}
                checked={selectedCategory === category}
                onCheckedChange={() => setSelectedCategory(category)}
                className="border-brand-medium data-[state=checked]:bg-brand-dark data-[state=checked]:border-brand-dark"
              />
              <label htmlFor={category} className="text-sm text-brand-dark cursor-pointer">
                {category}
              </label>
            </div>
          ))}
        </div>
      </div>

      <div>
        <h3 className="font-semibold mb-3 text-brand-dark">Warna</h3>
        <div className="space-y-2">
          {colors.slice(1).map((color) => (
            <div key={color} className="flex items-center space-x-2">
              <Checkbox
                id={color}
                checked={selectedColors.includes(color)}
                onCheckedChange={(checked) => {
                  if (checked) {
                    setSelectedColors([...selectedColors, color])
                  } else {
                    setSelectedColors(selectedColors.filter((c) => c !== color))
                  }
                }}
                className="border-brand-medium data-[state=checked]:bg-brand-dark data-[state=checked]:border-brand-dark"
              />
              <label htmlFor={color} className="text-sm text-brand-dark cursor-pointer">
                {color}
              </label>
            </div>
          ))}
        </div>
      </div>

      <div>
        <h3 className="font-semibold mb-3 text-brand-dark">Motif</h3>
        <div className="space-y-2">
          {motifs.slice(1).map((motif) => (
            <div key={motif} className="flex items-center space-x-2">
              <Checkbox
                id={motif}
                checked={selectedMotifs.includes(motif)}
                onCheckedChange={(checked) => {
                  if (checked) {
                    setSelectedMotifs([...selectedMotifs, motif])
                  } else {
                    setSelectedMotifs(selectedMotifs.filter((m) => m !== motif))
                  }
                }}
                className="border-brand-medium data-[state=checked]:bg-brand-dark data-[state=checked]:border-brand-dark"
              />
              <label htmlFor={motif} className="text-sm text-brand-dark cursor-pointer">
                {motif}
              </label>
            </div>
          ))}
        </div>
      </div>

      <div>
        <h3 className="font-semibold mb-3 text-brand-dark">Rentang Harga</h3>
        <div className="space-y-4">
          <Slider
            value={priceRange}
            onValueChange={setPriceRange}
            max={300000}
            min={0}
            step={10000}
            className="w-full [&_[role=slider]]:bg-brand-dark [&_[role=slider]]:border-brand-dark [&_.bg-primary]:bg-brand-dark"
          />
          <div className="flex justify-between text-sm text-brand-medium">
            <span>Rp {priceRange[0].toLocaleString()}</span>
            <span>Rp {priceRange[1].toLocaleString()}</span>
          </div>
        </div>
      </div>

      <div className="space-y-2">
        <Button
          onClick={() => {
            setSelectedCategory("Semua")
            setSelectedColors([])
            setSelectedMotifs([])
            setPriceRange([0, 300000])
          }}
          variant="outline"
          className="w-full border-brand-medium text-brand-dark hover:bg-brand-light hover:text-brand-dark"
        >
          Reset Filter
        </Button>
      </div>
    </div>
  )

  return (
    <div className="min-h-screen bg-brand-bg">
      <div className="bg-white border-b border-brand-light">
        <div className="max-w-7xl mx-auto px-4 py-6">
          <h1 className="text-3xl font-bold text-brand-dark mb-6">Katalog Gaun Syar'i</h1>

          {/* Search and Controls */}
          <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-brand-medium h-4 w-4" />
              <Input
                placeholder="Cari gaun..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 border-brand-light focus:border-brand-medium focus:ring-brand-medium"
              />
            </div>

            <div className="flex items-center gap-4">
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-48 border-brand-light focus:border-brand-medium focus:ring-brand-medium">
                  <SelectValue placeholder="Urutkan berdasarkan" />
                </SelectTrigger>
                <SelectContent className="bg-white border-brand-light">
                  <SelectItem value="name" className="hover:bg-brand-light">
                    Nama A-Z
                  </SelectItem>
                  <SelectItem value="price-low" className="hover:bg-brand-light">
                    Harga Terendah
                  </SelectItem>
                  <SelectItem value="price-high" className="hover:bg-brand-light">
                    Harga Tertinggi
                  </SelectItem>
                  <SelectItem value="rating" className="hover:bg-brand-light">
                    Rating Tertinggi
                  </SelectItem>
                </SelectContent>
              </Select>

              <div className="flex items-center gap-2">
                <Button
                  variant={viewMode === "grid" ? "default" : "outline"}
                  size="icon"
                  onClick={() => setViewMode("grid")}
                  className={
                    viewMode === "grid"
                      ? "bg-brand-dark hover:bg-brand-medium text-white"
                      : "border-brand-medium text-brand-dark hover:bg-brand-light"
                  }
                >
                  <Grid className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === "list" ? "default" : "outline"}
                  size="icon"
                  onClick={() => setViewMode("list")}
                  className={
                    viewMode === "list"
                      ? "bg-brand-dark hover:bg-brand-medium text-white"
                      : "border-brand-medium text-brand-dark hover:bg-brand-light"
                  }
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>

              <Sheet>
                <SheetTrigger asChild>
                  <Button
                    variant="outline"
                    className="lg:hidden bg-transparent border-brand-medium text-brand-dark hover:bg-brand-light"
                  >
                    <Filter className="h-4 w-4 mr-2" />
                    Filter
                  </Button>
                </SheetTrigger>
                <SheetContent side="left" className="bg-white">
                  <SheetHeader>
                    <SheetTitle className="text-brand-dark">Filter Gaun</SheetTitle>
                  </SheetHeader>
                  <div className="mt-6">
                    <FilterContent />
                  </div>
                </SheetContent>
              </Sheet>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="flex gap-8">
          {/* Desktop Filters */}
          <div className="hidden lg:block w-64 flex-shrink-0">
            <div className="bg-white rounded-lg p-6 sticky top-4 border border-brand-light card-shadow">
              <h2 className="font-semibold text-lg mb-4 text-brand-dark">Filter</h2>
              <FilterContent />
            </div>
          </div>

          {/* Results */}
          <div className="flex-1">
            <div className="mb-6">
              <p className="text-brand-medium">
                Menampilkan {sortedDresses.length} dari {dresses.length} gaun
              </p>
            </div>

            {viewMode === "grid" ? (
              <div className="grid md:grid-cols-2 xl:grid-cols-3 gap-6">
                {sortedDresses.map((dress) => (
                  <Card
                    key={dress.id}
                    className="group hover:shadow-xl transition-all duration-300 overflow-hidden border-brand-light card-shadow hover:card-shadow-lg"
                  >
                    <div className="relative">
                      <Image
                        src={dress.image || "/placeholder.svg"}
                        alt={dress.name}
                        width={300}
                        height={400}
                        className="w-full h-80 object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                      <div className="absolute top-4 right-4">
                        <Badge
                          variant={dress.status === "Tersedia" ? "default" : "destructive"}
                          className={
                            dress.status === "Tersedia"
                              ? "bg-green-600 hover:bg-green-700 text-white"
                              : "bg-red-600 hover:bg-red-700 text-white"
                          }
                        >
                          {dress.status}
                        </Badge>
                      </div>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="absolute top-4 left-4 bg-white/80 hover:bg-white text-brand-dark hover:text-brand-medium"
                      >
                        <Heart className="h-4 w-4" />
                      </Button>
                    </div>

                    <CardContent className="p-6">
                      <div className="space-y-3">
                        <div>
                          <h3 className="font-semibold text-lg text-brand-dark group-hover:text-brand-medium transition-colors">
                            {dress.name}
                          </h3>
                          <p className="text-sm text-brand-medium">
                            {dress.category} • {dress.color}
                          </p>
                        </div>

                        <div className="flex items-center gap-2">
                          <div className="flex items-center">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className={`h-4 w-4 ${
                                  i < Math.floor(dress.rating) ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
                                }`}
                              />
                            ))}
                          </div>
                          <span className="text-sm text-brand-medium">({dress.rating})</span>
                        </div>

                        <div className="flex items-center justify-between">
                          <div>
                            <span className="text-2xl font-bold text-brand-dark">
                              Rp {dress.price.toLocaleString()}
                            </span>
                            <span className="text-sm text-brand-medium ml-1">/hari</span>
                          </div>
                        </div>

                        <Button asChild className="w-full bg-brand-dark hover:bg-brand-medium text-white">
                          <Link href={`/dress/${dress.id}`}>Lihat Detail</Link>
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : (
              <div className="space-y-4">
                {sortedDresses.map((dress) => (
                  <Card key={dress.id} className="overflow-hidden border-brand-light card-shadow">
                    <div className="flex">
                      <div className="relative w-48 h-48">
                        <Image src={dress.image || "/placeholder.svg"} alt={dress.name} fill className="object-cover" />
                        <div className="absolute top-2 right-2">
                          <Badge
                            variant={dress.status === "Tersedia" ? "default" : "destructive"}
                            className={
                              dress.status === "Tersedia"
                                ? "bg-green-600 hover:bg-green-700 text-white"
                                : "bg-red-600 hover:bg-red-700 text-white"
                            }
                          >
                            {dress.status}
                          </Badge>
                        </div>
                      </div>

                      <CardContent className="flex-1 p-6">
                        <div className="flex justify-between items-start">
                          <div className="space-y-2">
                            <h3 className="font-semibold text-xl text-brand-dark">{dress.name}</h3>
                            <p className="text-brand-medium">
                              {dress.category} • {dress.color} • {dress.motif}
                            </p>

                            <div className="flex items-center gap-2">
                              <div className="flex items-center">
                                {[...Array(5)].map((_, i) => (
                                  <Star
                                    key={i}
                                    className={`h-4 w-4 ${
                                      i < Math.floor(dress.rating) ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
                                    }`}
                                  />
                                ))}
                              </div>
                              <span className="text-sm text-brand-medium">({dress.rating})</span>
                            </div>
                          </div>

                          <div className="text-right space-y-4">
                            <div>
                              <span className="text-2xl font-bold text-brand-dark">
                                Rp {dress.price.toLocaleString()}
                              </span>
                              <span className="text-sm text-brand-medium block">/hari</span>
                            </div>

                            <Button asChild className="bg-brand-dark hover:bg-brand-medium text-white">
                              <Link href={`/dress/${dress.id}`}>Lihat Detail</Link>
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </div>
                  </Card>
                ))}
              </div>
            )}

            {sortedDresses.length === 0 && (
              <div className="text-center py-12">
                <p className="text-brand-medium text-lg">Tidak ada gaun yang sesuai dengan filter Anda</p>
                <Button
                  onClick={() => {
                    setSearchTerm("")
                    setSelectedCategory("Semua")
                    setSelectedColors([])
                    setSelectedMotifs([])
                    setPriceRange([0, 300000])
                  }}
                  variant="outline"
                  className="mt-4 border-brand-medium text-brand-dark hover:bg-brand-light"
                >
                  Reset Semua Filter
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
