import { Skeleton } from "@/components/ui/skeleton"
import { Card, CardContent } from "@/components/ui/card"

export default function CatalogLoading() {
  return (
    <div className="min-h-screen bg-brand-bg">
      <div className="bg-white border-b border-brand-light">
        <div className="max-w-7xl mx-auto px-4 py-6">
          <Skeleton className="h-8 w-64 mb-6" />

          {/* Search and Controls */}
          <div className="flex flex-col lg:flex-row gap-4 items-center justify-between">
            <Skeleton className="h-10 w-full max-w-md" />
            <div className="flex items-center gap-4">
              <Skeleton className="h-10 w-48" />
              <div className="flex items-center gap-2">
                <Skeleton className="h-10 w-10" />
                <Skeleton className="h-10 w-10" />
              </div>
              <Skeleton className="h-10 w-20" />
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-8">
        <div className="flex gap-8">
          {/* Desktop Filters */}
          <div className="hidden lg:block w-64 flex-shrink-0">
            <div className="bg-white rounded-lg p-6 sticky top-4 border border-brand-light">
              <Skeleton className="h-6 w-16 mb-4" />
              <div className="space-y-6">
                {[1, 2, 3, 4].map((i) => (
                  <div key={i}>
                    <Skeleton className="h-5 w-20 mb-3" />
                    <div className="space-y-2">
                      {[1, 2, 3].map((j) => (
                        <div key={j} className="flex items-center space-x-2">
                          <Skeleton className="h-4 w-4" />
                          <Skeleton className="h-4 w-24" />
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Results */}
          <div className="flex-1">
            <Skeleton className="h-5 w-48 mb-6" />

            <div className="grid md:grid-cols-2 xl:grid-cols-3 gap-6">
              {[1, 2, 3, 4, 5, 6].map((i) => (
                <Card key={i} className="overflow-hidden border-brand-light">
                  <Skeleton className="w-full h-80" />
                  <CardContent className="p-6">
                    <div className="space-y-3">
                      <Skeleton className="h-6 w-full" />
                      <Skeleton className="h-4 w-32" />
                      <div className="flex items-center gap-2">
                        <div className="flex items-center">
                          {[1, 2, 3, 4, 5].map((star) => (
                            <Skeleton key={star} className="h-4 w-4" />
                          ))}
                        </div>
                        <Skeleton className="h-4 w-12" />
                      </div>
                      <div className="flex items-center justify-between">
                        <Skeleton className="h-8 w-32" />
                      </div>
                      <Skeleton className="h-10 w-full" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
