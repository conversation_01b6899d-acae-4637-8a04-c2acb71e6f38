"use client"

import { useState } from "react"
import {
  CalendarIcon,
  Search,
  Plus,
  MoreHorizontal,
  Eye,
  Edit,
  AlertTriangle,
  CheckCircle,
  Clock,
  Wrench,
  Package,
  TrendingUp,
  Download,
  Grid,
  List,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { AdminSidebar } from "@/components/admin/admin-sidebar"
import { AdminHeader } from "@/components/admin/admin-header"
import { InventoryCalendar } from "@/components/admin/inventory-calendar"
import { GownDetailsModal } from "@/components/admin/gown-details-modal"

const inventoryData = [
  {
    id: "GSJ-001",
    name: "Gaun Pengantin Syar'i Mewah",
    category: "Baju Pengantin",
    size: "M",
    color: "Putih Gading",
    status: "available",
    condition: "excellent",
    lastRented: "2024-03-10",
    totalRentals: 15,
    image: "/placeholder.svg?height=80&width=80",
    notes: "Gaun premium dengan bordir emas",
    rentalHistory: [
      { date: "2024-03-10", customer: "Siti Aminah", duration: 3 },
      { date: "2024-02-20", customer: "Fatimah Zahra", duration: 2 },
    ],
    upcomingBookings: [{ date: "2024-03-25", customer: "Khadijah Rahman", duration: 3 }],
  },
  {
    id: "GSJ-002",
    name: "Gaun Ibu Pengantin Elegant",
    category: "Baju Orang Tua (Ibu)",
    size: "L",
    color: "Navy Blue",
    status: "rented",
    condition: "good",
    lastRented: "2024-03-15",
    totalRentals: 12,
    image: "/placeholder.svg?height=80&width=80",
    notes: "Detail renda premium",
    rentalHistory: [{ date: "2024-03-15", customer: "Ahmad Rahman", duration: 3 }],
    upcomingBookings: [],
  },
  {
    id: "GSJ-003",
    name: "Baju Koko Ayah Premium",
    category: "Baju Ayah",
    size: "XL",
    color: "Hitam",
    status: "maintenance",
    condition: "fair",
    lastRented: "2024-03-08",
    totalRentals: 8,
    image: "/placeholder.svg?height=80&width=80",
    notes: "Perlu perbaikan kancing",
    rentalHistory: [],
    upcomingBookings: [],
  },
  {
    id: "GSJ-004",
    name: "Gaun Akad Minimalis",
    category: "Baju Pengantin",
    size: "S",
    color: "Rose Gold",
    status: "cleaning",
    condition: "excellent",
    lastRented: "2024-03-12",
    totalRentals: 6,
    image: "/placeholder.svg?height=80&width=80",
    notes: "Sedang dalam proses dry cleaning",
    rentalHistory: [],
    upcomingBookings: [{ date: "2024-03-20", customer: "Laila Sari", duration: 2 }],
  },
  {
    id: "GSJ-005",
    name: "Gaun Wisuda Syar'i",
    category: "Baju Pengantin",
    size: "M",
    color: "Emerald",
    status: "damaged",
    condition: "poor",
    lastRented: "2024-03-05",
    totalRentals: 20,
    image: "/placeholder.svg?height=80&width=80",
    notes: "Sobek di bagian lengan, perlu perbaikan",
    rentalHistory: [],
    upcomingBookings: [],
  },
]

const getStatusBadge = (status: string) => {
  switch (status) {
    case "available":
      return (
        <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
          <CheckCircle className="h-3 w-3 mr-1" />
          Tersedia
        </Badge>
      )
    case "rented":
      return (
        <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">
          <Package className="h-3 w-3 mr-1" />
          Disewa
        </Badge>
      )
    case "cleaning":
      return (
        <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
          <Clock className="h-3 w-3 mr-1" />
          Dibersihkan
        </Badge>
      )
    case "maintenance":
      return (
        <Badge className="bg-orange-100 text-orange-800 hover:bg-orange-100">
          <Wrench className="h-3 w-3 mr-1" />
          Maintenance
        </Badge>
      )
    case "damaged":
      return (
        <Badge className="bg-red-100 text-red-800 hover:bg-red-100">
          <AlertTriangle className="h-3 w-3 mr-1" />
          Rusak
        </Badge>
      )
    default:
      return <Badge variant="secondary">{status}</Badge>
  }
}

const getConditionBadge = (condition: string) => {
  switch (condition) {
    case "excellent":
      return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Sangat Baik</Badge>
    case "good":
      return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">Baik</Badge>
    case "fair":
      return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">Cukup</Badge>
    case "poor":
      return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Buruk</Badge>
    default:
      return <Badge variant="secondary">{condition}</Badge>
  }
}

export default function InventoryPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [categoryFilter, setCategoryFilter] = useState("all")
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [selectedGown, setSelectedGown] = useState<(typeof inventoryData)[0] | null>(null)
  const [currentUser] = useState({
    name: "Admin User",
    role: "Super Admin",
    avatar: "/placeholder.svg?height=32&width=32",
  })

  const filteredInventory = inventoryData.filter((item) => {
    const matchesSearch =
      item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.color.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = statusFilter === "all" || item.status === statusFilter
    const matchesCategory = categoryFilter === "all" || item.category === categoryFilter
    return matchesSearch && matchesStatus && matchesCategory
  })

  const stats = [
    {
      title: "Total Gaun",
      value: inventoryData.length.toString(),
      icon: Package,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
    },
    {
      title: "Tersedia",
      value: inventoryData.filter((item) => item.status === "available").length.toString(),
      icon: CheckCircle,
      color: "text-green-600",
      bgColor: "bg-green-50",
    },
    {
      title: "Disewa",
      value: inventoryData.filter((item) => item.status === "rented").length.toString(),
      icon: Package,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
    },
    {
      title: "Perlu Perhatian",
      value: inventoryData
        .filter((item) => ["maintenance", "damaged", "cleaning"].includes(item.status))
        .length.toString(),
      icon: AlertTriangle,
      color: "text-red-600",
      bgColor: "bg-red-50",
    },
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      <AdminSidebar />

      <div className="lg:pl-64">
        <AdminHeader user={currentUser} />

        <main className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Manajemen Inventori</h1>
              <p className="text-gray-600">Kelola ketersediaan dan status gaun syar'i</p>
            </div>
            <div className="flex items-center gap-3">
              <Button variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Export Laporan
              </Button>
              <Button className="bg-rose-600 hover:bg-rose-700">
                <Plus className="h-4 w-4 mr-2" />
                Tambah Gaun
              </Button>
            </div>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            {stats.map((stat, index) => {
              const Icon = stat.icon
              return (
                <Card key={index}>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                        <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                      </div>
                      <div className={`w-12 h-12 rounded-lg ${stat.bgColor} flex items-center justify-center`}>
                        <Icon className={`h-6 w-6 ${stat.color}`} />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>

          {/* Main Content */}
          <Tabs defaultValue="overview" className="space-y-6">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="overview">Ringkasan Inventori</TabsTrigger>
              <TabsTrigger value="calendar">Kalender Ketersediaan</TabsTrigger>
              <TabsTrigger value="reports">Laporan & Analitik</TabsTrigger>
            </TabsList>

            <TabsContent value="overview">
              {/* Filters */}
              <Card className="mb-6">
                <CardContent className="p-6">
                  <div className="flex flex-col lg:flex-row gap-4 items-center">
                    <div className="flex-1">
                      <div className="relative">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                        <Input
                          placeholder="Cari gaun berdasarkan nama, ID, atau warna..."
                          value={searchTerm}
                          onChange={(e) => setSearchTerm(e.target.value)}
                          className="pl-10"
                        />
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Select value={statusFilter} onValueChange={setStatusFilter}>
                        <SelectTrigger className="w-48">
                          <SelectValue placeholder="Status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">Semua Status</SelectItem>
                          <SelectItem value="available">Tersedia</SelectItem>
                          <SelectItem value="rented">Disewa</SelectItem>
                          <SelectItem value="cleaning">Dibersihkan</SelectItem>
                          <SelectItem value="maintenance">Maintenance</SelectItem>
                          <SelectItem value="damaged">Rusak</SelectItem>
                        </SelectContent>
                      </Select>
                      <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                        <SelectTrigger className="w-48">
                          <SelectValue placeholder="Kategori" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">Semua Kategori</SelectItem>
                          <SelectItem value="Baju Pengantin">Baju Pengantin</SelectItem>
                          <SelectItem value="Baju Orang Tua (Ibu)">Baju Orang Tua (Ibu)</SelectItem>
                          <SelectItem value="Baju Ayah">Baju Ayah</SelectItem>
                        </SelectContent>
                      </Select>
                      <div className="flex items-center gap-1 border rounded-lg p-1">
                        <Button
                          variant={viewMode === "grid" ? "default" : "ghost"}
                          size="sm"
                          onClick={() => setViewMode("grid")}
                          className="h-8 w-8 p-0"
                        >
                          <Grid className="h-4 w-4" />
                        </Button>
                        <Button
                          variant={viewMode === "list" ? "default" : "ghost"}
                          size="sm"
                          onClick={() => setViewMode("list")}
                          className="h-8 w-8 p-0"
                        >
                          <List className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Inventory Grid/List */}
              <Card>
                <CardHeader>
                  <CardTitle>Daftar Inventori ({filteredInventory.length})</CardTitle>
                </CardHeader>
                <CardContent>
                  {viewMode === "grid" ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                      {filteredInventory.map((item) => (
                        <Card key={item.id} className="hover:shadow-md transition-shadow cursor-pointer">
                          <CardContent className="p-4">
                            <div className="space-y-4">
                              <div className="relative">
                                <img
                                  src={item.image || "/placeholder.svg"}
                                  alt={item.name}
                                  className="w-full h-32 object-cover rounded-lg"
                                />
                                <div className="absolute top-2 right-2">{getStatusBadge(item.status)}</div>
                              </div>
                              <div className="space-y-2">
                                <h3 className="font-semibold text-gray-900 truncate">{item.name}</h3>
                                <p className="text-sm text-gray-600">ID: {item.id}</p>
                                <div className="flex items-center justify-between">
                                  <span className="text-sm text-gray-600">
                                    {item.size} • {item.color}
                                  </span>
                                  {getConditionBadge(item.condition)}
                                </div>
                                <div className="flex items-center justify-between text-sm text-gray-500">
                                  <span>{item.totalRentals} sewa</span>
                                  <span>Terakhir: {item.lastRented}</span>
                                </div>
                              </div>
                              <div className="flex items-center gap-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="flex-1 bg-transparent"
                                  onClick={() => setSelectedGown(item)}
                                >
                                  <Eye className="h-3 w-3 mr-1" />
                                  Detail
                                </Button>
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button variant="outline" size="sm" className="px-2 bg-transparent">
                                      <MoreHorizontal className="h-4 w-4" />
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end">
                                    <DropdownMenuLabel>Aksi</DropdownMenuLabel>
                                    <DropdownMenuItem>
                                      <Edit className="mr-2 h-4 w-4" />
                                      Edit Status
                                    </DropdownMenuItem>
                                    <DropdownMenuItem>
                                      <CalendarIcon className="mr-2 h-4 w-4" />
                                      Lihat Kalender
                                    </DropdownMenuItem>
                                    <DropdownMenuSeparator />
                                    <DropdownMenuItem>
                                      <TrendingUp className="mr-2 h-4 w-4" />
                                      Lihat Statistik
                                    </DropdownMenuItem>
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {filteredInventory.map((item) => (
                        <Card key={item.id} className="hover:shadow-md transition-shadow">
                          <CardContent className="p-4">
                            <div className="flex items-center space-x-4">
                              <img
                                src={item.image || "/placeholder.svg"}
                                alt={item.name}
                                className="w-16 h-16 object-cover rounded-lg"
                              />
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center justify-between mb-2">
                                  <h3 className="font-semibold text-gray-900 truncate">{item.name}</h3>
                                  <div className="flex items-center gap-2">
                                    {getStatusBadge(item.status)}
                                    {getConditionBadge(item.condition)}
                                  </div>
                                </div>
                                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
                                  <div>
                                    <span className="font-medium">ID:</span> {item.id}
                                  </div>
                                  <div>
                                    <span className="font-medium">Ukuran:</span> {item.size}
                                  </div>
                                  <div>
                                    <span className="font-medium">Warna:</span> {item.color}
                                  </div>
                                  <div>
                                    <span className="font-medium">Total Sewa:</span> {item.totalRentals}
                                  </div>
                                </div>
                                {item.notes && <p className="text-sm text-gray-500 mt-2 italic">{item.notes}</p>}
                              </div>
                              <div className="flex items-center gap-2">
                                <Button variant="outline" size="sm" onClick={() => setSelectedGown(item)}>
                                  <Eye className="h-4 w-4 mr-1" />
                                  Detail
                                </Button>
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button variant="outline" size="sm" className="px-2 bg-transparent">
                                      <MoreHorizontal className="h-4 w-4" />
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end">
                                    <DropdownMenuLabel>Aksi</DropdownMenuLabel>
                                    <DropdownMenuItem>
                                      <Edit className="mr-2 h-4 w-4" />
                                      Edit Status
                                    </DropdownMenuItem>
                                    <DropdownMenuItem>
                                      <CalendarIcon className="mr-2 h-4 w-4" />
                                      Lihat Kalender
                                    </DropdownMenuItem>
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="calendar">
              <InventoryCalendar inventoryData={inventoryData} />
            </TabsContent>

            <TabsContent value="reports">
              <div className="grid lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Statistik Penyewaan</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <span className="text-sm font-medium">Rata-rata Penyewaan per Bulan</span>
                        <span className="text-lg font-bold text-rose-600">24</span>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <span className="text-sm font-medium">Gaun Paling Populer</span>
                        <span className="text-sm font-semibold">Gaun Pengantin Mewah</span>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <span className="text-sm font-medium">Tingkat Okupansi</span>
                        <span className="text-lg font-bold text-green-600">78%</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle>Status Maintenance</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                        <span className="text-sm font-medium">Perlu Maintenance</span>
                        <span className="text-lg font-bold text-yellow-600">2</span>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                        <span className="text-sm font-medium">Rusak</span>
                        <span className="text-lg font-bold text-red-600">1</span>
                      </div>
                      <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                        <span className="text-sm font-medium">Dalam Pembersihan</span>
                        <span className="text-lg font-bold text-blue-600">1</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </main>
      </div>

      {/* Gown Details Modal */}
      {selectedGown && (
        <GownDetailsModal gown={selectedGown} isOpen={!!selectedGown} onClose={() => setSelectedGown(null)} />
      )}
    </div>
  )
}
