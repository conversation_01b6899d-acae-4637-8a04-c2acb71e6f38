"use client"

import type React from "react"

import { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { ArrowLeft, Upload, X, Plus, Save } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { AdminSidebar } from "@/components/admin/admin-sidebar"
import { AdminHeader } from "@/components/admin/admin-header"

const categories = [
  { value: "baju-pengantin", label: "Baju Pengantin" },
  { value: "baju-orang-tua-ibu", label: "Baju Orang Tua (Ibu)" },
  { value: "baju-ayah", label: "Baju Ayah" },
  { value: "baju-wisuda", label: "Baju Wisuda" },
  { value: "aksesoris", label: "Aksesoris" },
  { value: "paket-lengkap", label: "Paket Lengkap" },
]

const subcategories = {
  "baju-pengantin": ["Gaun Akad", "Gaun Resepsi", "Gaun Syar'i", "Gaun Modern"],
  "baju-orang-tua-ibu": ["Gaun Pesta", "Gaun Formal", "Gaun Casual"],
  "baju-ayah": ["Koko Premium", "Koko Reguler", "Jas Formal"],
  "baju-wisuda": ["Gaun Wisuda", "Toga Set"],
  aksesoris: ["Hijab", "Selendang", "Perhiasan", "Sepatu"],
  "paket-lengkap": ["Paket Pengantin", "Paket Keluarga", "Paket Wisuda"],
}

const availableSizes = ["XS", "S", "M", "L", "XL", "XXL", "XXXL"]
const availableColors = [
  { name: "Putih", hex: "#FFFFFF" },
  { name: "Krem", hex: "#F5F5DC" },
  { name: "Champagne", hex: "#F7E7CE" },
  { name: "Hitam", hex: "#000000" },
  { name: "Navy", hex: "#000080" },
  { name: "Maroon", hex: "#800000" },
  { name: "Hijau Tua", hex: "#006400" },
  { name: "Abu-abu", hex: "#808080" },
  { name: "Coklat", hex: "#8B4513" },
  { name: "Ungu", hex: "#800080" },
]

export default function AddProductPage() {
  const router = useRouter()
  const [currentUser] = useState({
    name: "Admin User",
    role: "Super Admin",
    avatar: "/placeholder.svg?height=32&width=32",
  })

  const [formData, setFormData] = useState({
    name: "",
    description: "",
    category: "",
    subcategory: "",
    price: "",
    originalPrice: "",
    deposit: "",
    minRentalDays: "1",
    maxRentalDays: "7",
    status: "active",
    isPopular: false,
    isNew: true,
    features: [""],
    specifications: {
      material: "",
      care: "",
      weight: "",
      dimensions: "",
    },
  })

  const [selectedSizes, setSelectedSizes] = useState<string[]>([])
  const [selectedColors, setSelectedColors] = useState<string[]>([])
  const [images, setImages] = useState<File[]>([])
  const [imagePreviews, setImagePreviews] = useState<string[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  const handleInputChange = (field: string, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }))
    }
  }

  const handleSpecificationChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      specifications: {
        ...prev.specifications,
        [field]: value,
      },
    }))
  }

  const handleFeatureChange = (index: number, value: string) => {
    const newFeatures = [...formData.features]
    newFeatures[index] = value
    setFormData((prev) => ({ ...prev, features: newFeatures }))
  }

  const addFeature = () => {
    setFormData((prev) => ({ ...prev, features: [...prev.features, ""] }))
  }

  const removeFeature = (index: number) => {
    const newFeatures = formData.features.filter((_, i) => i !== index)
    setFormData((prev) => ({ ...prev, features: newFeatures }))
  }

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    if (files.length + images.length > 5) {
      alert("Maksimal 5 gambar")
      return
    }

    setImages((prev) => [...prev, ...files])

    // Create previews
    files.forEach((file) => {
      const reader = new FileReader()
      reader.onload = (e) => {
        setImagePreviews((prev) => [...prev, e.target?.result as string])
      }
      reader.readAsDataURL(file)
    })
  }

  const removeImage = (index: number) => {
    setImages((prev) => prev.filter((_, i) => i !== index))
    setImagePreviews((prev) => prev.filter((_, i) => i !== index))
  }

  const toggleSize = (size: string) => {
    setSelectedSizes((prev) => (prev.includes(size) ? prev.filter((s) => s !== size) : [...prev, size]))
  }

  const toggleColor = (color: string) => {
    setSelectedColors((prev) => (prev.includes(color) ? prev.filter((c) => c !== color) : [...prev, color]))
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) newErrors.name = "Nama produk wajib diisi"
    if (!formData.description.trim()) newErrors.description = "Deskripsi wajib diisi"
    if (!formData.category) newErrors.category = "Kategori wajib dipilih"
    if (!formData.price) newErrors.price = "Harga wajib diisi"
    if (selectedSizes.length === 0) newErrors.sizes = "Minimal pilih 1 ukuran"
    if (selectedColors.length === 0) newErrors.colors = "Minimal pilih 1 warna"
    if (images.length === 0) newErrors.images = "Minimal upload 1 gambar"

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (isDraft = false) => {
    if (!isDraft && !validateForm()) return

    setIsSubmitting(true)

    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 2000))

      const productData = {
        ...formData,
        sizes: selectedSizes,
        colors: selectedColors,
        images: images,
        status: isDraft ? "draft" : formData.status,
        createdAt: new Date().toISOString(),
        id: `GSJ-${Date.now()}`,
      }

      console.log("Product data:", productData)

      // Redirect to products page
      router.push("/admin/products")
    } catch (error) {
      console.error("Error saving product:", error)
    } finally {
      setIsSubmitting(false)
    }
  }

  const calculateDeposit = () => {
    const price = Number(formData.price)
    if (price > 0) {
      return Math.round(price * 0.5) // 50% deposit
    }
    return 0
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <AdminSidebar />

      <div className="lg:pl-64">
        <AdminHeader user={currentUser} />

        <main className="p-6">
          {/* Header */}
          <div className="flex items-center gap-4 mb-6">
            <Button variant="outline" onClick={() => router.back()}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Kembali
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Tambah Produk Baru</h1>
              <p className="text-gray-600">Tambahkan produk gaun syar'i baru ke katalog</p>
            </div>
          </div>

          <div className="grid lg:grid-cols-3 gap-6">
            {/* Main Form */}
            <div className="lg:col-span-2 space-y-6">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle>Informasi Dasar</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="name">Nama Produk *</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => handleInputChange("name", e.target.value)}
                      placeholder="Contoh: Gaun Pengantin Syar'i Mewah"
                      className={errors.name ? "border-red-500" : ""}
                    />
                    {errors.name && <p className="text-sm text-red-500 mt-1">{errors.name}</p>}
                  </div>

                  <div>
                    <Label htmlFor="description">Deskripsi *</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => handleInputChange("description", e.target.value)}
                      placeholder="Deskripsikan produk secara detail..."
                      rows={4}
                      className={errors.description ? "border-red-500" : ""}
                    />
                    {errors.description && <p className="text-sm text-red-500 mt-1">{errors.description}</p>}
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="category">Kategori *</Label>
                      <Select
                        value={formData.category}
                        onValueChange={(value) => {
                          handleInputChange("category", value)
                          handleInputChange("subcategory", "") // Reset subcategory
                        }}
                      >
                        <SelectTrigger className={errors.category ? "border-red-500" : ""}>
                          <SelectValue placeholder="Pilih kategori" />
                        </SelectTrigger>
                        <SelectContent>
                          {categories.map((cat) => (
                            <SelectItem key={cat.value} value={cat.value}>
                              {cat.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {errors.category && <p className="text-sm text-red-500 mt-1">{errors.category}</p>}
                    </div>

                    <div>
                      <Label htmlFor="subcategory">Sub Kategori</Label>
                      <Select
                        value={formData.subcategory}
                        onValueChange={(value) => handleInputChange("subcategory", value)}
                        disabled={!formData.category}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Pilih sub kategori" />
                        </SelectTrigger>
                        <SelectContent>
                          {formData.category &&
                            subcategories[formData.category as keyof typeof subcategories]?.map((sub) => (
                              <SelectItem key={sub} value={sub}>
                                {sub}
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Pricing */}
              <Card>
                <CardHeader>
                  <CardTitle>Harga & Pembayaran</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <Label htmlFor="price">Harga Sewa/Hari *</Label>
                      <Input
                        id="price"
                        type="number"
                        value={formData.price}
                        onChange={(e) => {
                          handleInputChange("price", e.target.value)
                          // Auto calculate deposit
                          if (!formData.deposit) {
                            handleInputChange("deposit", calculateDeposit().toString())
                          }
                        }}
                        placeholder="0"
                        className={errors.price ? "border-red-500" : ""}
                      />
                      {errors.price && <p className="text-sm text-red-500 mt-1">{errors.price}</p>}
                    </div>

                    <div>
                      <Label htmlFor="originalPrice">Harga Asli (Opsional)</Label>
                      <Input
                        id="originalPrice"
                        type="number"
                        value={formData.originalPrice}
                        onChange={(e) => handleInputChange("originalPrice", e.target.value)}
                        placeholder="0"
                      />
                      <p className="text-xs text-gray-500 mt-1">Untuk menampilkan diskon</p>
                    </div>

                    <div>
                      <Label htmlFor="deposit">DP (Down Payment)</Label>
                      <Input
                        id="deposit"
                        type="number"
                        value={formData.deposit}
                        onChange={(e) => handleInputChange("deposit", e.target.value)}
                        placeholder="0"
                      />
                      <p className="text-xs text-gray-500 mt-1">Saran: {calculateDeposit().toLocaleString()}</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="minRentalDays">Min. Hari Sewa</Label>
                      <Input
                        id="minRentalDays"
                        type="number"
                        min="1"
                        value={formData.minRentalDays}
                        onChange={(e) => handleInputChange("minRentalDays", e.target.value)}
                      />
                    </div>

                    <div>
                      <Label htmlFor="maxRentalDays">Max. Hari Sewa</Label>
                      <Input
                        id="maxRentalDays"
                        type="number"
                        min="1"
                        value={formData.maxRentalDays}
                        onChange={(e) => handleInputChange("maxRentalDays", e.target.value)}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Variants */}
              <Card>
                <CardHeader>
                  <CardTitle>Varian Produk</CardTitle>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div>
                    <Label>Ukuran Tersedia *</Label>
                    <div className="flex flex-wrap gap-2 mt-2">
                      {availableSizes.map((size) => (
                        <Button
                          key={size}
                          type="button"
                          variant={selectedSizes.includes(size) ? "default" : "outline"}
                          size="sm"
                          onClick={() => toggleSize(size)}
                          className={selectedSizes.includes(size) ? "bg-rose-600 hover:bg-rose-700" : ""}
                        >
                          {size}
                        </Button>
                      ))}
                    </div>
                    {errors.sizes && <p className="text-sm text-red-500 mt-1">{errors.sizes}</p>}
                  </div>

                  <div>
                    <Label>Warna Tersedia *</Label>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-2">
                      {availableColors.map((color) => (
                        <Button
                          key={color.name}
                          type="button"
                          variant={selectedColors.includes(color.name) ? "default" : "outline"}
                          size="sm"
                          onClick={() => toggleColor(color.name)}
                          className={`justify-start ${selectedColors.includes(color.name) ? "bg-rose-600 hover:bg-rose-700" : ""}`}
                        >
                          <div className="w-4 h-4 rounded-full border mr-2" style={{ backgroundColor: color.hex }} />
                          {color.name}
                        </Button>
                      ))}
                    </div>
                    {errors.colors && <p className="text-sm text-red-500 mt-1">{errors.colors}</p>}
                  </div>
                </CardContent>
              </Card>

              {/* Features */}
              <Card>
                <CardHeader>
                  <CardTitle>Fitur Produk</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {formData.features.map((feature, index) => (
                    <div key={index} className="flex gap-2">
                      <Input
                        value={feature}
                        onChange={(e) => handleFeatureChange(index, e.target.value)}
                        placeholder="Contoh: Bahan Premium"
                      />
                      {formData.features.length > 1 && (
                        <Button type="button" variant="outline" size="icon" onClick={() => removeFeature(index)}>
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  ))}
                  <Button type="button" variant="outline" onClick={addFeature} className="w-full bg-transparent">
                    <Plus className="h-4 w-4 mr-2" />
                    Tambah Fitur
                  </Button>
                </CardContent>
              </Card>

              {/* Specifications */}
              <Card>
                <CardHeader>
                  <CardTitle>Spesifikasi</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="material">Bahan</Label>
                      <Input
                        id="material"
                        value={formData.specifications.material}
                        onChange={(e) => handleSpecificationChange("material", e.target.value)}
                        placeholder="Contoh: Satin Premium"
                      />
                    </div>

                    <div>
                      <Label htmlFor="weight">Berat (gram)</Label>
                      <Input
                        id="weight"
                        value={formData.specifications.weight}
                        onChange={(e) => handleSpecificationChange("weight", e.target.value)}
                        placeholder="Contoh: 800"
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="care">Cara Perawatan</Label>
                    <Textarea
                      id="care"
                      value={formData.specifications.care}
                      onChange={(e) => handleSpecificationChange("care", e.target.value)}
                      placeholder="Contoh: Dry clean only, hindari sinar matahari langsung"
                      rows={3}
                    />
                  </div>

                  <div>
                    <Label htmlFor="dimensions">Dimensi</Label>
                    <Input
                      id="dimensions"
                      value={formData.specifications.dimensions}
                      onChange={(e) => handleSpecificationChange("dimensions", e.target.value)}
                      placeholder="Contoh: Panjang 150cm, Lebar 60cm"
                    />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Images */}
              <Card>
                <CardHeader>
                  <CardTitle>Gambar Produk *</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                    <input
                      type="file"
                      multiple
                      accept="image/*"
                      onChange={handleImageUpload}
                      className="hidden"
                      id="image-upload"
                    />
                    <label htmlFor="image-upload" className="cursor-pointer">
                      <Upload className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                      <p className="text-sm text-gray-600">Klik untuk upload gambar</p>
                      <p className="text-xs text-gray-500 mt-1">Maksimal 5 gambar, format JPG/PNG</p>
                    </label>
                  </div>

                  {imagePreviews.length > 0 && (
                    <div className="grid grid-cols-2 gap-2">
                      {imagePreviews.map((preview, index) => (
                        <div key={index} className="relative">
                          <img
                            src={preview || "/placeholder.svg"}
                            alt={`Preview ${index + 1}`}
                            className="w-full h-24 object-cover rounded-lg"
                          />
                          <Button
                            type="button"
                            variant="destructive"
                            size="icon"
                            className="absolute -top-2 -right-2 h-6 w-6"
                            onClick={() => removeImage(index)}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                          {index === 0 && <Badge className="absolute bottom-1 left-1 text-xs">Utama</Badge>}
                        </div>
                      ))}
                    </div>
                  )}
                  {errors.images && <p className="text-sm text-red-500">{errors.images}</p>}
                </CardContent>
              </Card>

              {/* Status & Settings */}
              <Card>
                <CardHeader>
                  <CardTitle>Status & Pengaturan</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="status">Status Produk</Label>
                    <Select value={formData.status} onValueChange={(value) => handleInputChange("status", value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="active">Aktif</SelectItem>
                        <SelectItem value="inactive">Tidak Aktif</SelectItem>
                        <SelectItem value="draft">Draft</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <Separator />

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="isPopular">Produk Populer</Label>
                      <p className="text-xs text-gray-500">Tampilkan di bagian populer</p>
                    </div>
                    <Switch
                      id="isPopular"
                      checked={formData.isPopular}
                      onCheckedChange={(checked) => handleInputChange("isPopular", checked)}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label htmlFor="isNew">Produk Baru</Label>
                      <p className="text-xs text-gray-500">Tampilkan badge "Baru"</p>
                    </div>
                    <Switch
                      id="isNew"
                      checked={formData.isNew}
                      onCheckedChange={(checked) => handleInputChange("isNew", checked)}
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Preview */}
              <Card>
                <CardHeader>
                  <CardTitle>Preview</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="border rounded-lg p-4 bg-white">
                    {imagePreviews[0] && (
                      <img
                        src={imagePreviews[0] || "/placeholder.svg"}
                        alt="Preview"
                        className="w-full h-32 object-cover rounded-lg mb-3"
                      />
                    )}
                    <h4 className="font-medium mb-1">{formData.name || "Nama Produk"}</h4>
                    <p className="text-sm text-gray-600 mb-2">
                      {categories.find((c) => c.value === formData.category)?.label || "Kategori"}
                    </p>
                    <div className="flex items-center justify-between">
                      <span className="font-bold text-rose-600">Rp {Number(formData.price || 0).toLocaleString()}</span>
                      <div className="flex gap-1">
                        {formData.isNew && <Badge className="bg-green-600 text-xs">Baru</Badge>}
                        {formData.isPopular && <Badge className="bg-orange-600 text-xs">Populer</Badge>}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Action Buttons */}
              <div className="space-y-3">
                <Button
                  onClick={() => handleSubmit(false)}
                  disabled={isSubmitting}
                  className="w-full bg-rose-600 hover:bg-rose-700"
                >
                  {isSubmitting ? (
                    "Menyimpan..."
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Simpan & Publikasikan
                    </>
                  )}
                </Button>

                <Button variant="outline" onClick={() => handleSubmit(true)} disabled={isSubmitting} className="w-full">
                  Simpan sebagai Draft
                </Button>

                <Button variant="outline" onClick={() => router.back()} className="w-full">
                  Batal
                </Button>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}
