"use client"

import { useState } from "react"
import Link from "next/link"
import { Plus, Search, MoreHorizontal, Edit, Trash2, Eye, Package, AlertCircle, CheckCircle, Clock } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { AdminSidebar } from "@/components/admin/admin-sidebar"
import { AdminHeader } from "@/components/admin/admin-header"

const products = [
  {
    id: "1",
    name: "Gaun <PERSON>gantin <PERSON>yar'i Mewah",
    category: "Baju Pengantin",
    price: 300000,
    deposit: 150000,
    status: "active",
    stock: 3,
    orders: 45,
    image: "/placeholder.svg?height=60&width=60",
    createdAt: "2024-01-15",
    description:
      "Gaun pengantin syar'i dengan bordir emas mewah. Tersedia dalam ukuran S (Lingkar Dada: 86-90cm), M (Lingkar Dada: 90-94cm), L (Lingkar Dada: 94-98cm), XL (Lingkar Dada: 98-102cm).",
  },
  {
    id: "2",
    name: "Gaun Ibu Pengantin Elegant",
    category: "Baju Orang Tua (Ibu)",
    price: 200000,
    deposit: 100000,
    status: "active",
    stock: 5,
    orders: 32,
    image: "/placeholder.svg?height=60&width=60",
    createdAt: "2024-01-10",
    description:
      "Gaun elegant untuk ibu pengantin dengan detail renda premium. Tersedia dalam ukuran S hingga XXL dengan lingkar dada 86-110cm.",
  },
  {
    id: "3",
    name: "Baju Koko Ayah Premium",
    category: "Baju Ayah",
    price: 175000,
    deposit: 87500,
    status: "active",
    stock: 2,
    orders: 28,
    image: "/placeholder.svg?height=60&width=60",
    createdAt: "2024-01-08",
    description:
      "Baju koko premium untuk ayah dengan bahan katun berkualitas tinggi. Tersedia dalam ukuran M (Lingkar Dada: 96-100cm), L (100-104cm), XL (104-108cm), XXL (108-112cm).",
  },
  {
    id: "4",
    name: "Gaun Akad Minimalis",
    category: "Baju Pengantin",
    price: 250000,
    deposit: 125000,
    status: "draft",
    stock: 4,
    orders: 0,
    image: "/placeholder.svg?height=60&width=60",
    createdAt: "2024-03-01",
    description:
      "Gaun akad dengan desain minimalis dan elegan. Cocok untuk acara akad nikah yang sederhana namun berkesan.",
  },
  {
    id: "5",
    name: "Gaun Wisuda Syar'i",
    category: "Baju Pengantin",
    price: 225000,
    deposit: 112500,
    status: "inactive",
    stock: 0,
    orders: 15,
    image: "/placeholder.svg?height=60&width=60",
    createdAt: "2024-02-20",
    description:
      "Gaun wisuda syar'i dengan warna navy elegant. Sementara tidak tersedia karena sedang dalam perbaikan.",
  },
]

const getStatusBadge = (status: string) => {
  switch (status) {
    case "active":
      return (
        <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
          <CheckCircle className="h-3 w-3 mr-1" />
          Aktif
        </Badge>
      )
    case "inactive":
      return (
        <Badge className="bg-red-100 text-red-800 hover:bg-red-100">
          <AlertCircle className="h-3 w-3 mr-1" />
          Tidak Aktif
        </Badge>
      )
    case "draft":
      return (
        <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
          <Clock className="h-3 w-3 mr-1" />
          Draft
        </Badge>
      )
    default:
      return <Badge variant="secondary">{status}</Badge>
  }
}

const getStockStatus = (stock: number) => {
  if (stock === 0) {
    return <span className="text-red-600 font-medium">Habis</span>
  } else if (stock <= 2) {
    return <span className="text-yellow-600 font-medium">Rendah ({stock})</span>
  } else {
    return <span className="text-green-600 font-medium">{stock} unit</span>
  }
}

export default function ProductsPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [categoryFilter, setCategoryFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [currentUser] = useState({
    name: "Admin User",
    role: "Super Admin",
    avatar: "/placeholder.svg?height=32&width=32",
  })

  const filteredProducts = products.filter((product) => {
    const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = categoryFilter === "all" || product.category === categoryFilter
    const matchesStatus = statusFilter === "all" || product.status === statusFilter
    return matchesSearch && matchesCategory && matchesStatus
  })

  const stats = [
    {
      title: "Total Produk",
      value: products.length.toString(),
      icon: Package,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
    },
    {
      title: "Produk Aktif",
      value: products.filter((p) => p.status === "active").length.toString(),
      icon: CheckCircle,
      color: "text-green-600",
      bgColor: "bg-green-50",
    },
    {
      title: "Stok Rendah",
      value: products.filter((p) => p.stock <= 2 && p.stock > 0).length.toString(),
      icon: AlertCircle,
      color: "text-yellow-600",
      bgColor: "bg-yellow-50",
    },
    {
      title: "Habis Stok",
      value: products.filter((p) => p.stock === 0).length.toString(),
      icon: AlertCircle,
      color: "text-red-600",
      bgColor: "bg-red-50",
    },
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      <AdminSidebar />

      <div className="lg:pl-64">
        <AdminHeader user={currentUser} />

        <main className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Kelola Produk</h1>
              <p className="text-gray-600">Kelola semua produk gaun syar'i Anda</p>
            </div>
            <Button asChild className="bg-rose-600 hover:bg-rose-700">
              <Link href="/admin/products/new">
                <Plus className="h-4 w-4 mr-2" />
                Tambah Produk
              </Link>
            </Button>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            {stats.map((stat, index) => {
              const Icon = stat.icon
              return (
                <Card key={index}>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                        <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                      </div>
                      <div className={`w-12 h-12 rounded-lg ${stat.bgColor} flex items-center justify-center`}>
                        <Icon className={`h-6 w-6 ${stat.color}`} />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>

          {/* Filters */}
          <Card className="mb-6">
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Cari produk..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                  <SelectTrigger className="w-full md:w-48">
                    <SelectValue placeholder="Kategori" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Semua Kategori</SelectItem>
                    <SelectItem value="Baju Pengantin">Baju Pengantin</SelectItem>
                    <SelectItem value="Baju Orang Tua (Ibu)">Baju Orang Tua (Ibu)</SelectItem>
                    <SelectItem value="Baju Ayah">Baju Ayah</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-full md:w-48">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Semua Status</SelectItem>
                    <SelectItem value="active">Aktif</SelectItem>
                    <SelectItem value="inactive">Tidak Aktif</SelectItem>
                    <SelectItem value="draft">Draft</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Products Table */}
          <Card>
            <CardHeader>
              <CardTitle>Daftar Produk ({filteredProducts.length})</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Produk</TableHead>
                    <TableHead>Kategori</TableHead>
                    <TableHead>Harga/Hari</TableHead>
                    <TableHead>DP</TableHead>
                    <TableHead>Stok</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Pesanan</TableHead>
                    <TableHead className="text-right">Aksi</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredProducts.map((product) => (
                    <TableRow key={product.id}>
                      <TableCell>
                        <div className="flex items-center space-x-3">
                          <img
                            src={product.image || "/placeholder.svg"}
                            alt={product.name}
                            className="w-12 h-12 rounded-lg object-cover"
                          />
                          <div>
                            <p className="font-medium text-gray-900">{product.name}</p>
                            <p className="text-sm text-gray-500 max-w-xs truncate">{product.description}</p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">{product.category}</Badge>
                      </TableCell>
                      <TableCell className="font-medium">Rp {product.price.toLocaleString()}</TableCell>
                      <TableCell className="text-gray-600">Rp {product.deposit.toLocaleString()}</TableCell>
                      <TableCell>{getStockStatus(product.stock)}</TableCell>
                      <TableCell>{getStatusBadge(product.status)}</TableCell>
                      <TableCell className="text-gray-600">{product.orders} pesanan</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Aksi</DropdownMenuLabel>
                            <DropdownMenuItem asChild>
                              <Link href={`/admin/products/${product.id}`}>
                                <Eye className="mr-2 h-4 w-4" />
                                Lihat Detail
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem asChild>
                              <Link href={`/admin/products/${product.id}/edit`}>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-red-600">
                              <Trash2 className="mr-2 h-4 w-4" />
                              Hapus
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </main>
      </div>
    </div>
  )
}
