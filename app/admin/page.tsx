"use client"

import { useState } from "react"
import Link from "next/link"
import { Package, CreditCard, Users, TrendingUp, ShoppingCart, Eye, Plus } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { AdminSidebar } from "@/components/admin/admin-sidebar"
import { AdminHeader } from "@/components/admin/admin-header"

const stats = [
  {
    title: "Total Pesanan Bulan Ini",
    value: "156",
    change: "+12%",
    changeType: "increase",
    icon: ShoppingCart,
    color: "text-blue-600",
    bgColor: "bg-blue-50",
  },
  {
    title: "Pendapatan Bulan Ini",
    value: "Rp 45.2M",
    change: "+8%",
    changeType: "increase",
    icon: CreditCard,
    color: "text-green-600",
    bgColor: "bg-green-50",
  },
  {
    title: "Pelanggan Aktif",
    value: "89",
    change: "+15%",
    changeType: "increase",
    icon: Users,
    color: "text-purple-600",
    bgColor: "bg-purple-50",
  },
  {
    title: "Tingkat Okupansi",
    value: "78%",
    change: "+5%",
    changeType: "increase",
    icon: TrendingUp,
    color: "text-rose-600",
    bgColor: "bg-rose-50",
  },
]

const recentOrders = [
  {
    id: "GSJ1710501234",
    customer: "Siti Aminah",
    product: "Gaun Pengantin Syar'i Mewah",
    amount: 750000,
    status: "confirmed",
    date: "15 Mar 2024",
    avatar: "/placeholder.svg?height=32&width=32",
  },
  {
    id: "GSJ1710501235",
    customer: "Fatimah Zahra",
    product: "Gaun Ibu Pengantin Elegant",
    amount: 500000,
    status: "shipped",
    date: "14 Mar 2024",
    avatar: "/placeholder.svg?height=32&width=32",
  },
  {
    id: "GSJ1710501236",
    customer: "Ahmad Rahman",
    product: "Baju Koko Ayah Premium",
    amount: 437500,
    status: "pending",
    date: "13 Mar 2024",
    avatar: "/placeholder.svg?height=32&width=32",
  },
  {
    id: "GSJ1710501237",
    customer: "Khadijah Rahman",
    product: "Gaun Wisuda Premium Gold",
    amount: 525000,
    status: "processing",
    date: "12 Mar 2024",
    avatar: "/placeholder.svg?height=32&width=32",
  },
]

const popularProducts = [
  {
    name: "Gaun Pengantin Syar'i Mewah",
    category: "Baju Pengantin",
    orders: 45,
    revenue: 33750000,
    image: "/placeholder.svg?height=60&width=60",
  },
  {
    name: "Gaun Ibu Pengantin Elegant",
    category: "Baju Orang Tua (Ibu)",
    orders: 32,
    revenue: 16000000,
    image: "/placeholder.svg?height=60&width=60",
  },
  {
    name: "Baju Koko Ayah Premium",
    category: "Baju Ayah",
    orders: 28,
    revenue: 12250000,
    image: "/placeholder.svg?height=60&width=60",
  },
]

const getStatusBadge = (status: string) => {
  switch (status) {
    case "confirmed":
      return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Dikonfirmasi</Badge>
    case "shipped":
      return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">Dikirim</Badge>
    case "pending":
      return <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">Menunggu</Badge>
    case "processing":
      return <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-100">Diproses</Badge>
    default:
      return <Badge variant="secondary">{status}</Badge>
  }
}

export default function AdminDashboard() {
  const [currentUser] = useState({
    name: "Admin User",
    role: "Super Admin",
    avatar: "/placeholder.svg?height=32&width=32",
  })

  return (
    <div className="min-h-screen bg-gray-50">
      <AdminSidebar />

      <div className="lg:pl-64">
        <AdminHeader user={currentUser} />

        <main className="p-6">
          {/* Welcome Section */}
          <div className="mb-8">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Selamat datang kembali, {currentUser.name}</h1>
            <p className="text-gray-600">Berikut adalah ringkasan aktivitas GaunSyariJogja.com hari ini</p>
          </div>

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {stats.map((stat, index) => {
              const Icon = stat.icon
              return (
                <Card key={index} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <p className="text-sm font-medium text-gray-600 mb-1">{stat.title}</p>
                        <p className="text-2xl font-bold text-gray-900 mb-1">{stat.value}</p>
                        <div className="flex items-center">
                          <span
                            className={`text-sm font-medium ${
                              stat.changeType === "increase" ? "text-green-600" : "text-red-600"
                            }`}
                          >
                            {stat.change}
                          </span>
                          <span className="text-sm text-gray-500 ml-1">dari bulan lalu</span>
                        </div>
                      </div>
                      <div className={`w-12 h-12 rounded-lg ${stat.bgColor} flex items-center justify-center`}>
                        <Icon className={`h-6 w-6 ${stat.color}`} />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>

          <div className="grid lg:grid-cols-3 gap-6 mb-8">
            {/* Recent Orders */}
            <Card className="lg:col-span-2">
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Pesanan Terbaru</CardTitle>
                <Button variant="outline" size="sm" asChild>
                  <Link href="/admin/orders">
                    <Eye className="h-4 w-4 mr-2" />
                    Lihat Semua
                  </Link>
                </Button>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentOrders.map((order) => (
                    <div
                      key={order.id}
                      className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex items-center space-x-4">
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={order.avatar || "/placeholder.svg"} alt={order.customer} />
                          <AvatarFallback>{order.customer.charAt(0)}</AvatarFallback>
                        </Avatar>
                        <div>
                          <p className="font-medium text-gray-900">{order.customer}</p>
                          <p className="text-sm text-gray-600">{order.product}</p>
                          <p className="text-xs text-gray-500">{order.date}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-gray-900">Rp {order.amount.toLocaleString()}</p>
                        {getStatusBadge(order.status)}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Popular Products */}
            <Card>
              <CardHeader>
                <CardTitle>Produk Terpopuler</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {popularProducts.map((product, index) => (
                    <div key={index} className="flex items-center space-x-4">
                      <img
                        src={product.image || "/placeholder.svg"}
                        alt={product.name}
                        className="w-12 h-12 rounded-lg object-cover"
                      />
                      <div className="flex-1 min-w-0">
                        <p className="font-medium text-gray-900 truncate">{product.name}</p>
                        <p className="text-sm text-gray-600">{product.category}</p>
                        <div className="flex items-center justify-between mt-1">
                          <span className="text-xs text-gray-500">{product.orders} pesanan</span>
                          <span className="text-xs font-medium text-green-600">
                            Rp {(product.revenue / 1000000).toFixed(1)}M
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
            <Button asChild className="h-20 flex-col bg-rose-600 hover:bg-rose-700">
              <Link href="/admin/products/new">
                <Plus className="h-6 w-6 mb-2" />
                Tambah Produk
              </Link>
            </Button>
            <Button asChild variant="outline" className="h-20 flex-col bg-transparent">
              <Link href="/admin/orders">
                <Package className="h-6 w-6 mb-2" />
                Kelola Pesanan
              </Link>
            </Button>
            <Button asChild variant="outline" className="h-20 flex-col bg-transparent">
              <Link href="/admin/users">
                <Users className="h-6 w-6 mb-2" />
                Kelola Pengguna
              </Link>
            </Button>
            <Button asChild variant="outline" className="h-20 flex-col bg-transparent">
              <Link href="/admin/reports">
                <TrendingUp className="h-6 w-6 mb-2" />
                Lihat Laporan
              </Link>
            </Button>
          </div>

          {/* Activity Timeline */}
          <Card>
            <CardHeader>
              <CardTitle>Aktivitas Terbaru</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-start space-x-4">
                  <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">Pesanan baru dari Siti Aminah</p>
                    <p className="text-xs text-gray-500">2 menit yang lalu</p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">
                      Pembayaran dikonfirmasi untuk pesanan GSJ1710501235
                    </p>
                    <p className="text-xs text-gray-500">15 menit yang lalu</p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <div className="w-2 h-2 bg-purple-500 rounded-full mt-2"></div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">Produk baru "Gaun Akad Minimalis" ditambahkan</p>
                    <p className="text-xs text-gray-500">1 jam yang lalu</p>
                  </div>
                </div>
                <div className="flex items-start space-x-4">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
                  <div>
                    <p className="text-sm font-medium text-gray-900">Stok gaun "Gaun Pengantin Mewah" hampir habis</p>
                    <p className="text-xs text-gray-500">3 jam yang lalu</p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </main>
      </div>
    </div>
  )
}
