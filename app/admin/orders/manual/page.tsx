"use client"

import { useState } from "react"
import { ArrowLeft, ArrowRight, Check, Save, Send, FileText, User, Package, CreditCard } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { AdminSidebar } from "@/components/admin/admin-sidebar"
import { AdminHeader } from "@/components/admin/admin-header"
import { ProductSelector } from "@/components/admin/product-selector"
import { CustomerSelector } from "@/components/admin/customer-selector"
import { cn } from "@/lib/utils"

const steps = [
  { id: 1, name: "<PERSON><PERSON><PERSON><PERSON>", icon: User },
  { id: 2, name: "<PERSON>du<PERSON>", icon: Package },
  { id: 3, name: "Pembayaran", icon: CreditCard },
  { id: 4, name: "Review", icon: FileText },
]

interface OrderItem {
  id: string
  name: string
  category: string
  size: string
  color: string
  quantity: number
  price: number
  startDate: string
  endDate: string
  image: string
}

interface Customer {
  id?: string
  name: string
  email: string
  phone: string
  address: string
  city: string
  postalCode: string
  isExisting: boolean
}

export default function ManualOrderPage() {
  const [currentStep, setCurrentStep] = useState(1)
  const [customer, setCustomer] = useState<Customer>({
    name: "",
    email: "",
    phone: "",
    address: "",
    city: "",
    postalCode: "",
    isExisting: false,
  })
  const [orderItems, setOrderItems] = useState<OrderItem[]>([])
  const [paymentMethod, setPaymentMethod] = useState("")
  const [paymentStatus, setPaymentStatus] = useState("pending")
  const [orderSource, setOrderSource] = useState("")
  const [discount, setDiscount] = useState({ type: "percentage", value: 0 })
  const [shippingFee, setShippingFee] = useState(0)
  const [notes, setNotes] = useState("")
  const [dueDate, setDueDate] = useState("")

  const [currentUser] = useState({
    name: "Admin User",
    role: "Super Admin",
    avatar: "/placeholder.svg?height=32&width=32",
  })

  const subtotal = orderItems.reduce((sum, item) => sum + item.price * item.quantity, 0)
  const discountAmount = discount.type === "percentage" ? (subtotal * discount.value) / 100 : discount.value
  const total = subtotal - discountAmount + shippingFee

  const handleCustomerSelect = (selectedCustomer: Customer) => {
    setCustomer(selectedCustomer)
  }

  const handleProductAdd = (product: OrderItem) => {
    setOrderItems([...orderItems, { ...product, id: Date.now().toString() }])
  }

  const handleProductRemove = (id: string) => {
    setOrderItems(orderItems.filter((item) => item.id !== id))
  }

  const handleNext = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1)
    }
  }

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const handleSaveDraft = () => {
    // Save as draft logic
    console.log("Saving draft...")
  }

  const handleCreateOrder = () => {
    // Create order logic
    console.log("Creating order...")
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <CustomerSelector onCustomerSelect={handleCustomerSelect} />

            {!customer.isExisting && (
              <Card>
                <CardHeader>
                  <CardTitle>Informasi Pelanggan Baru</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="name">Nama Lengkap *</Label>
                      <Input
                        id="name"
                        value={customer.name}
                        onChange={(e) => setCustomer({ ...customer, name: e.target.value })}
                        placeholder="Masukkan nama lengkap"
                      />
                    </div>
                    <div>
                      <Label htmlFor="email">Email *</Label>
                      <Input
                        id="email"
                        type="email"
                        value={customer.email}
                        onChange={(e) => setCustomer({ ...customer, email: e.target.value })}
                        placeholder="<EMAIL>"
                      />
                    </div>
                    <div>
                      <Label htmlFor="phone">Nomor Telepon *</Label>
                      <Input
                        id="phone"
                        value={customer.phone}
                        onChange={(e) => setCustomer({ ...customer, phone: e.target.value })}
                        placeholder="08xxxxxxxxxx"
                      />
                    </div>
                    <div>
                      <Label htmlFor="city">Kota *</Label>
                      <Input
                        id="city"
                        value={customer.city}
                        onChange={(e) => setCustomer({ ...customer, city: e.target.value })}
                        placeholder="Yogyakarta"
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="address">Alamat Lengkap *</Label>
                    <Textarea
                      id="address"
                      value={customer.address}
                      onChange={(e) => setCustomer({ ...customer, address: e.target.value })}
                      placeholder="Masukkan alamat lengkap"
                      rows={3}
                    />
                  </div>
                  <div className="w-full md:w-1/2">
                    <Label htmlFor="postalCode">Kode Pos</Label>
                    <Input
                      id="postalCode"
                      value={customer.postalCode}
                      onChange={(e) => setCustomer({ ...customer, postalCode: e.target.value })}
                      placeholder="55xxx"
                    />
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        )

      case 2:
        return (
          <div className="space-y-6">
            <ProductSelector onProductAdd={handleProductAdd} />

            {orderItems.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle>Produk Terpilih ({orderItems.length})</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {orderItems.map((item) => (
                      <div key={item.id} className="flex items-center space-x-4 p-4 border rounded-lg">
                        <img
                          src={item.image || "/placeholder.svg?height=60&width=60"}
                          alt={item.name}
                          className="w-15 h-15 rounded-lg object-cover"
                        />
                        <div className="flex-1">
                          <h4 className="font-medium">{item.name}</h4>
                          <p className="text-sm text-gray-600">{item.category}</p>
                          <div className="flex items-center space-x-4 mt-2 text-sm">
                            <span>Ukuran: {item.size}</span>
                            <span>Warna: {item.color}</span>
                            <span>Qty: {item.quantity}</span>
                          </div>
                          <div className="flex items-center space-x-4 mt-1 text-sm text-gray-600">
                            <span>
                              {item.startDate} - {item.endDate}
                            </span>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-medium">Rp {item.price.toLocaleString()}</p>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleProductRemove(item.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            Hapus
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        )

      case 3:
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Informasi Pembayaran</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="paymentMethod">Metode Pembayaran *</Label>
                    <Select value={paymentMethod} onValueChange={setPaymentMethod}>
                      <SelectTrigger>
                        <SelectValue placeholder="Pilih metode pembayaran" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="bank_transfer">Transfer Bank</SelectItem>
                        <SelectItem value="cash">Tunai</SelectItem>
                        <SelectItem value="e_wallet">E-Wallet</SelectItem>
                        <SelectItem value="credit_card">Kartu Kredit</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="paymentStatus">Status Pembayaran</Label>
                    <Select value={paymentStatus} onValueChange={setPaymentStatus}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="pending">Menunggu Pembayaran</SelectItem>
                        <SelectItem value="partial">Dibayar Sebagian</SelectItem>
                        <SelectItem value="paid">Lunas</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="orderSource">Sumber Pesanan *</Label>
                    <Select value={orderSource} onValueChange={setOrderSource}>
                      <SelectTrigger>
                        <SelectValue placeholder="Pilih sumber pesanan" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="phone">Telepon</SelectItem>
                        <SelectItem value="whatsapp">WhatsApp</SelectItem>
                        <SelectItem value="email">Email</SelectItem>
                        <SelectItem value="walk_in">Datang Langsung</SelectItem>
                        <SelectItem value="social_media">Media Sosial</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="dueDate">Batas Waktu Pembayaran</Label>
                    <Input id="dueDate" type="date" value={dueDate} onChange={(e) => setDueDate(e.target.value)} />
                  </div>
                </div>

                <Separator />

                <div className="space-y-4">
                  <h4 className="font-medium">Diskon & Biaya Tambahan</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <Label>Jenis Diskon</Label>
                      <Select
                        value={discount.type}
                        onValueChange={(value) => setDiscount({ ...discount, type: value })}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="percentage">Persentase (%)</SelectItem>
                          <SelectItem value="fixed">Nominal (Rp)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label>Nilai Diskon</Label>
                      <Input
                        type="number"
                        value={discount.value}
                        onChange={(e) => setDiscount({ ...discount, value: Number(e.target.value) })}
                        placeholder="0"
                      />
                    </div>
                    <div>
                      <Label>Biaya Pengiriman</Label>
                      <Input
                        type="number"
                        value={shippingFee}
                        onChange={(e) => setShippingFee(Number(e.target.value))}
                        placeholder="0"
                      />
                    </div>
                  </div>
                </div>

                <div>
                  <Label htmlFor="notes">Catatan Pesanan</Label>
                  <Textarea
                    id="notes"
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    placeholder="Tambahkan catatan khusus untuk pesanan ini..."
                    rows={3}
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        )

      case 4:
        return (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Review Pesanan</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                {/* Customer Info */}
                <div>
                  <h4 className="font-medium mb-3">Informasi Pelanggan</h4>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <p className="font-medium">{customer.name}</p>
                    <p className="text-sm text-gray-600">{customer.email}</p>
                    <p className="text-sm text-gray-600">{customer.phone}</p>
                    <p className="text-sm text-gray-600">
                      {customer.address}, {customer.city} {customer.postalCode}
                    </p>
                  </div>
                </div>

                {/* Order Items */}
                <div>
                  <h4 className="font-medium mb-3">Produk Pesanan</h4>
                  <div className="space-y-3">
                    {orderItems.map((item) => (
                      <div key={item.id} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                        <div>
                          <p className="font-medium">{item.name}</p>
                          <p className="text-sm text-gray-600">
                            {item.size} • {item.color} • Qty: {item.quantity}
                          </p>
                          <p className="text-sm text-gray-600">
                            {item.startDate} - {item.endDate}
                          </p>
                        </div>
                        <p className="font-medium">Rp {(item.price * item.quantity).toLocaleString()}</p>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Order Summary */}
                <div>
                  <h4 className="font-medium mb-3">Ringkasan Pembayaran</h4>
                  <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                    <div className="flex justify-between">
                      <span>Subtotal</span>
                      <span>Rp {subtotal.toLocaleString()}</span>
                    </div>
                    {discountAmount > 0 && (
                      <div className="flex justify-between text-green-600">
                        <span>Diskon ({discount.type === "percentage" ? `${discount.value}%` : "Nominal"})</span>
                        <span>-Rp {discountAmount.toLocaleString()}</span>
                      </div>
                    )}
                    {shippingFee > 0 && (
                      <div className="flex justify-between">
                        <span>Biaya Pengiriman</span>
                        <span>Rp {shippingFee.toLocaleString()}</span>
                      </div>
                    )}
                    <Separator />
                    <div className="flex justify-between font-bold text-lg">
                      <span>Total</span>
                      <span>Rp {total.toLocaleString()}</span>
                    </div>
                  </div>
                </div>

                {/* Payment & Order Info */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium mb-2">Informasi Pembayaran</h4>
                    <div className="text-sm space-y-1">
                      <p>Metode: {paymentMethod}</p>
                      <p>
                        Status: <Badge variant="secondary">{paymentStatus}</Badge>
                      </p>
                      {dueDate && <p>Batas Waktu: {dueDate}</p>}
                    </div>
                  </div>
                  <div>
                    <h4 className="font-medium mb-2">Informasi Pesanan</h4>
                    <div className="text-sm space-y-1">
                      <p>Sumber: {orderSource}</p>
                      {notes && <p>Catatan: {notes}</p>}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <AdminSidebar />

      <div className="lg:pl-64">
        <AdminHeader user={currentUser} />

        <main className="p-6">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Buat Pesanan Manual</h1>
                <p className="text-gray-600">
                  Buat pesanan baru untuk pelanggan dari telepon, email, atau saluran offline lainnya
                </p>
              </div>
              <div className="flex space-x-3">
                <Button variant="outline" onClick={handleSaveDraft}>
                  <Save className="h-4 w-4 mr-2" />
                  Simpan Draft
                </Button>
              </div>
            </div>
          </div>

          {/* Progress Steps */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              {steps.map((step, index) => {
                const Icon = step.icon
                const isActive = currentStep === step.id
                const isCompleted = currentStep > step.id

                return (
                  <div key={step.id} className="flex items-center">
                    <div
                      className={cn(
                        "flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors",
                        isActive
                          ? "border-rose-600 bg-rose-600 text-white"
                          : isCompleted
                            ? "border-green-600 bg-green-600 text-white"
                            : "border-gray-300 bg-white text-gray-400",
                      )}
                    >
                      {isCompleted ? <Check className="h-5 w-5" /> : <Icon className="h-5 w-5" />}
                    </div>
                    <div className="ml-3">
                      <p className={cn("text-sm font-medium", isActive ? "text-rose-600" : "text-gray-500")}>
                        {step.name}
                      </p>
                    </div>
                    {index < steps.length - 1 && (
                      <div className="flex-1 mx-8">
                        <div className={cn("h-0.5 transition-colors", isCompleted ? "bg-green-600" : "bg-gray-300")} />
                      </div>
                    )}
                  </div>
                )
              })}
            </div>
          </div>

          {/* Step Content */}
          <div className="mb-8">{renderStepContent()}</div>

          {/* Navigation */}
          <div className="flex items-center justify-between">
            <Button variant="outline" onClick={handlePrevious} disabled={currentStep === 1}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Sebelumnya
            </Button>

            <div className="flex space-x-3">
              {currentStep === steps.length ? (
                <>
                  <Button variant="outline" onClick={handleCreateOrder}>
                    <FileText className="h-4 w-4 mr-2" />
                    Buat & Cetak Invoice
                  </Button>
                  <Button onClick={handleCreateOrder} className="bg-rose-600 hover:bg-rose-700">
                    <Send className="h-4 w-4 mr-2" />
                    Buat Pesanan & Kirim Email
                  </Button>
                </>
              ) : (
                <Button
                  onClick={handleNext}
                  disabled={
                    (currentStep === 1 && !customer.name) ||
                    (currentStep === 2 && orderItems.length === 0) ||
                    (currentStep === 3 && (!paymentMethod || !orderSource))
                  }
                  className="bg-rose-600 hover:bg-rose-700"
                >
                  Selanjutnya
                  <ArrowRight className="h-4 w-4 ml-2" />
                </Button>
              )}
            </div>
          </div>
        </main>
      </div>
    </div>
  )
}
