"use client"

import { useState } from "react"
import Link from "next/link"
import {
  Search,
  MoreHorizontal,
  Eye,
  Check,
  X,
  Clock,
  Truck,
  Package,
  CheckCircle,
  AlertCircle,
  Download,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { AdminSidebar } from "@/components/admin/admin-sidebar"
import { AdminHeader } from "@/components/admin/admin-header"

const orders = [
  {
    id: "GSJ1710501234",
    customerName: "Siti Aminah",
    customerPhone: "+62 812-3456-7890",
    customerEmail: "<EMAIL>",
    dress: "Gaun Pengantin Syar'i Mewah",
    category: "Baju Pengantin",
    rentalDate: "15-17 Mar 2024",
    duration: "3 hari",
    totalAmount: 750000,
    deposit: 375000,
    paymentStatus: "confirmed",
    orderStatus: "shipped",
    createdAt: "15 Mar 2024, 14:30",
    avatar: "/placeholder.svg?height=32&width=32",
    address: "Jl. Malioboro No. 123, Yogyakarta",
  },
  {
    id: "GSJ1710501235",
    customerName: "Fatimah Zahra",
    customerPhone: "+62 813-7890-1234",
    customerEmail: "<EMAIL>",
    dress: "Gaun Ibu Pengantin Elegant",
    category: "Baju Orang Tua (Ibu)",
    rentalDate: "20-22 Mar 2024",
    duration: "3 hari",
    totalAmount: 500000,
    deposit: 250000,
    paymentStatus: "confirmed",
    orderStatus: "delivered",
    createdAt: "14 Mar 2024, 10:15",
    avatar: "/placeholder.svg?height=32&width=32",
    address: "Jl. Sudirman No. 456, Yogyakarta",
  },
  {
    id: "GSJ1710501236",
    customerName: "Ahmad Rahman",
    customerPhone: "+62 814-5678-9012",
    customerEmail: "<EMAIL>",
    dress: "Baju Koko Ayah Premium",
    category: "Baju Ayah",
    rentalDate: "25-27 Mar 2024",
    duration: "3 hari",
    totalAmount: 437500,
    deposit: 218750,
    paymentStatus: "pending",
    orderStatus: "pending_payment",
    createdAt: "13 Mar 2024, 16:45",
    avatar: "/placeholder.svg?height=32&width=32",
    address: "Jl. Tugu No. 789, Yogyakarta",
  },
  {
    id: "GSJ1710501237",
    customerName: "Khadijah Rahman",
    customerPhone: "+62 815-9012-3456",
    customerEmail: "<EMAIL>",
    dress: "Gaun Wisuda Premium Gold",
    category: "Baju Pengantin",
    rentalDate: "28-30 Mar 2024",
    duration: "3 hari",
    totalAmount: 525000,
    deposit: 262500,
    paymentStatus: "waiting_confirmation",
    orderStatus: "processing",
    createdAt: "12 Mar 2024, 09:30",
    avatar: "/placeholder.svg?height=32&width=32",
    address: "Jl. Gejayan No. 321, Yogyakarta",
  },
]

const getPaymentStatusBadge = (status: string) => {
  switch (status) {
    case "confirmed":
      return (
        <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
          <CheckCircle className="h-3 w-3 mr-1" />
          Dikonfirmasi
        </Badge>
      )
    case "pending":
      return (
        <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
          <Clock className="h-3 w-3 mr-1" />
          Menunggu
        </Badge>
      )
    case "waiting_confirmation":
      return (
        <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">
          <AlertCircle className="h-3 w-3 mr-1" />
          Perlu Konfirmasi
        </Badge>
      )
    default:
      return <Badge variant="secondary">{status}</Badge>
  }
}

const getOrderStatusBadge = (status: string) => {
  switch (status) {
    case "pending_payment":
      return (
        <Badge className="bg-red-100 text-red-800 hover:bg-red-100">
          <X className="h-3 w-3 mr-1" />
          Menunggu Pembayaran
        </Badge>
      )
    case "processing":
      return (
        <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
          <Clock className="h-3 w-3 mr-1" />
          Diproses
        </Badge>
      )
    case "shipped":
      return (
        <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">
          <Truck className="h-3 w-3 mr-1" />
          Dikirim
        </Badge>
      )
    case "delivered":
      return (
        <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
          <CheckCircle className="h-3 w-3 mr-1" />
          Selesai
        </Badge>
      )
    default:
      return <Badge variant="secondary">{status}</Badge>
  }
}

export default function OrdersPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [paymentStatusFilter, setPaymentStatusFilter] = useState("all")
  const [orderStatusFilter, setOrderStatusFilter] = useState("all")
  const [currentUser] = useState({
    name: "Admin User",
    role: "Super Admin",
    avatar: "/placeholder.svg?height=32&width=32",
  })

  const filteredOrders = orders.filter((order) => {
    const matchesSearch =
      order.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.dress.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesPaymentStatus = paymentStatusFilter === "all" || order.paymentStatus === paymentStatusFilter
    const matchesOrderStatus = orderStatusFilter === "all" || order.orderStatus === orderStatusFilter
    return matchesSearch && matchesPaymentStatus && matchesOrderStatus
  })

  const stats = [
    {
      title: "Total Pesanan",
      value: orders.length.toString(),
      icon: Package,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
    },
    {
      title: "Menunggu Konfirmasi",
      value: orders.filter((o) => o.paymentStatus === "waiting_confirmation").length.toString(),
      icon: AlertCircle,
      color: "text-yellow-600",
      bgColor: "bg-yellow-50",
    },
    {
      title: "Dalam Pengiriman",
      value: orders.filter((o) => o.orderStatus === "shipped").length.toString(),
      icon: Truck,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
    },
    {
      title: "Selesai",
      value: orders.filter((o) => o.orderStatus === "delivered").length.toString(),
      icon: CheckCircle,
      color: "text-green-600",
      bgColor: "bg-green-50",
    },
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      <AdminSidebar />

      <div className="lg:pl-64">
        <AdminHeader user={currentUser} />

        <main className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Kelola Pesanan</h1>
              <p className="text-gray-600">Kelola semua pesanan pelanggan</p>
            </div>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Data
            </Button>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            {stats.map((stat, index) => {
              const Icon = stat.icon
              return (
                <Card key={index}>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                        <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                      </div>
                      <div className={`w-12 h-12 rounded-lg ${stat.bgColor} flex items-center justify-center`}>
                        <Icon className={`h-6 w-6 ${stat.color}`} />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>

          {/* Filters */}
          <Card className="mb-6">
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Cari pesanan, pelanggan, atau produk..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <Select value={paymentStatusFilter} onValueChange={setPaymentStatusFilter}>
                  <SelectTrigger className="w-full md:w-48">
                    <SelectValue placeholder="Status Pembayaran" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Semua Pembayaran</SelectItem>
                    <SelectItem value="confirmed">Dikonfirmasi</SelectItem>
                    <SelectItem value="waiting_confirmation">Perlu Konfirmasi</SelectItem>
                    <SelectItem value="pending">Menunggu</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={orderStatusFilter} onValueChange={setOrderStatusFilter}>
                  <SelectTrigger className="w-full md:w-48">
                    <SelectValue placeholder="Status Pesanan" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Semua Status</SelectItem>
                    <SelectItem value="pending_payment">Menunggu Pembayaran</SelectItem>
                    <SelectItem value="processing">Diproses</SelectItem>
                    <SelectItem value="shipped">Dikirim</SelectItem>
                    <SelectItem value="delivered">Selesai</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Orders Table */}
          <Card>
            <CardHeader>
              <CardTitle>Daftar Pesanan ({filteredOrders.length})</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Pelanggan</TableHead>
                    <TableHead>Produk</TableHead>
                    <TableHead>Tanggal Sewa</TableHead>
                    <TableHead>Total</TableHead>
                    <TableHead>Status Pembayaran</TableHead>
                    <TableHead>Status Pesanan</TableHead>
                    <TableHead className="text-right">Aksi</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredOrders.map((order) => (
                    <TableRow key={order.id}>
                      <TableCell>
                        <div className="flex items-center space-x-3">
                          <Avatar className="h-10 w-10">
                            <AvatarImage src={order.avatar || "/placeholder.svg"} alt={order.customerName} />
                            <AvatarFallback>{order.customerName.charAt(0)}</AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium text-gray-900">{order.customerName}</p>
                            <p className="text-sm text-gray-500">{order.id}</p>
                            <p className="text-xs text-gray-400">{order.customerPhone}</p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium text-gray-900">{order.dress}</p>
                          <Badge variant="outline" className="mt-1">
                            {order.category}
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium text-gray-900">{order.rentalDate}</p>
                          <p className="text-sm text-gray-500">{order.duration}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <p className="font-medium text-gray-900">Rp {order.totalAmount.toLocaleString()}</p>
                          <p className="text-sm text-gray-500">DP: Rp {order.deposit.toLocaleString()}</p>
                        </div>
                      </TableCell>
                      <TableCell>{getPaymentStatusBadge(order.paymentStatus)}</TableCell>
                      <TableCell>{getOrderStatusBadge(order.orderStatus)}</TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Aksi</DropdownMenuLabel>
                            <DropdownMenuItem asChild>
                              <Link href={`/admin/orders/${order.id}`}>
                                <Eye className="mr-2 h-4 w-4" />
                                Lihat Detail
                              </Link>
                            </DropdownMenuItem>
                            {order.paymentStatus === "waiting_confirmation" && (
                              <>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem className="text-green-600">
                                  <Check className="mr-2 h-4 w-4" />
                                  Konfirmasi Pembayaran
                                </DropdownMenuItem>
                                <DropdownMenuItem className="text-red-600">
                                  <X className="mr-2 h-4 w-4" />
                                  Tolak Pembayaran
                                </DropdownMenuItem>
                              </>
                            )}
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </main>
      </div>
    </div>
  )
}
