"use client"

import { useState } from "react"
import {
  Search,
  MoreHorizontal,
  Eye,
  Edit,
  Trash2,
  UserPlus,
  Shield,
  User,
  Crown,
  UsersIcon,
  Mail,
  Phone,
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { AdminSidebar } from "@/components/admin/admin-sidebar"
import { AdminHeader } from "@/components/admin/admin-header"

const users = [
  {
    id: "1",
    name: "Admin User",
    email: "<EMAIL>",
    phone: "+62 812-3456-7890",
    role: "Super Admin",
    status: "active",
    lastLogin: "2024-03-15 14:30",
    createdAt: "2024-01-01",
    avatar: "/placeholder.svg?height=32&width=32",
    totalOrders: 0,
  },
  {
    id: "2",
    name: "Siti Aminah",
    email: "<EMAIL>",
    phone: "+62 813-7890-1234",
    role: "Customer",
    status: "active",
    lastLogin: "2024-03-15 10:15",
    createdAt: "2024-02-15",
    avatar: "/placeholder.svg?height=32&width=32",
    totalOrders: 3,
  },
  {
    id: "3",
    name: "Product Manager",
    email: "<EMAIL>",
    phone: "+62 814-5678-9012",
    role: "Product Manager",
    status: "active",
    lastLogin: "2024-03-14 16:45",
    createdAt: "2024-01-15",
    avatar: "/placeholder.svg?height=32&width=32",
    totalOrders: 0,
  },
  {
    id: "4",
    name: "Fatimah Zahra",
    email: "<EMAIL>",
    phone: "+62 815-9012-3456",
    role: "Customer",
    status: "active",
    lastLogin: "2024-03-13 09:30",
    createdAt: "2024-02-20",
    avatar: "/placeholder.svg?height=32&width=32",
    totalOrders: 2,
  },
  {
    id: "5",
    name: "Order Manager",
    email: "<EMAIL>",
    phone: "+62 816-2345-6789",
    role: "Order Manager",
    status: "active",
    lastLogin: "2024-03-12 11:20",
    createdAt: "2024-01-20",
    avatar: "/placeholder.svg?height=32&width=32",
    totalOrders: 0,
  },
  {
    id: "6",
    name: "Ahmad Rahman",
    email: "<EMAIL>",
    phone: "+62 817-3456-7890",
    role: "Customer",
    status: "inactive",
    lastLogin: "2024-02-28 15:45",
    createdAt: "2024-01-25",
    avatar: "/placeholder.svg?height=32&width=32",
    totalOrders: 1,
  },
]

const getRoleBadge = (role: string) => {
  switch (role) {
    case "Super Admin":
      return (
        <Badge className="bg-purple-100 text-purple-800 hover:bg-purple-100">
          <Crown className="h-3 w-3 mr-1" />
          Super Admin
        </Badge>
      )
    case "Product Manager":
      return (
        <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">
          <Shield className="h-3 w-3 mr-1" />
          Product Manager
        </Badge>
      )
    case "Order Manager":
      return (
        <Badge className="bg-green-100 text-green-800 hover:bg-green-100">
          <Shield className="h-3 w-3 mr-1" />
          Order Manager
        </Badge>
      )
    case "Content Editor":
      return (
        <Badge className="bg-yellow-100 text-yellow-800 hover:bg-yellow-100">
          <Shield className="h-3 w-3 mr-1" />
          Content Editor
        </Badge>
      )
    case "Analyst":
      return (
        <Badge className="bg-orange-100 text-orange-800 hover:bg-orange-100">
          <Shield className="h-3 w-3 mr-1" />
          Analyst
        </Badge>
      )
    case "Customer":
      return (
        <Badge variant="outline">
          <User className="h-3 w-3 mr-1" />
          Customer
        </Badge>
      )
    default:
      return <Badge variant="secondary">{role}</Badge>
  }
}

const getStatusBadge = (status: string) => {
  switch (status) {
    case "active":
      return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Aktif</Badge>
    case "inactive":
      return <Badge className="bg-red-100 text-red-800 hover:bg-red-100">Tidak Aktif</Badge>
    default:
      return <Badge variant="secondary">{status}</Badge>
  }
}

export default function UsersPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [roleFilter, setRoleFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [currentUser] = useState({
    name: "Admin User",
    role: "Super Admin",
    avatar: "/placeholder.svg?height=32&width=32",
  })

  const filteredUsers = users.filter((user) => {
    const matchesSearch =
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.phone.includes(searchTerm)
    const matchesRole = roleFilter === "all" || user.role === roleFilter
    const matchesStatus = statusFilter === "all" || user.status === statusFilter
    return matchesSearch && matchesRole && matchesStatus
  })

  const stats = [
    {
      title: "Total Pengguna",
      value: users.length.toString(),
      icon: UsersIcon,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
    },
    {
      title: "Admin",
      value: users.filter((u) => u.role !== "Customer").length.toString(),
      icon: Shield,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
    },
    {
      title: "Pelanggan",
      value: users.filter((u) => u.role === "Customer").length.toString(),
      icon: User,
      color: "text-green-600",
      bgColor: "bg-green-50",
    },
    {
      title: "Aktif Hari Ini",
      value: users.filter((u) => u.lastLogin.includes("2024-03-15")).length.toString(),
      icon: UsersIcon,
      color: "text-orange-600",
      bgColor: "bg-orange-50",
    },
  ]

  return (
    <div className="min-h-screen bg-gray-50">
      <AdminSidebar />

      <div className="lg:pl-64">
        <AdminHeader user={currentUser} />

        <main className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Kelola Pengguna</h1>
              <p className="text-gray-600">Kelola akun pengguna dan administrator</p>
            </div>
            <Button className="bg-rose-600 hover:bg-rose-700">
              <UserPlus className="h-4 w-4 mr-2" />
              Tambah Pengguna
            </Button>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            {stats.map((stat, index) => {
              const Icon = stat.icon
              return (
                <Card key={index}>
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                        <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                      </div>
                      <div className={`w-12 h-12 rounded-lg ${stat.bgColor} flex items-center justify-center`}>
                        <Icon className={`h-6 w-6 ${stat.color}`} />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })}
          </div>

          {/* Filters */}
          <Card className="mb-6">
            <CardContent className="p-6">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Cari nama, email, atau telepon..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <Select value={roleFilter} onValueChange={setRoleFilter}>
                  <SelectTrigger className="w-full md:w-48">
                    <SelectValue placeholder="Role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Semua Role</SelectItem>
                    <SelectItem value="Super Admin">Super Admin</SelectItem>
                    <SelectItem value="Product Manager">Product Manager</SelectItem>
                    <SelectItem value="Order Manager">Order Manager</SelectItem>
                    <SelectItem value="Content Editor">Content Editor</SelectItem>
                    <SelectItem value="Analyst">Analyst</SelectItem>
                    <SelectItem value="Customer">Customer</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-full md:w-48">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Semua Status</SelectItem>
                    <SelectItem value="active">Aktif</SelectItem>
                    <SelectItem value="inactive">Tidak Aktif</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Users Table */}
          <Card>
            <CardHeader>
              <CardTitle>Daftar Pengguna ({filteredUsers.length})</CardTitle>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Pengguna</TableHead>
                    <TableHead>Kontak</TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Total Pesanan</TableHead>
                    <TableHead>Login Terakhir</TableHead>
                    <TableHead className="text-right">Aksi</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredUsers.map((user) => (
                    <TableRow key={user.id}>
                      <TableCell>
                        <div className="flex items-center space-x-3">
                          <Avatar className="h-10 w-10">
                            <AvatarImage src={user.avatar || "/placeholder.svg"} alt={user.name} />
                            <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium text-gray-900">{user.name}</p>
                            <p className="text-sm text-gray-500">ID: {user.id}</p>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="flex items-center text-sm text-gray-600">
                            <Mail className="h-3 w-3 mr-2" />
                            {user.email}
                          </div>
                          <div className="flex items-center text-sm text-gray-600">
                            <Phone className="h-3 w-3 mr-2" />
                            {user.phone}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>{getRoleBadge(user.role)}</TableCell>
                      <TableCell>{getStatusBadge(user.status)}</TableCell>
                      <TableCell>
                        <span className="font-medium text-gray-900">{user.totalOrders} pesanan</span>
                      </TableCell>
                      <TableCell>
                        <span className="text-sm text-gray-600">{user.lastLogin}</span>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Aksi</DropdownMenuLabel>
                            <DropdownMenuItem>
                              <Eye className="mr-2 h-4 w-4" />
                              Lihat Detail
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit Pengguna
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-red-600">
                              <Trash2 className="mr-2 h-4 w-4" />
                              Hapus Pengguna
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </main>
      </div>
    </div>
  )
}
