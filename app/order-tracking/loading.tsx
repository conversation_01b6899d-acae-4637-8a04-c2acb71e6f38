import { Skeleton } from "@/components/ui/skeleton"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader } from "@/components/ui/card"

export default function OrderTrackingLoading() {
  return (
    <div className="min-h-screen bg-brand-bg">
      <div className="max-w-6xl mx-auto px-4 py-8">
        <Skeleton className="h-10 w-48 mb-6" />

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Order Status Timeline */}
          <div className="lg:col-span-2">
            <Card className="border-brand-light">
              <CardHeader className="flex flex-row items-center justify-between">
                <Skeleton className="h-6 w-32" />
                <div className="flex items-center gap-2">
                  <Skeleton className="h-6 w-24" />
                  <Skeleton className="h-6 w-16" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {[1, 2, 3, 4, 5].map((i) => (
                    <div key={i} className="flex items-start gap-4">
                      <div className="relative">
                        <Skeleton className="w-10 h-10 rounded-full" />
                        {i < 5 && <Skeleton className="absolute top-10 left-5 w-0.5 h-8" />}
                      </div>

                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between">
                          <Skeleton className="h-5 w-32" />
                          <Skeleton className="h-4 w-24" />
                        </div>
                        <Skeleton className="h-4 w-48 mt-1" />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Order Details Sidebar */}
          <div className="lg:col-span-1">
            <Card className="sticky top-4 border-brand-light">
              <CardHeader>
                <Skeleton className="h-6 w-28" />
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Dress Info */}
                <div className="flex gap-3">
                  <Skeleton className="w-16 h-20 rounded-lg" />
                  <div className="flex-1 min-w-0">
                    <Skeleton className="h-4 w-full mb-1" />
                    <Skeleton className="h-3 w-24 mb-1" />
                    <Skeleton className="h-3 w-20" />
                  </div>
                </div>

                <div className="border-t border-brand-light pt-4">
                  <Skeleton className="h-4 w-28 mb-2" />
                  <div className="space-y-1">
                    <Skeleton className="h-3 w-32" />
                    <Skeleton className="h-3 w-28" />
                    <Skeleton className="h-3 w-24" />
                  </div>
                </div>

                <div className="border-t border-brand-light pt-4">
                  <Skeleton className="h-4 w-24 mb-2" />
                  <div className="space-y-1">
                    <div className="flex justify-between">
                      <Skeleton className="h-4 w-12" />
                      <Skeleton className="h-4 w-20" />
                    </div>
                    <div className="flex justify-between">
                      <Skeleton className="h-4 w-16" />
                      <Skeleton className="h-4 w-24" />
                    </div>
                    <div className="flex justify-between">
                      <Skeleton className="h-4 w-14" />
                      <Skeleton className="h-6 w-16" />
                    </div>
                  </div>
                </div>

                <div className="border-t border-brand-light pt-4">
                  <Skeleton className="h-4 w-32 mb-2" />
                  <div className="space-y-1">
                    <Skeleton className="h-3 w-28" />
                    <Skeleton className="h-3 w-24" />
                    <Skeleton className="h-3 w-20" />
                  </div>
                </div>

                <div className="border-t border-brand-light pt-4">
                  <Skeleton className="h-4 w-28 mb-2" />
                  <div className="space-y-2">
                    {[1, 2, 3].map((i) => (
                      <Skeleton key={i} className="h-8 w-full" />
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
