"use client"

import { useState, useEffect } from "react"
import { useSearchParams } from "next/navigation"
import Link from "next/link"
import { Package, Clock, CheckCircle, Truck, ArrowLeft, Phone, Mail, MessageCircle } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription } from "@/components/ui/alert"

const orderStatuses = [
  {
    id: 1,
    title: "Pesanan Dibuat",
    description: "Pesanan Anda telah berhasil dibuat",
    icon: Package,
    completed: true,
    timestamp: "15 Mar 2024, 14:30",
  },
  {
    id: 2,
    title: "Menunggu Pembayaran",
    description: "Silakan lakukan pembayaran sesuai instruksi",
    icon: Clock,
    completed: true,
    timestamp: "15 Mar 2024, 14:30",
  },
  {
    id: 3,
    title: "Pembayaran Dikonfirmasi",
    description: "Pembayaran Anda telah diverifikasi",
    icon: CheckCircle,
    completed: true,
    timestamp: "15 Mar 2024, 16:45",
  },
  {
    id: 4,
    title: "Gaun Disiapkan",
    description: "Gaun sedang disiapkan untuk pengiriman",
    icon: Package,
    completed: false,
    timestamp: "",
  },
  {
    id: 5,
    title: "Dalam Pengiriman",
    description: "Gaun sedang dalam perjalanan ke alamat Anda",
    icon: Truck,
    completed: false,
    timestamp: "",
  },
]

export default function OrderTrackingPage() {
  const searchParams = useSearchParams()
  const orderId = searchParams.get("orderId") || "GSJ" + Date.now()
  const [currentStatus, setCurrentStatus] = useState(3)

  // Simulate status updates
  useEffect(() => {
    const timer = setTimeout(() => {
      if (currentStatus < 5) {
        setCurrentStatus((prev) => prev + 1)
      }
    }, 5000)

    return () => clearTimeout(timer)
  }, [currentStatus])

  const orderDetails = {
    id: orderId,
    customerName: "Siti Aminah",
    customerPhone: "+62 812-3456-7890",
    customerEmail: "<EMAIL>",
    dress: {
      name: "Gaun Pengantin Syar'i Mewah",
      category: "Baju Pengantin",
      color: "Putih Gading",
      image: "/placeholder.svg?height=200&width=150",
    },
    rental: {
      startDate: "15 Maret 2024",
      endDate: "17 Maret 2024",
      duration: 3,
    },
    payment: {
      total: 750000, // Updated price for wedding dress
      method: "Transfer Bank",
      status: "Lunas",
    },
    delivery: {
      address: "Jl. Malioboro No. 456, Yogyakarta 55271",
      method: "Antar Jemput",
      estimatedDelivery: "16 Maret 2024, 09:00",
    },
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 py-8">
        <Button asChild variant="ghost" className="mb-6">
          <Link href="/catalog">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Kembali ke Katalog
          </Link>
        </Button>

        <div className="grid lg:grid-cols-3 gap-8">
          {/* Order Status Timeline */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Status Pesanan</CardTitle>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-sm">
                    {orderId}
                  </Badge>
                  <Badge variant={orderDetails.payment.status === "Lunas" ? "default" : "secondary"}>
                    {orderDetails.payment.status}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {orderStatuses.map((status, index) => {
                    const Icon = status.icon
                    const isActive = index + 1 <= currentStatus
                    const isCurrent = index + 1 === currentStatus

                    return (
                      <div key={status.id} className="flex items-start gap-4">
                        <div className="relative">
                          <div
                            className={`w-10 h-10 rounded-full flex items-center justify-center ${
                              isActive ? "bg-rose-600 text-white" : "bg-gray-200 text-gray-400"
                            }`}
                          >
                            <Icon className="h-5 w-5" />
                          </div>
                          {index < orderStatuses.length - 1 && (
                            <div
                              className={`absolute top-10 left-5 w-0.5 h-8 ${isActive ? "bg-rose-600" : "bg-gray-200"}`}
                            />
                          )}
                        </div>

                        <div className="flex-1 min-w-0">
                          <div className="flex items-center justify-between">
                            <h3 className={`font-medium ${isActive ? "text-gray-900" : "text-gray-500"}`}>
                              {status.title}
                            </h3>
                            {status.timestamp && <span className="text-sm text-gray-500">{status.timestamp}</span>}
                          </div>
                          <p className={`text-sm mt-1 ${isActive ? "text-gray-600" : "text-gray-400"}`}>
                            {status.description}
                          </p>
                          {isCurrent && (
                            <div className="mt-2">
                              <Badge variant="outline" className="text-xs bg-rose-50 text-rose-700 border-rose-200">
                                Status Saat Ini
                              </Badge>
                            </div>
                          )}
                        </div>
                      </div>
                    )
                  })}
                </div>

                {currentStatus >= 3 && (
                  <Alert className="mt-6">
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription>
                      Pembayaran Anda telah dikonfirmasi! Gaun akan segera disiapkan untuk pengiriman.
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>

            {/* Delivery Information */}
            {currentStatus >= 4 && (
              <Card className="mt-6">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Truck className="h-5 w-5" />
                    Informasi Pengiriman
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Alamat Pengiriman</h4>
                      <p className="text-sm text-gray-600">{orderDetails.delivery.address}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Estimasi Pengiriman</h4>
                      <p className="text-sm text-gray-600">{orderDetails.delivery.estimatedDelivery}</p>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Metode Pengiriman</h4>
                      <p className="text-sm text-gray-600">{orderDetails.delivery.method}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Order Details Sidebar */}
          <div className="lg:col-span-1">
            <Card className="sticky top-4">
              <CardHeader>
                <CardTitle>Detail Pesanan</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Dress Info */}
                <div className="flex gap-3">
                  <div className="w-16 h-20 bg-gray-100 rounded-lg flex-shrink-0">
                    <img
                      src={orderDetails.dress.image || "/placeholder.svg"}
                      alt={orderDetails.dress.name}
                      className="w-full h-full object-cover rounded-lg"
                    />
                  </div>
                  <div className="flex-1 min-w-0">
                    <h4 className="font-medium text-gray-900 text-sm">{orderDetails.dress.name}</h4>
                    <p className="text-xs text-gray-600">Kategori: {orderDetails.dress.category}</p>
                    <p className="text-xs text-gray-600">Warna: {orderDetails.dress.color}</p>
                  </div>
                </div>

                <Separator />

                {/* Rental Period */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-2 text-sm">Periode Sewa</h4>
                  <div className="text-xs text-gray-600 space-y-1">
                    <p>Mulai: {orderDetails.rental.startDate}</p>
                    <p>Selesai: {orderDetails.rental.endDate}</p>
                    <p>Durasi: {orderDetails.rental.duration} hari</p>
                  </div>
                </div>

                <Separator />

                {/* Payment Info */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-2 text-sm">Pembayaran</h4>
                  <div className="text-xs text-gray-600 space-y-1">
                    <div className="flex justify-between">
                      <span>Total:</span>
                      <span className="font-medium">Rp {orderDetails.payment.total.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Metode:</span>
                      <span>{orderDetails.payment.method}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Status:</span>
                      <Badge
                        variant={orderDetails.payment.status === "Lunas" ? "default" : "secondary"}
                        className="text-xs"
                      >
                        {orderDetails.payment.status}
                      </Badge>
                    </div>
                  </div>
                </div>

                <Separator />

                {/* Customer Info */}
                <div>
                  <h4 className="font-medium text-gray-900 mb-2 text-sm">Informasi Penyewa</h4>
                  <div className="text-xs text-gray-600 space-y-1">
                    <p>{orderDetails.customerName}</p>
                    <p>{orderDetails.customerPhone}</p>
                    <p>{orderDetails.customerEmail}</p>
                  </div>
                </div>

                <Separator />

                {/* Contact Support */}
                <div className="space-y-2">
                  <h4 className="font-medium text-gray-900 text-sm">Butuh Bantuan?</h4>
                  <div className="space-y-2">
                    <Button asChild variant="outline" size="sm" className="w-full justify-start text-xs bg-transparent">
                      <Link href="https://wa.me/6281234567890" className="flex items-center gap-2">
                        <MessageCircle className="h-3 w-3" />
                        WhatsApp
                      </Link>
                    </Button>
                    <Button asChild variant="outline" size="sm" className="w-full justify-start text-xs bg-transparent">
                      <Link href="tel:+6281234567890" className="flex items-center gap-2">
                        <Phone className="h-3 w-3" />
                        Telepon
                      </Link>
                    </Button>
                    <Button asChild variant="outline" size="sm" className="w-full justify-start text-xs bg-transparent">
                      <Link href="mailto:<EMAIL>" className="flex items-center gap-2">
                        <Mail className="h-3 w-3" />
                        Email
                      </Link>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
