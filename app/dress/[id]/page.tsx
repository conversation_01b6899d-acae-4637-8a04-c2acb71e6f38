"use client"

import { useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { useParams } from "next/navigation"
import { Star, Heart, ArrowLeft, Info, Shield, Truck } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Calendar as CalendarComponent } from "@/components/ui/calendar"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

const dressData = {
  1: {
    id: 1,
    name: "Gaun Pengantin Syar'i Mewah",
    price: 300000,
    dp: 150000,
    images: [
      "/placeholder.svg?height=600&width=500",
      "/placeholder.svg?height=600&width=500",
      "/placeholder.svg?height=600&width=500",
      "/placeholder.svg?height=600&width=500",
    ],
    rating: 4.9,
    reviewCount: 45,
    category: "Baju Pengantin",
    color: "Putih Gading",
    motif: "Bordir Emas",
    description:
      "Gaun pengantin syar'i yang mewah dengan detail bordir emas yang memukau. Dibuat dari bahan satin premium dengan lapisan tulle untuk memberikan volume yang sempurna. Dilengkapi dengan hijab pengantin matching dan aksesoris pelengkap. Tersedia dalam ukuran S (Lingkar Dada: 86-90cm), M (Lingkar Dada: 90-94cm), L (Lingkar Dada: 94-98cm), XL (Lingkar Dada: 98-102cm). Cocok untuk akad nikah dan resepsi pernikahan yang sakral dan berkesan.",
    features: [
      "Bahan satin premium dengan lapisan tulle",
      "Detail bordir emas 24 karat yang mewah",
      "Potongan A-line yang flattering untuk semua bentuk tubuh",
      "Lengan panjang dengan manset mutiara",
      "Dilengkapi dengan hijab pengantin dan aksesoris",
      "Tersedia ukuran S, M, L, XL dengan panduan lengkap",
    ],
    unavailableDates: [
      new Date(2024, 2, 15),
      new Date(2024, 2, 16),
      new Date(2024, 2, 22),
      new Date(2024, 2, 23),
      new Date(2024, 3, 5),
      new Date(2024, 3, 6),
    ],
  },
  2: {
    id: 2,
    name: "Gaun Ibu Pengantin Elegant",
    price: 200000,
    dp: 100000,
    images: [
      "/placeholder.svg?height=600&width=500",
      "/placeholder.svg?height=600&width=500",
      "/placeholder.svg?height=600&width=500",
      "/placeholder.svg?height=600&width=500",
    ],
    rating: 4.8,
    reviewCount: 32,
    category: "Baju Orang Tua (Ibu)",
    color: "Navy Blue",
    motif: "Lace Premium",
    description:
      "Gaun untuk ibu pengantin yang elegan dan anggun dengan detail lace premium. Dibuat dari bahan crepe berkualitas tinggi yang nyaman digunakan seharian. Desain yang sophisticated cocok untuk ibu pengantin di acara akad maupun resepsi. Tersedia dalam ukuran S (Lingkar Dada: 88-92cm), M (Lingkar Dada: 92-96cm), L (Lingkar Dada: 96-100cm), XL (Lingkar Dada: 100-104cm), XXL (Lingkar Dada: 104-108cm). Dilengkapi dengan hijab matching.",
    features: [
      "Bahan crepe premium yang nyaman dan tidak mudah kusut",
      "Detail lace premium di bagian dada dan lengan",
      "Potongan yang menutupi dengan sempurna",
      "Warna navy yang elegan dan tidak mudah kotor",
      "Dilengkapi dengan hijab matching",
      "Tersedia ukuran S hingga XXL",
    ],
    unavailableDates: [new Date(2024, 2, 20), new Date(2024, 2, 21), new Date(2024, 3, 10), new Date(2024, 3, 11)],
  },
  3: {
    id: 3,
    name: "Baju Koko Ayah Premium",
    price: 175000,
    dp: 87500,
    images: [
      "/placeholder.svg?height=600&width=500",
      "/placeholder.svg?height=600&width=500",
      "/placeholder.svg?height=600&width=500",
      "/placeholder.svg?height=600&width=500",
    ],
    rating: 4.7,
    reviewCount: 28,
    category: "Baju Ayah",
    color: "Hitam",
    motif: "Polos Elegan",
    description:
      "Baju koko premium untuk ayah pengantin dengan desain yang elegan dan berkelas. Dibuat dari bahan katun premium yang nyaman dan breathable. Cocok untuk acara formal seperti akad nikah, resepsi, dan acara keluarga lainnya. Tersedia dalam ukuran M (Lingkar Dada: 96-100cm), L (Lingkar Dada: 100-104cm), XL (Lingkar Dada: 104-108cm), XXL (Lingkar Dada: 108-112cm). Dilengkapi dengan peci matching.",
    features: [
      "Bahan katun premium yang nyaman dan tidak gerah",
      "Desain polos elegan yang timeless",
      "Kancing premium dengan finishing berkualitas",
      "Potongan regular fit yang nyaman",
      "Dilengkapi dengan peci matching",
      "Tersedia ukuran M hingga XXL",
    ],
    unavailableDates: [new Date(2024, 2, 18), new Date(2024, 2, 19), new Date(2024, 3, 8), new Date(2024, 3, 9)],
  },
}

export default function DressDetailPage() {
  const params = useParams()
  const dressId = Number.parseInt(params.id as string)
  const dress = dressData[dressId as keyof typeof dressData]

  const [selectedImage, setSelectedImage] = useState(0)
  const [selectedDates, setSelectedDates] = useState<{ from?: Date; to?: Date }>({})
  const [rentalDays, setRentalDays] = useState(1)

  if (!dress) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Gaun tidak ditemukan</h1>
          <Button asChild>
            <Link href="/catalog">Kembali ke Katalog</Link>
          </Button>
        </div>
      </div>
    )
  }

  const calculateTotal = () => {
    return dress.price * rentalDays
  }

  const isDateUnavailable = (date: Date) => {
    return dress.unavailableDates.some((unavailableDate) => date.toDateString() === unavailableDate.toDateString())
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Breadcrumb */}
      <div className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Link href="/" className="hover:text-rose-600">
              Beranda
            </Link>
            <span>/</span>
            <Link href="/catalog" className="hover:text-rose-600">
              Katalog
            </Link>
            <span>/</span>
            <span className="text-gray-900">{dress.name}</span>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-8">
        <Button asChild variant="ghost" className="mb-6">
          <Link href="/catalog">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Kembali ke Katalog
          </Link>
        </Button>

        <div className="grid lg:grid-cols-2 gap-12">
          {/* Image Gallery */}
          <div className="space-y-4">
            <div className="relative aspect-[4/5] bg-white rounded-lg overflow-hidden">
              <Image
                src={dress.images[selectedImage] || "/placeholder.svg"}
                alt={dress.name}
                fill
                className="object-cover"
              />
              <Button variant="ghost" size="icon" className="absolute top-4 right-4 bg-white/80 hover:bg-white">
                <Heart className="h-5 w-5" />
              </Button>
            </div>

            <div className="grid grid-cols-4 gap-2">
              {dress.images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImage(index)}
                  className={`relative aspect-square rounded-lg overflow-hidden border-2 transition-colors ${
                    selectedImage === index ? "border-rose-600" : "border-gray-200"
                  }`}
                >
                  <Image
                    src={image || "/placeholder.svg"}
                    alt={`${dress.name} ${index + 1}`}
                    fill
                    className="object-cover"
                  />
                </button>
              ))}
            </div>
          </div>

          {/* Product Info */}
          <div className="space-y-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">{dress.name}</h1>
              <div className="flex items-center gap-4 mb-4">
                <div className="flex items-center gap-1">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`h-5 w-5 ${
                        i < Math.floor(dress.rating) ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
                      }`}
                    />
                  ))}
                  <span className="text-sm text-gray-600 ml-2">
                    ({dress.rating}) • {dress.reviewCount} ulasan
                  </span>
                </div>
              </div>

              <div className="flex items-center gap-2 mb-4">
                <Badge variant="outline">{dress.category}</Badge>
                <Badge variant="outline">{dress.color}</Badge>
                <Badge variant="outline">{dress.motif}</Badge>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg">
              <div className="text-3xl font-bold text-rose-600 mb-2">
                Rp {dress.price.toLocaleString()}
                <span className="text-lg text-gray-500 font-normal">/hari</span>
              </div>
              <p className="text-gray-600 mb-4">DP: Rp {dress.dp.toLocaleString()}</p>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Durasi Sewa</label>
                  <Select
                    value={rentalDays.toString()}
                    onValueChange={(value) => setRentalDays(Number.parseInt(value))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {[1, 2, 3, 4, 5].map((days) => (
                        <SelectItem key={days} value={days.toString()}>
                          {days} hari
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Pilih Tanggal Sewa</label>
                  <div className="border rounded-lg p-4">
                    <CalendarComponent
                      mode="range"
                      selected={selectedDates}
                      onSelect={setSelectedDates}
                      disabled={isDateUnavailable}
                      className="rounded-md"
                    />
                  </div>
                  <div className="mt-2 text-sm text-gray-600">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-green-500 rounded"></div>
                        <span>Tersedia</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 bg-red-500 rounded"></div>
                        <span>Tidak tersedia</span>
                      </div>
                    </div>
                  </div>
                </div>

                {selectedDates.from && selectedDates.to && (
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h4 className="font-medium text-gray-900 mb-2">Ringkasan Biaya</h4>
                    <div className="space-y-1 text-sm">
                      <div className="flex justify-between">
                        <span>Harga sewa ({rentalDays} hari)</span>
                        <span>Rp {calculateTotal().toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>DP (Uang Muka)</span>
                        <span>Rp {dress.dp.toLocaleString()}</span>
                      </div>
                      <div className="border-t pt-1 flex justify-between font-medium">
                        <span>Total Pembayaran Awal</span>
                        <span>Rp {(calculateTotal() + dress.dp).toLocaleString()}</span>
                      </div>
                    </div>
                  </div>
                )}

                <Button
                  asChild
                  className="w-full bg-rose-600 hover:bg-rose-700 text-lg py-6"
                  disabled={!selectedDates.from || !selectedDates.to}
                >
                  <Link
                    href={`/booking/${dress.id}?days=${rentalDays}&from=${selectedDates.from?.toISOString()}&to=${selectedDates.to?.toISOString()}`}
                  >
                    Pesan Sekarang
                  </Link>
                </Button>
              </div>
            </div>

            {/* Additional Info */}
            <div className="grid grid-cols-3 gap-4">
              <div className="text-center p-4 bg-white rounded-lg">
                <Shield className="h-8 w-8 text-rose-600 mx-auto mb-2" />
                <p className="text-sm font-medium">Garansi Kualitas</p>
              </div>
              <div className="text-center p-4 bg-white rounded-lg">
                <Truck className="h-8 w-8 text-rose-600 mx-auto mb-2" />
                <p className="text-sm font-medium">Antar Jemput</p>
              </div>
              <div className="text-center p-4 bg-white rounded-lg">
                <Info className="h-8 w-8 text-rose-600 mx-auto mb-2" />
                <p className="text-sm font-medium">Konsultasi Gratis</p>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="mt-12">
          <Tabs defaultValue="description" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="description">Deskripsi</TabsTrigger>
              <TabsTrigger value="size-guide">Panduan Ukuran</TabsTrigger>
              <TabsTrigger value="reviews">Ulasan</TabsTrigger>
            </TabsList>

            <TabsContent value="description" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Deskripsi Produk</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-600 mb-4">{dress.description}</p>
                  <h4 className="font-medium text-gray-900 mb-2">Fitur Unggulan:</h4>
                  <ul className="list-disc list-inside space-y-1 text-gray-600">
                    {dress.features.map((feature, index) => (
                      <li key={index}>{feature}</li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="size-guide" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Panduan Ukuran</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="overflow-x-auto">
                    <table className="w-full border-collapse border border-gray-300">
                      <thead>
                        <tr className="bg-gray-50">
                          <th className="border border-gray-300 px-4 py-2">Ukuran</th>
                          <th className="border border-gray-300 px-4 py-2">Lingkar Dada (cm)</th>
                          <th className="border border-gray-300 px-4 py-2">Lingkar Pinggang (cm)</th>
                          <th className="border border-gray-300 px-4 py-2">Panjang Gaun (cm)</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr>
                          <td className="border border-gray-300 px-4 py-2 font-medium">S</td>
                          <td className="border border-gray-300 px-4 py-2">86-90</td>
                          <td className="border border-gray-300 px-4 py-2">66-70</td>
                          <td className="border border-gray-300 px-4 py-2">140</td>
                        </tr>
                        <tr>
                          <td className="border border-gray-300 px-4 py-2 font-medium">M</td>
                          <td className="border border-gray-300 px-4 py-2">90-94</td>
                          <td className="border border-gray-300 px-4 py-2">70-74</td>
                          <td className="border border-gray-300 px-4 py-2">142</td>
                        </tr>
                        <tr>
                          <td className="border border-gray-300 px-4 py-2 font-medium">L</td>
                          <td className="border border-gray-300 px-4 py-2">94-98</td>
                          <td className="border border-gray-300 px-4 py-2">74-78</td>
                          <td className="border border-gray-300 px-4 py-2">144</td>
                        </tr>
                        <tr>
                          <td className="border border-gray-300 px-4 py-2 font-medium">XL</td>
                          <td className="border border-gray-300 px-4 py-2">98-102</td>
                          <td className="border border-gray-300 px-4 py-2">78-82</td>
                          <td className="border border-gray-300 px-4 py-2">146</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  <p className="text-sm text-gray-600 mt-4">
                    * Ukuran dapat bervariasi ±2cm. Untuk konsultasi ukuran yang lebih akurat, silakan hubungi customer
                    service kami.
                  </p>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="reviews" className="mt-6">
              <Card>
                <CardHeader>
                  <CardTitle>Ulasan Pelanggan</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-6">
                    {[
                      {
                        name: "Siti Aminah",
                        rating: 5,
                        date: "15 Februari 2024",
                        comment:
                          "Gaun sangat cantik dan berkualitas. Sesuai dengan foto dan deskripsi. Pelayanan juga sangat baik!",
                      },
                      {
                        name: "Fatimah Zahra",
                        rating: 5,
                        date: "10 Februari 2024",
                        comment:
                          "Alhamdulillah gaun untuk akad nikah saya sangat sempurna. Terima kasih GaunSyariJogja!",
                      },
                      {
                        name: "Khadijah Rahman",
                        rating: 4,
                        date: "5 Februari 2024",
                        comment:
                          "Gaun bagus, hanya saja pengiriman agak terlambat. Tapi overall puas dengan kualitasnya.",
                      },
                    ].map((review, index) => (
                      <div key={index} className="border-b pb-4 last:border-b-0">
                        <div className="flex items-center justify-between mb-2">
                          <h5 className="font-medium text-gray-900">{review.name}</h5>
                          <span className="text-sm text-gray-500">{review.date}</span>
                        </div>
                        <div className="flex items-center mb-2">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              className={`h-4 w-4 ${
                                i < review.rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
                              }`}
                            />
                          ))}
                        </div>
                        <p className="text-gray-600">{review.comment}</p>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}
