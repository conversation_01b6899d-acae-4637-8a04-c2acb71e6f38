@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: #e4e0e1;
    --foreground: #493628;
    --card: #ffffff;
    --card-foreground: #493628;
    --popover: #ffffff;
    --popover-foreground: #493628;
    --primary: #493628;
    --primary-foreground: #e4e0e1;
    --secondary: #ab886d;
    --secondary-foreground: #493628;
    --muted: #d6c0b3;
    --muted-foreground: #493628;
    --accent: #d6c0b3;
    --accent-foreground: #493628;
    --destructive: #dc2626;
    --destructive-foreground: #fef2f2;
    --border: #d6c0b3;
    --input: #ffffff;
    --ring: #ab886d;
    --radius: 0.5rem;

    /* Sidebar colors */
    --sidebar-background: #ffffff;
    --sidebar-foreground: #493628;
    --sidebar-primary: #493628;
    --sidebar-primary-foreground: #e4e0e1;
    --sidebar-accent: #d6c0b3;
    --sidebar-accent-foreground: #493628;
    --sidebar-border: #d6c0b3;
    --sidebar-ring: #ab886d;
  }

  .dark {
    --background: #493628;
    --foreground: #e4e0e1;
    --card: #493628;
    --card-foreground: #e4e0e1;
    --popover: #493628;
    --popover-foreground: #e4e0e1;
    --primary: #e4e0e1;
    --primary-foreground: #493628;
    --secondary: #ab886d;
    --secondary-foreground: #e4e0e1;
    --muted: #ab886d;
    --muted-foreground: #e4e0e1;
    --accent: #ab886d;
    --accent-foreground: #e4e0e1;
    --destructive: #dc2626;
    --destructive-foreground: #fef2f2;
    --border: #ab886d;
    --input: #493628;
    --ring: #ab886d;

    /* Sidebar colors for dark mode */
    --sidebar-background: #493628;
    --sidebar-foreground: #e4e0e1;
    --sidebar-primary: #e4e0e1;
    --sidebar-primary-foreground: #493628;
    --sidebar-accent: #ab886d;
    --sidebar-accent-foreground: #e4e0e1;
    --sidebar-border: #ab886d;
    --sidebar-ring: #ab886d;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #d6c0b3;
}

::-webkit-scrollbar-thumb {
  background: #ab886d;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #493628;
}

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.3s ease-out;
}

/* Brand gradient */
.brand-gradient {
  background: linear-gradient(135deg, #493628 0%, #ab886d 50%, #d6c0b3 100%);
}

/* Card shadows with brand colors */
.card-shadow {
  box-shadow: 0 4px 6px -1px rgba(73, 54, 40, 0.1), 0 2px 4px -1px rgba(73, 54, 40, 0.06);
}

.card-shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(73, 54, 40, 0.1), 0 4px 6px -2px rgba(73, 54, 40, 0.05);
}
